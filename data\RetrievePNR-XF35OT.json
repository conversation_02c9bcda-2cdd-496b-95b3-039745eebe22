{"seriesNum": "299", "PNR": "XF35OT", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82372368", "bookDate": "2025-04-28T11:19:55", "modifyDate": "2025-05-16T11:12:48", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "3f592f97499ixamau614d064t9m8fc83af4726c059fe", "securityGUID": "3f592f97499ixamau614d064t9m8fc83af4726c059fe", "lastLoadGUID": "016259ab-aaa5-48ba-8127-f5b8f31aa080", "isAsyncPNR": false, "MasterPNR": "XF35OT", "segments": [{"segKey": "16066156:16066156:5/17/2025 9:35:00 AM", "LFID": 16066156, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16066156", "org": "DXB", "dest": "DOH", "depTime": "2025-05-17T09:35:00", "depTimeGMT": "2025-05-17T05:35:00", "arrTime": "2025-05-17T09:55:00", "operCarrier": "FZ", "operFlightNum": "003", "mrktCarrier": "FZ ", "mrktFlightNum": "003", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 180997, "depDate": "2025-05-17T09:35:00", "legKey": "16066156:180997:5/17/2025 9:35:00 AM", "customerKey": "F76B9D0ED2D6F9D90B2621F523672BF9A66B049D347D07CAD7D298EACDD40750"}], "active": true}, {"segKey": "16087271:16087271:5/15/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-05-15T17:05:00", "depTimeGMT": "2025-05-15T14:05:00", "arrTime": "2025-05-15T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ ", "mrktFlightNum": "018", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-15T17:05:00", "legKey": "16087271:181022:5/15/2025 5:05:00 PM", "customerKey": "7B8440B666CC584FD1D10F12C7B34E219113DE400F6743D5407A1DE9A13D6AAC"}], "active": true, "changeType": "TK"}, {"segKey": "16066159:16066159:5/16/2025 7:40:00 PM", "LFID": 16066159, "depDate": "2025-05-16T00:00:00", "flightGroupId": "16066159", "org": "DXB", "dest": "DOH", "depTime": "2025-05-16T19:40:00", "depTimeGMT": "2025-05-16T15:40:00", "arrTime": "2025-05-16T19:50:00", "operCarrier": "FZ", "operFlightNum": "005", "mrktCarrier": "FZ ", "mrktFlightNum": "005", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181000, "depDate": "2025-05-16T19:40:00", "legKey": "16066159:181000:5/16/2025 7:40:00 PM", "customerKey": "C035EBCDE5770DC98886648F4DCA4394D97B13720D569C92FBC33DFD73253E30"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 267224143, "fName": "NICOLA", "lName": "DELLA PORTA", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1971-08-24T00:00:00", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "4/28/2025 11:18:27 AM", "provider": "AIG", "status": 5, "fareClass": "M", "operFareClass": "M", "FBC": "MR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "emergencyContactID": 268860877, "discloseEmergencyContact": 1, "insuConfNum": "986937227", "insuTransID": "d3bbcbbe-77eb-45cf-a9e4-54a60699dce9", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682570c60007780000003a73#267224143#1#MOBILE#SFQE#CHANGE", "fareTypeID": 21, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-28T11:19:55"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "4/28/2025 11:18:27 AM", "provider": "AIG", "status": 0, "fareClass": "Q", "operFareClass": "Q", "FBC": "QR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "insuConfNum": "986937227", "insuTransID": "d3bbcbbe-77eb-45cf-a9e4-54a60699dce9", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680f63b7000777000000186b#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-28T11:19:55"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/15/2025 4:44:51 AM", "provider": "<PERSON>", "status": 5, "fareClass": "Q", "operFareClass": "Q", "FBC": "QR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "R9P6L-CX3ND-INS", "insuTransID": "R9P6L-CX3ND-INS/b98f3c4f-0882-4d73-b2a6-2a419d958141", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682570c60007780000003a73#267224143#2#MOBILE#SFQE#CHANGE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-15T04:44:50"}]}], "payments": [{"paymentID": *********, "paxID": 269019523, "method": "VISA", "status": "1", "paidDate": "2025-05-15T04:45:57", "cardNum": "************3290", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 417.56, "baseCurr": "QAR", "baseAmt": 417.56, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "028460", "reference": "23040955", "externalReference": "23040955", "tranId": "21415961", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21415961}, {"paymentID": 207215342, "paxID": 267224264, "method": "VISA", "status": "1", "paidDate": "2025-04-28T11:20:26", "cardNum": "************3290", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 29.61, "baseCurr": "QAR", "baseAmt": 29.61, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "066159", "reference": "22703007", "externalReference": "22703007", "tranId": "21076456", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21076456}, {"paymentID": 209251983, "paxID": 269182494, "method": "VISA", "status": "1", "paidDate": "2025-05-16T11:12:42", "cardNum": "************3290", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 72.1, "baseCurr": "QAR", "baseAmt": 72.1, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON>", "authCode": "057848", "reference": "23068652", "externalReference": "23068652", "tranId": "21445235", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21445235}, {"paymentID": *********, "paxID": 267346285, "method": "VISA", "status": "1", "paidDate": "2025-04-29T11:23:08", "cardNum": "************3290", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1803.53, "baseCurr": "QAR", "baseAmt": 1803.53, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "092949", "reference": "22722089", "externalReference": "22722089", "tranId": "21100373", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21100373}, {"paymentID": 207338144, "paxID": 267346257, "method": "VISA", "status": "2", "paidDate": "2025-04-29T11:19:27", "cardNum": "************3290", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1803.53, "baseCurr": "QAR", "baseAmt": 1803.53, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "reference": "22722044", "externalReference": "22722044", "tranId": "21100373", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21100373}], "OAFlights": null, "physicalFlights": [{"key": "16087271:181022:2025-05-15T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-15T17:05:00", "depTime": "2025-05-15T17:05:00", "arrTime": "2025-05-15T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "3/10/2025 10:39:22 AM"}, {"key": "16066159:181000:2025-05-16T07:40:00 PM", "LFID": 16066159, "PFID": 181000, "org": "DXB", "dest": "DOH", "depDate": "2025-05-16T19:40:00", "depTime": "2025-05-16T19:40:00", "arrTime": "2025-05-16T19:50:00", "carrier": "FZ", "flightNum": "005", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "005", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "TK", "flightChangeTime": "11/29/2024 4:28:29 AM"}, {"key": "16066156:180997:2025-05-17T09:35:00 AM", "LFID": 16066156, "PFID": 180997, "org": "DXB", "dest": "DOH", "depDate": "2025-05-17T09:35:00", "depTime": "2025-05-17T09:35:00", "arrTime": "2025-05-17T09:55:00", "carrier": "FZ", "flightNum": "003", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "003", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-28T11:19:55", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-28T11:19:54"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1323587896, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587896:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1323587895, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587895:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1323587893, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587893:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1323587894, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587894:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1010, "curr": "QAR", "originalAmt": 1010, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-28T11:19:55", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1010, "approveCode": 0}]}, {"chargeID": 1346820065, "codeType": "PMNT", "amt": 12.16, "curr": "QAR", "originalAmt": 12.16, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-15T04:46:01", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346820065:*********", "paymentID": *********, "amt": 12.16, "approveCode": 0}]}, {"chargeID": 1325152593, "codeType": "PMNT", "amt": 52.53, "curr": "QAR", "originalAmt": 52.53, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-29T11:23:20", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325152593:*********", "paymentID": *********, "amt": 52.53, "approveCode": 0}]}, {"chargeID": 1323593512, "codeType": "PMNT", "amt": 0.86, "curr": "QAR", "originalAmt": 0.86, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-28T11:20:31", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323593512:207215342", "paymentID": 207215342, "amt": 0.86, "approveCode": 0}]}, {"chargeID": 1323593513, "codeType": "BHFT", "amt": 28.75, "curr": "QAR", "originalAmt": 28.75, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-28T11:20:31", "desc": "Hold Book Charge", "comment": "TLTRULE:102", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323593513:207215342", "paymentID": 207215342, "amt": 28.75, "approveCode": 0}], "parameter1Name": "TLT_EXTENSION_HR", "parameter1Value": "24", "parameter2Name": "TLT_EXTENSION_FEE", "parameter2Value": "28.75"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-28T11:19:55", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-28T11:19:54"}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1323587906, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1346818093, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1323587907, "paymentMap": [{"key": "1346818093:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1346818095, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1323587905, "paymentMap": [{"key": "1346818095:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1346818094, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": -80, "curr": "QAR", "originalAmt": -80, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1323587904, "paymentMap": [{"key": "1346818094:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1346818096, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1323587903, "paymentMap": [{"key": "1346818096:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1323587906, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587906:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1323587907, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587907:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1323587904, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587904:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1323587905, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587905:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1323587903, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-28T11:19:55", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1323587903:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1346818092, "codeType": "AIR", "amt": -305, "curr": "QAR", "originalAmt": -305, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-15T04:44:51", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1346818092:*********", "paymentID": *********, "amt": -305, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 305, "curr": "QAR", "originalAmt": 305, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-28T11:19:55", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 305, "approveCode": 0}]}, {"chargeID": 1346818097, "codeType": "PNLT", "amt": 345, "curr": "QAR", "originalAmt": 345, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-15T04:44:51", "desc": "Penalty AddedDueToModify FZ  005 DXB  - DOH  16-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346818097:*********", "paymentID": *********, "amt": 345, "approveCode": 0}]}]}, {"recNum": 3, "charges": [{"chargeID": 1346818109, "codeType": "INSU", "amt": 35.4, "curr": "QAR", "originalAmt": 35.4, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-15T04:44:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346818109:*********", "paymentID": *********, "amt": 35.4, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-15T04:44:51"}, {"chargeID": 1346817981, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346817978, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817981:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1346817982, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346817978, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817982:*********", "paymentID": *********, "amt": 40, "approveCode": 0}, {"key": "1346817982:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1346817984, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346817978, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817984:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1346817979, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346817978, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817979:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346817983, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346817978, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817983:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346817980, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346817978, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T04:44:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817980:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346817978, "codeType": "AIR", "amt": 330, "curr": "QAR", "originalAmt": 330, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-15T04:44:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346817978:*********", "paymentID": *********, "amt": 330, "approveCode": 0}]}, {"chargeID": 1348933972, "codeType": "PMNT", "amt": 2.1, "curr": "QAR", "originalAmt": 2.1, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-16T11:12:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348933972:209251983", "paymentID": 209251983, "amt": 2.1, "approveCode": 0}]}, {"chargeID": 1348929835, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-16T11:10:16", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348929835:209251983", "paymentID": 209251983, "amt": 70, "approveCode": 0}], "PFID": "180997", "ssrCommentId": "136022844"}]}], "parentPNRs": [], "childPNRs": []}