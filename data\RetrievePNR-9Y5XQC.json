{"seriesNum": "299", "PNR": "9Y5XQC", "bookAgent": "swapnil.admin", "IATA": "96008962", "resCurrency": "AED", "PNRPin": "82644440", "bookDate": "2025-05-08T07:05:00", "modifyDate": "2025-05-14T09:12:28", "resType": "TA", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "9f35ca6h632bif6413l54f69bat6k43cu8f6599a3989", "securityGUID": "9f35ca6h632bif6413l54f69bat6k43cu8f6599a3989", "lastLoadGUID": "10e3bdbf-acec-44bb-a291-b561c36dc37b", "isAsyncPNR": false, "MasterPNR": "9Y5XQC", "segments": [{"segKey": "17093569:17093569:5/19/2025 12:40:00 AM", "LFID": 17093569, "depDate": "2025-05-19T00:00:00", "flightGroupId": "17093569", "org": "DXB", "dest": "ISB", "depTime": "2025-05-19T00:40:00", "depTimeGMT": "2025-05-18T20:40:00", "arrTime": "2025-05-19T04:50:00", "operCarrier": "FZ", "operFlightNum": "353", "mrktCarrier": "FZ", "mrktFlightNum": "353", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 187806, "depDate": "2025-05-19T00:40:00", "legKey": "17093569:187806:5/19/2025 12:40:00 AM", "customerKey": "9E40AF774DE7B3AD11C0B68CD8A1B2B94046CE065733B7809E0F468D50F771BC"}], "active": true}, {"segKey": "17093569:17093569:5/15/2025 12:40:00 AM", "LFID": 17093569, "depDate": "2025-05-15T00:00:00", "flightGroupId": "17093569", "org": "DXB", "dest": "ISB", "depTime": "2025-05-15T00:40:00", "depTimeGMT": "2025-05-14T20:40:00", "arrTime": "2025-05-15T04:50:00", "operCarrier": "FZ", "operFlightNum": "353", "mrktCarrier": "FZ ", "mrktFlightNum": "353", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 187806, "depDate": "2025-05-15T00:40:00", "legKey": "17093569:187806:5/15/2025 12:40:00 AM", "customerKey": "7A26B50C02EFA04FB471F510DC04AC2D660FAEBBCB0F892912836D60761F1AAA"}], "active": true}, {"segKey": "17093569:17093569:5/12/2025 12:40:00 AM", "LFID": 17093569, "depDate": "2025-05-12T00:00:00", "flightGroupId": "17093569", "org": "DXB", "dest": "ISB", "depTime": "2025-05-12T00:40:00", "depTimeGMT": "2025-05-11T20:40:00", "arrTime": "2025-05-12T04:50:00", "operCarrier": "FZ", "operFlightNum": "353", "mrktCarrier": "FZ ", "mrktFlightNum": "353", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 187806, "depDate": "2025-05-12T00:40:00", "legKey": "17093569:187806:5/12/2025 12:40:00 AM", "customerKey": "0E023AA9C2693B08FB1BCF3F4F2B1E54D1ECDD187CF8006EEF28DC3E47298E04"}], "active": true}], "persons": [{"paxID": 268270124, "fName": "MOBEEN", "lName": "AKRAM", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "swapnil.admin", "cancelAgent": "swapnil.admin", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "V", "status": 0, "fareClass": "V", "operFareClass": "V", "FBC": "VOX7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681c570700077800000015d7#1#1#TA#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 16, "cancelReasonID": 39, "bookDate": "2025-05-08T07:05:00"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "swapnil.admin", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "V", "insuPurchasedate": "5/11/2025 9:46:18 AM", "provider": "<PERSON>", "status": 0, "fareClass": "V", "operFareClass": "V", "FBC": "VOX7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 2, "insuConfNum": "MTSAV-69ECK-INS", "insuTransID": "MTSAV-69ECK-INS/ac03dede-de0d-4782-9c4d-042c87a940f1", "toRecNum": 3, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f43b400077800000097d1#268270124#1#TA#SFQE#CHANGE", "fareTypeID": 12, "channelID": 16, "cancelReasonID": 1, "bookDate": "2025-05-10T12:17:33"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "iman.al<PERSON>i", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/11/2025 9:46:18 AM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "ROL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "MTSAV-69ECK-INS/ac03dede-de0d-4782-9c4d-042c87a940f1", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68244ec500077700000012b7#268270124#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-14T08:11:24"}]}], "payments": [{"paymentID": *********, "paxID": 233137345, "method": "INVC", "status": "1", "paidDate": "2025-05-08T07:08:59", "IATANum": "96008962", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 440, "baseCurr": "AED", "baseAmt": 440, "userID": "swapnil.admin", "channelID": 16, "authCode": "110658223", "reference": "A5328839", "externalReference": "A5328839", "tranId": "21280040", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21280040}, {"paymentID": *********, "paxID": 268928251, "method": "VISA", "status": "1", "paidDate": "2025-05-14T08:52:43", "cardNum": "************3738", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1134.03, "baseCurr": "AED", "baseAmt": 1134.03, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "350871", "reference": "23026650", "externalReference": "23026650", "tranId": "21399713", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21399713}, {"paymentID": *********, "paxID": 268583776, "method": "VISA", "status": "1", "paidDate": "2025-05-11T09:46:43", "cardNum": "************5127", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 93.42, "baseCurr": "AED", "baseAmt": 93.42, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "110795", "reference": "22968523", "externalReference": "22968523", "tranId": "21337382", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21337382}], "OAFlights": null, "physicalFlights": [{"key": "17093569:187806:2025-05-12T12:40:00 AM", "LFID": 17093569, "PFID": 187806, "org": "DXB", "dest": "ISB", "depDate": "2025-05-12T00:40:00", "depTime": "2025-05-12T00:40:00", "arrTime": "2025-05-12T04:50:00", "carrier": "FZ", "flightNum": "353", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "353", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ISB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Islamabad", "isActive": false}, {"key": "17093569:187806:2025-05-15T12:40:00 AM", "LFID": 17093569, "PFID": 187806, "org": "DXB", "dest": "ISB", "depDate": "2025-05-15T00:40:00", "depTime": "2025-05-15T00:40:00", "arrTime": "2025-05-15T04:50:00", "carrier": "FZ", "flightNum": "353", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "353", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ISB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Islamabad", "isActive": false}, {"key": "17093569:187806:2025-05-19T12:40:00 AM", "LFID": 17093569, "PFID": 187806, "org": "DXB", "dest": "ISB", "depDate": "2025-05-19T00:40:00", "depTime": "2025-05-19T00:40:00", "arrTime": "2025-05-19T04:50:00", "carrier": "FZ", "flightNum": "353", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "353", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ISB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Islamabad", "isActive": false}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1341042543, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T09:46:18", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341042543:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-11T09:46:18"}, {"chargeID": 1345511565, "codeType": "INSU", "taxChargeID": 1345511556, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-14T08:11:25", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341042543, "paymentMap": [{"key": "1345511565:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}"}, {"chargeID": 1340023958, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340023957, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340023958:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1340023962, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340023957, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340023962:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1340023959, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1340023957, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340023959:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340023961, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1340023957, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340023961:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1340023960, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340023957, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340023960:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345511558, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1345511556, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023959, "paymentMap": [{"key": "1345511558:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345511559, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345511556, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023962, "paymentMap": [{"key": "1345511559:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1345511560, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1345511556, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023958, "paymentMap": [{"key": "1345511560:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1345511563, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345511556, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023960, "paymentMap": [{"key": "1345511563:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345511564, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1345511556, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023961, "paymentMap": [{"key": "1345511564:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1340023957, "codeType": "AIR", "amt": 210, "curr": "AED", "originalAmt": 210, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:17:33", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340023957:*********", "paymentID": *********, "amt": 210, "approveCode": 0}]}, {"chargeID": 1345511556, "codeType": "AIR", "amt": -210, "curr": "AED", "originalAmt": -210, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T08:11:24", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023957, "paymentMap": [{"key": "1345511556:*********", "paymentID": *********, "amt": -210, "approveCode": 0}]}, {"chargeID": 1341047596, "codeType": "PMNT", "amt": 2.72, "curr": "AED", "originalAmt": 2.72, "originalCurr": "AED", "status": 0, "billDate": "2025-05-11T09:46:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341047596:*********", "paymentID": *********, "amt": 2.72, "approveCode": 0}]}, {"chargeID": 1341042475, "codeType": "FRST", "amt": 55, "curr": "AED", "originalAmt": 55, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T09:46:18", "desc": "FRST", "comment": "FLXID:FRST_Zone1_73B_73M_WIN_AIS::187806", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341042475:*********", "paymentID": *********, "amt": 55, "approveCode": 0}], "PFID": "187806"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 310, "curr": "AED", "originalAmt": 310, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "CancelNoRefund FZ 353 DXB - ISB 15.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 310, "approveCode": 0}]}, {"chargeID": 1340023963, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1340023957, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 40, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1345511561, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1345511556, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023963, "paymentMap": []}, {"chargeID": 1340023969, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187806"}, {"chargeID": 1345511562, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1345511556, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T08:11:25", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340023969, "paymentMap": [], "PFID": "187806"}]}, {"recNum": 3, "charges": [{"chargeID": 1345511636, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:05:33", "billDate": "2025-05-14T08:11:26", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511636:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-14T08:11:26"}, {"chargeID": 1345511568, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1345511567, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511568:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1345511609, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1345511567, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511609:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345511610, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345511567, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511610:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345511611, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1345511567, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511611:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1345511612, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345511567, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511612:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1345511567, "codeType": "AIR", "amt": 866, "curr": "AED", "originalAmt": 866, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "FZ 353 DXB-ISB 19May2025 Mon 00:40 04:50\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511567:*********", "paymentID": *********, "amt": 700.3, "approveCode": 0}, {"key": "1345511567:*********", "paymentID": *********, "amt": 130, "approveCode": 0}, {"key": "1345511567:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}]}, {"chargeID": 1345591472, "codeType": "PMNT", "amt": 33.03, "curr": "AED", "originalAmt": 33.03, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T08:52:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345591472:*********", "paymentID": *********, "amt": 33.03, "approveCode": 0}]}, {"chargeID": 1345511629, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:26", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511629:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1345511644, "codeType": "FRST", "amt": 55, "curr": "AED", "originalAmt": 55, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:05:56", "billDate": "2025-05-14T08:11:26", "desc": "Special Service Request:FRST-8A", "comment": "FLXID:FRST_Zone1_73B_73M_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511644:*********", "paymentID": *********, "amt": 55, "approveCode": 0}], "PFID": "187806"}, {"chargeID": 1345511614, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1345511567, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1345511650, "codeType": "BUPZ", "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:06:52", "billDate": "2025-05-14T08:11:26", "desc": "Special Service Request", "comment": "10 kg Baggage Upgrade", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345511650:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1345511621, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1345511567, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T08:11:25", "billDate": "2025-05-14T08:11:25", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187806"}]}, {"recNum": 1, "charges": [{"chargeID": 1336920000, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336919999, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336920000:*********", "paymentID": *********, "amt": 5, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-08T07:05:00"}, {"chargeID": 1336920004, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336919999, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336920004:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1336920001, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336919999, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336920001:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1336920003, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336919999, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336920003:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1336920005, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336919999, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336920005:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340023902, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336919999, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Passenger Facilities Charge.", "comment": "Cancel due to Reaccommodation", "reasonID": 39, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336920004, "paymentMap": [{"key": "1340023902:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1340023905, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336919999, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Advanced passenger information fee", "comment": "Cancel due to Reaccommodation", "reasonID": 39, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336920000, "paymentMap": [{"key": "1340023905:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1340023907, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336919999, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel due to Reaccommodation", "reasonID": 39, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336920005, "paymentMap": [{"key": "1340023907:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1340023903, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336919999, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "YQ - DUMMY", "comment": "Cancel due to Reaccommodation", "reasonID": 39, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336920001, "paymentMap": [{"key": "1340023903:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1340023904, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336919999, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:17:33", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel due to Reaccommodation", "reasonID": 39, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336920003, "paymentMap": [{"key": "1340023904:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1336919999, "codeType": "AIR", "amt": 210, "curr": "AED", "originalAmt": 210, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T07:05:00", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336919999:*********", "paymentID": *********, "amt": 210, "approveCode": 0}]}, {"chargeID": 1340023906, "codeType": "AIR", "amt": -210, "curr": "AED", "originalAmt": -210, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:17:33", "desc": "WEB:AIR", "comment": "Cancel due to Reaccommodation", "reasonID": 39, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336919999, "paymentMap": [{"key": "1340023906:*********", "paymentID": *********, "amt": -210, "approveCode": 0}]}, {"chargeID": 1336920002, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1336919999, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1336920008, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T07:05:00", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187806"}]}], "parentPNRs": [], "childPNRs": []}