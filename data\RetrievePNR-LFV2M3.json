{"seriesNum": "299", "PNR": "LFV2M3", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "83180643", "bookDate": "2025-05-27T14:55:45", "modifyDate": "2025-05-29T13:28:03", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "ae72851ca1084d5q1ahfk4ba63u1ma1e66b3cf4c40a6", "securityGUID": "ae72851ca1084d5q1ahfk4ba63u1ma1e66b3cf4c40a6", "lastLoadGUID": "1cd3d6db-2da8-4399-9409-764680a12bf1", "isAsyncPNR": false, "MasterPNR": "LFV2M3", "segments": [{"segKey": "16087749:16087749:6/20/2025 10:15:00 AM", "LFID": 16087749, "depDate": "2025-06-20T00:00:00", "flightGroupId": "16087749", "org": "DXB", "dest": "ZNZ", "depTime": "2025-06-20T10:15:00", "depTimeGMT": "2025-06-20T06:15:00", "arrTime": "2025-06-20T14:40:00", "operCarrier": "FZ", "operFlightNum": "1687", "mrktCarrier": "FZ ", "mrktFlightNum": "1687", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181499, "depDate": "2025-06-20T10:15:00", "legKey": "16087749:181499:6/20/2025 10:15:00 AM", "customerKey": "D5AD4C6206B2DA8C96231924FF112216A8D05F778698DBEAADD4F670020E5BB6"}], "active": true, "changeType": "TK"}, {"segKey": "16087825:16087825:7/15/2025 5:25:00 AM", "LFID": 16087825, "depDate": "2025-07-15T00:00:00", "flightGroupId": "16087825", "org": "ZNZ", "dest": "DXB", "depTime": "2025-07-15T05:25:00", "depTimeGMT": "2025-07-15T02:25:00", "arrTime": "2025-07-15T11:55:00", "operCarrier": "FZ", "operFlightNum": "1686", "mrktCarrier": "FZ ", "mrktFlightNum": "1686", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181555, "depDate": "2025-07-15T05:25:00", "legKey": "16087825:181555:7/15/2025 5:25:00 AM", "customerKey": "FD10A591E0B9447D5105581B192206C50BBF408D0DC6C6BAE00C5CE13B3C71BD"}], "active": true, "changeType": "TK"}, {"segKey": "16087720:16087720:8/4/2025 7:55:00 AM", "LFID": 16087720, "depDate": "2025-08-04T00:00:00", "flightGroupId": "16087720", "org": "ZNZ", "dest": "DXB", "depTime": "2025-08-04T07:55:00", "depTimeGMT": "2025-08-04T04:55:00", "arrTime": "2025-08-04T14:30:00", "operCarrier": "FZ", "operFlightNum": "1260", "mrktCarrier": "FZ", "mrktFlightNum": "1260", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181490, "depDate": "2025-08-04T07:55:00", "legKey": "16087720:181490:8/4/2025 7:55:00 AM", "customerKey": "A808E886CF50A4FEEC285BFBCFFBB9BCE61A828220297A80D196FE793D2FC6B3"}], "active": true, "changeType": "TK"}, {"segKey": "16087811:16087811:7/7/2025 12:05:00 AM", "LFID": 16087811, "depDate": "2025-07-07T00:00:00", "flightGroupId": "16087811", "org": "DXB", "dest": "ZNZ", "depTime": "2025-07-07T00:05:00", "depTimeGMT": "2025-07-06T20:05:00", "arrTime": "2025-07-07T04:25:00", "operCarrier": "FZ", "operFlightNum": "1685", "mrktCarrier": "FZ ", "mrktFlightNum": "1685", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181587, "depDate": "2025-07-07T00:05:00", "legKey": "16087811:181587:7/7/2025 12:05:00 AM", "customerKey": "D444A28B9D9BBD68368FB533A6A7D574F8CFC105627DE341DEB401B91F852ADC"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270341958, "fName": "MALEEKA", "lName": "ALMASKARI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "N", "insuPurchasedate": "5/27/2025 2:55:46 PM", "provider": "<PERSON>", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "PJDLM-EYS7B-INS", "insuTransID": "PJDLM-EYS7B-INS/01a1e816-bd0e-4288-870a-ff074faf6b6d", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835cf7200077800000045bc#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-27T14:55:45"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/27/2025 2:55:46 PM", "provider": "<PERSON>", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "PJDLM-EYS7B-INS", "insuTransID": "PJDLM-EYS7B-INS/01a1e816-bd0e-4288-870a-ff074faf6b6d", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68385b8e0007780000010e1c#270341958#2#WEB#SFQE#CHANGE", "fareTypeID": 22, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-27T14:55:45"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/27/2025 2:55:46 PM", "provider": "<PERSON>", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "PJDLM-EYS7B-INS", "insuTransID": "PJDLM-EYS7B-INS/01a1e816-bd0e-4288-870a-ff074faf6b6d", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68385fa400077700000113bc#270341958#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 2, "bookDate": "2025-05-29T13:10:08"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "dhikra.ben", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/27/2025 2:55:46 PM", "provider": "<PERSON>", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "PJDLM-EYS7B-INS", "insuTransID": "PJDLM-EYS7B-INS/01a1e816-bd0e-4288-870a-ff074faf6b6d", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68385fa400077700000113bc#270341958#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-29T13:24:55"}]}], "payments": [{"paymentID": 210705991, "paxID": 270573822, "method": "VISA", "status": "2", "paidDate": "2025-05-29T13:10:59", "cardNum": "************0779", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 144.25, "baseCurr": "AED", "baseAmt": 144.25, "userID": "dhikra.ben", "channelID": 1, "cardHolderName": "<PERSON><PERSON>", "reference": "23323495", "externalReference": "23323495", "tranId": "21707169", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21707169}, {"paymentID": 210706008, "paxID": 270573830, "method": "VISA", "status": "2", "paidDate": "2025-05-29T13:12:40", "cardNum": "************0779", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 144.25, "baseCurr": "AED", "baseAmt": 144.25, "userID": "dhikra.ben", "channelID": 1, "cardHolderName": "<PERSON><PERSON>", "reference": "23323534", "externalReference": "23323534", "tranId": "21707169", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21707169}, {"paymentID": *********, "paxID": 270575399, "method": "VISA", "status": "1", "paidDate": "2025-05-29T13:28:00", "cardNum": "************0779", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 391.4, "baseCurr": "AED", "baseAmt": 391.4, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "667250", "reference": "23323752", "externalReference": "23323752", "tranId": "21707627", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21707627}, {"paymentID": *********, "paxID": 270342096, "method": "VISA", "status": "1", "paidDate": "2025-05-27T14:56:31", "cardNum": "************0779", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2750.2, "baseCurr": "AED", "baseAmt": 2750.2, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "258600", "reference": "23284440", "externalReference": "23284440", "tranId": "21664338", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21664338}], "OAFlights": null, "physicalFlights": [{"key": "16087749:181499:2025-06-20T10:15:00 AM", "LFID": 16087749, "PFID": 181499, "org": "DXB", "dest": "ZNZ", "depDate": "2025-06-20T10:15:00", "depTime": "2025-06-20T10:15:00", "arrTime": "2025-06-20T14:40:00", "carrier": "FZ", "flightNum": "1687", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:38:32 PM"}, {"key": "16087811:181587:2025-07-07T12:05:00 AM", "LFID": 16087811, "PFID": 181587, "org": "DXB", "dest": "ZNZ", "depDate": "2025-07-07T00:05:00", "depTime": "2025-07-07T00:05:00", "arrTime": "2025-07-07T04:25:00", "carrier": "FZ", "flightNum": "1685", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1685", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:38:13 PM"}, {"key": "16087825:181555:2025-07-15T05:25:00 AM", "LFID": 16087825, "PFID": 181555, "org": "ZNZ", "dest": "DXB", "depDate": "2025-07-15T05:25:00", "depTime": "2025-07-15T05:25:00", "arrTime": "2025-07-15T11:55:00", "carrier": "FZ", "flightNum": "1686", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1686", "flightStatus": "OPEN", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19800, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:38:13 PM"}, {"key": "16087720:181490:2025-08-04T07:55:00 AM", "LFID": 16087720, "PFID": 181490, "org": "ZNZ", "dest": "DXB", "depDate": "2025-08-04T07:55:00", "depTime": "2025-08-04T07:55:00", "arrTime": "2025-08-04T14:30:00", "carrier": "FZ", "flightNum": "1260", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1260", "flightStatus": "OPEN", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20100, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 7:52:51 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1367560974, "codeType": "INSU", "amt": -32.55, "curr": "AED", "originalAmt": -32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "INSU", "comment": "Reverse due to <PERSON>", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400227, "paymentMap": [{"key": "1367560974:*********", "paymentID": *********, "amt": -32.55, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-29T13:10:09"}, {"chargeID": 1364400227, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400227:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"17.72\",\r\n  \"Tax\": \"0.84\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1367560940, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364400210, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:08", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400211, "paymentMap": [{"key": "1367560940:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1367560938, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364400210, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:08", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400213, "paymentMap": [{"key": "1367560938:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1367560943, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364400210, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400215, "paymentMap": [{"key": "1367560943:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1367560939, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364400210, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400216, "paymentMap": [{"key": "1367560939:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1367560941, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364400210, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400214, "paymentMap": [{"key": "1367560941:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1364400213, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364400210, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400213:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364400211, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364400210, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400211:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364400214, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364400210, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400214:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364400215, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364400210, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400215:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364400216, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364400210, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400216:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367560944, "codeType": "AIR", "amt": -585, "curr": "AED", "originalAmt": -585, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T13:10:08", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400210, "paymentMap": [{"key": "1367560944:*********", "paymentID": *********, "amt": -585, "approveCode": 0}]}, {"chargeID": 1364400210, "codeType": "AIR", "amt": 585, "curr": "AED", "originalAmt": 585, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T14:55:45", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400210:*********", "paymentID": *********, "amt": 585, "approveCode": 0}]}, {"chargeID": 1364403176, "codeType": "PMNT", "amt": 80.1, "curr": "AED", "originalAmt": 80.1, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T14:56:35", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364403176:*********", "paymentID": *********, "amt": 80.1, "approveCode": 0}]}, {"chargeID": 1367560942, "codeType": "NSST", "amt": -35, "curr": "AED", "originalAmt": -35, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "NSST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400228, "paymentMap": [{"key": "1367560942:*********", "paymentID": *********, "amt": -35, "approveCode": 0}], "PFID": "181499"}, {"chargeID": 1364400228, "codeType": "NSST", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "NSST", "comment": "FLXID:73X_NSST_ZONE3_WIN_AIS::181499", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400228:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "181499"}, {"chargeID": 1367560945, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T13:10:09", "desc": "Penalty AddedDueToModify FZ  1687 DXB  - ZNZ  20-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560945:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1364400212, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1364400210, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364400232, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}]}, {"recNum": 2, "charges": [{"chargeID": 1367595577, "codeType": "INSU", "taxChargeID": 1367595570, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T13:24:56", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367560971, "paymentMap": [], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T13:24:56"}, {"chargeID": 1367560971, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T13:10:09", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1367560975, "codeType": "INSU", "amt": -32.55, "curr": "AED", "originalAmt": -32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "INSU", "comment": "Reverse due to <PERSON>", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400230, "paymentMap": [{"key": "1367560975:*********", "paymentID": *********, "amt": -32.55, "approveCode": 0}]}, {"chargeID": 1364400230, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400230:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"17.72\",\r\n  \"Tax\": \"0.84\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1367595574, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367595570, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400222, "paymentMap": [{"key": "1367595574:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1367595579, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1367595570, "amt": -150, "curr": "AED", "originalAmt": -150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "Airport Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400223, "paymentMap": [{"key": "1367595579:*********", "paymentID": *********, "amt": -150, "approveCode": 0}]}, {"chargeID": 1367595583, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1367595570, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "Aviation Safety Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400221, "paymentMap": [{"key": "1367595583:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1367595586, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367595570, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400219, "paymentMap": [{"key": "1367595586:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1367595573, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1367595570, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "Airport Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400224, "paymentMap": [{"key": "1367595573:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1364400219, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364400218, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400219:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364400221, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1364400218, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Aviation Safety Fee", "comment": "Aviation Safety Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400221:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1364400223, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1364400218, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400223:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1364400222, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364400218, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400222:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364400224, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1364400218, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Airport Security Fee (International)", "comment": "Airport Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400224:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1367595570, "codeType": "AIR", "amt": -1045, "curr": "AED", "originalAmt": -1045, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T13:24:56", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400218, "paymentMap": [{"key": "1367595570:*********", "paymentID": *********, "amt": -1045, "approveCode": 0}]}, {"chargeID": 1364400218, "codeType": "AIR", "amt": 1045, "curr": "AED", "originalAmt": 1045, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T14:55:45", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400218:*********", "paymentID": *********, "amt": 1045, "approveCode": 0}]}, {"chargeID": 1367595585, "codeType": "NSST", "taxChargeID": 1367595570, "amt": -35, "curr": "AED", "originalAmt": -35, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "NSST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400231, "paymentMap": [{"key": "1367595585:*********", "paymentID": *********, "amt": -35, "approveCode": 0}], "PFID": "181555"}, {"chargeID": 1364400231, "codeType": "NSST", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "NSST", "comment": "FLXID:73X_NSST_ZONE3_WIN_AIS::181555", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364400231:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "181555"}, {"chargeID": 1367595589, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "CancelNoRefund FZ 1686 ZNZ - DXB 15.07.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595589:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1367595575, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1367595570, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400220, "paymentMap": []}, {"chargeID": 1364400220, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1364400218, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1367595571, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1367595570, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T13:24:56", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364400233, "paymentMap": [], "PFID": "181555"}, {"chargeID": 1364400233, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T14:55:45", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181555"}]}, {"recNum": 3, "charges": [{"chargeID": 1367595567, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:21:05", "billDate": "2025-05-29T13:24:56", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595567:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"17.72\",\"Tax\":\"0.84\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T13:24:56"}, {"chargeID": 1367595569, "codeType": "INSU", "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T13:24:56", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367560969, "paymentMap": [], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1367560969, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T13:10:09", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1367560947, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1367560946, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560947:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1367560951, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367560946, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560951:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1367560950, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1367560946, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560950:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1367560948, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1367560946, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560948:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367560949, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367560946, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560949:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367560946, "codeType": "AIR", "amt": 595, "curr": "AED", "originalAmt": 595, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T13:10:09", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560946:*********", "paymentID": *********, "amt": 535.1, "approveCode": 0}, {"key": "1367560946:*********", "paymentID": *********, "amt": 59.9, "approveCode": 0}]}, {"chargeID": 1367601213, "codeType": "PMNT", "amt": 11.4, "curr": "AED", "originalAmt": 11.4, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T13:28:03", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367601213:*********", "paymentID": *********, "amt": 11.4, "approveCode": 0}]}, {"chargeID": 1367560970, "codeType": "NSST", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-29T13:10:09", "desc": "NSST", "comment": "FLXID:73X_NSST_ZONE3_WIN_AIS::181587", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367560970:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "181587"}, {"chargeID": 1367560952, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1367560946, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1367560958, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T13:10:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181587"}]}, {"recNum": 4, "charges": [{"chargeID": 1367595603, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:22:51", "billDate": "2025-05-29T13:24:56", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595603:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"17.72\",\"Tax\":\"0.84\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T13:24:56"}, {"chargeID": 1367595593, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1367595591, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "HY: Aviation Safety Fee", "comment": "HY: Aviation Safety Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595593:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1367595594, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367595591, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595594:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367595595, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1367595591, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "P9: Airport Security Fee (International)", "comment": "P9: Airport Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595595:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1367595596, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1367595591, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "NN: Airport Service Charge", "comment": "NN: Airport Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595596:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1367595597, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367595591, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595597:*********", "paymentID": *********, "amt": 155, "approveCode": 0}, {"key": "1367595597:*********", "paymentID": *********, "amt": 125, "approveCode": 0}]}, {"chargeID": 1367595591, "codeType": "AIR", "amt": 1055, "curr": "AED", "originalAmt": 1055, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "FZ 1260 ZNZ-DXB 04Aug2025 Mon 07:55 14:30\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595591:*********", "paymentID": *********, "amt": 1055, "approveCode": 0}]}, {"chargeID": 1367595601, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595601:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1367595602, "codeType": "NSST", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:22:49", "billDate": "2025-05-29T13:24:56", "desc": "Special Service Request:NSST-23D", "comment": "FLXID:73X_NSST_ZONE3_WIN_AIS:\r\nADVANCED SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367595602:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "181490"}, {"chargeID": 1367595598, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1367595591, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1367595600, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1367595591, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T13:24:56", "billDate": "2025-05-29T13:24:56", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181490"}]}], "parentPNRs": [], "childPNRs": []}