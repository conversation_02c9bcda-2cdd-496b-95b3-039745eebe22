{"seriesNum": "299", "PNR": "IN52NW", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "83189689", "bookDate": "2025-05-27T23:13:34", "modifyDate": "2025-05-28T08:35:03", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "a71295c047ae38j4j0u2nb0g6b50a0451fc435c0c152", "securityGUID": "a71295c047ae38j4j0u2nb0g6b50a0451fc435c0c152", "lastLoadGUID": "362D479ED1FE1F43E0630A57380A381C", "MasterPNR": "IN52NW", "segments": [{"segKey": "16659887:16659887:5/29/2025 3:45:00 PM", "LFID": 16659887, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16659887", "org": "DXB", "dest": "TLV", "depTime": "2025-05-29T15:45:00", "depTimeGMT": "2025-05-29T11:45:00", "arrTime": "2025-05-29T18:15:00", "operCarrier": "FZ", "operFlightNum": "1125", "mrktCarrier": "FZ ", "mrktFlightNum": "1125", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 185106, "depDate": "2025-05-29T15:45:00", "legKey": "16659887:185106:5/29/2025 3:45:00 PM", "customerKey": "7C26488968680B2B635690E3D1D8968BDA99FBA9A9062C2BF4918395870585B1"}], "active": true}, {"segKey": "16659886:16659886:6/4/2025 3:50:00 PM", "LFID": 16659886, "depDate": "2025-06-04T00:00:00", "flightGroupId": "16659886", "org": "TLV", "dest": "DXB", "depTime": "2025-06-04T15:50:00", "depTimeGMT": "2025-06-04T12:50:00", "arrTime": "2025-06-04T20:05:00", "operCarrier": "FZ", "operFlightNum": "1082", "mrktCarrier": "FZ ", "mrktFlightNum": "1082", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 185105, "depDate": "2025-06-04T15:50:00", "legKey": "16659886:185105:6/4/2025 3:50:00 PM", "customerKey": "25F9E374345031604C7E8E0E6AC73AA142576EF4E8DFCCFD8FB390932DBB2C94"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270378566, "fName": "HANOCH", "lName": "HARAZI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "AutoCancel", "statusReasonID": 0, "markFareClass": "W", "insuPurchasedate": "5/27/2025 11:13:35 PM", "provider": "<PERSON>", "status": 0, "fareClass": "W", "operFareClass": "W", "FBC": "WRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "XREHU-JMVYT-INS/868d4515-fa32-495c-ab98-1723d68f9f25", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6836467700077700000007fb#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 10, "bookDate": "2025-05-27T23:13:34"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "AutoCancel", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/27/2025 11:13:35 PM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "XREHU-JMVYT-INS/868d4515-fa32-495c-ab98-1723d68f9f25", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6836467700077700000007fb#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 10, "bookDate": "2025-05-27T23:13:34"}]}], "payments": [{"paymentID": 210544914, "paxID": 270378967, "method": "MSCD", "status": "1", "paidDate": "2025-05-28T08:33:48", "cardNum": "************0086", "gateway": "EPS", "paidCurr": "AED", "paidAmt": -2696.23, "baseCurr": "AED", "baseAmt": -2696.23, "userID": "fzdb", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "02748Q", "reference": "23291134", "externalReference": "23291134", "tranId": "21671525", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": "1", "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671525}, {"paymentID": *********, "paxID": 270378967, "method": "MSCD", "status": "1", "paidDate": "2025-05-27T23:21:47", "cardNum": "************0086", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2696.23, "baseCurr": "AED", "baseAmt": 2696.23, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "02748Q", "reference": "23291134", "externalReference": "23291134", "tranId": "21671525", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671525}, {"paymentID": 210509243, "paxID": 270378763, "method": "MSCD", "status": "2", "paidDate": "2025-05-27T23:17:32", "cardNum": "************0086", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2696.23, "baseCurr": "AED", "baseAmt": 2696.23, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "reference": "23291134", "externalReference": "23291134", "tranId": "21671525", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671525}, {"paymentID": *********, "paxID": 270378809, "method": "GPAY", "status": "1", "paidDate": "2025-05-27T23:17:50", "cardNum": "************0086", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2696.23, "baseCurr": "AED", "baseAmt": 2696.23, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "02713Q", "reference": "23291114", "externalReference": "23291114", "tranId": "21671525", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671525}], "OAFlights": null, "physicalFlights": [{"key": "16659887:185106:2025-05-29T03:45:00 PM", "LFID": 16659887, "PFID": 185106, "org": "DXB", "dest": "TLV", "depDate": "2025-05-29T15:45:00", "depTime": "2025-05-29T15:45:00", "arrTime": "2025-05-29T18:15:00", "carrier": "FZ", "flightNum": "1125", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1125", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false}, {"key": "16659886:185105:2025-06-04T03:50:00 PM", "LFID": 16659886, "PFID": 185105, "org": "TLV", "dest": "DXB", "depDate": "2025-06-04T15:50:00", "depTime": "2025-06-04T15:50:00", "arrTime": "2025-06-04T20:05:00", "carrier": "FZ", "flightNum": "1082", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "1082", "flightStatus": "OPEN", "originMetroGroup": "TLV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": false, "originName": "Tel Aviv Ben Gurion", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/14/2025 6:52:59 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1365356399, "codeType": "PYRF", "amt": -2696.23, "curr": "AED", "originalAmt": -2696.23, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-28T08:31:09", "billDate": "2025-05-28T08:31:09", "desc": "Overpayment refund charge manual cancel - radixx", "comment": "Overpayment refund charge", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365349113, "paymentMap": [{"key": "1365356399:210544914", "paymentID": 210544914, "amt": -2696.23, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-28T08:31:09"}, {"chargeID": 1365349113, "codeType": "PYRF", "amt": 2696.23, "curr": "AED", "originalAmt": 2696.23, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-28T08:28:10", "billDate": "2025-05-28T08:28:10", "desc": "Overpayment refund charge", "comment": "Payment Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365349113:*********", "paymentID": *********, "amt": 2696.23, "approveCode": 0}]}, {"chargeID": 1364943932, "codeType": "INSU", "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "INSU", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915036, "paymentMap": [{"key": "1364943932:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}]}, {"chargeID": 1365348418, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "INSU manual cancel - radixx", "comment": "INSU", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348418:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}]}, {"chargeID": 1364915036, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915036:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1364943926, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915022, "paymentMap": [{"key": "1364943926:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1364943929, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915025, "paymentMap": [{"key": "1364943929:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364943930, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915023, "paymentMap": [{"key": "1364943930:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1364943931, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915026, "paymentMap": [{"key": "1364943931:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1364943934, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915024, "paymentMap": [{"key": "1364943934:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1365348412, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Passenger Service Charge (Intl) manual cancel - radixx", "comment": "Passenger Service Charge (Intl)", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348412:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1365348415, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Advanced passenger information fee manual cancel - radixx", "comment": "Advanced passenger information fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348415:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1365348416, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Passenger Facilities Charge. manual cancel - radixx", "comment": "Passenger Facilities Charge.", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348416:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1365348417, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "YQ - DUMMY manual cancel - radixx", "comment": "YQ - DUMMY", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348417:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1365348420, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Passengers Security & Safety Service Fees manual cancel - radixx", "comment": "Passengers Security & Safety Service Fees", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348420:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364915022, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364915021, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915022:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364915025, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364915021, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915025:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364915023, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364915021, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915023:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364915026, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364915021, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915026:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1364915024, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364915021, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915024:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364943928, "codeType": "AIR", "amt": -1285, "curr": "AED", "originalAmt": -1285, "originalCurr": "AED", "status": 0, "billDate": "2025-05-28T00:34:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915021, "paymentMap": [{"key": "1364943928:*********", "paymentID": *********, "amt": -1285, "approveCode": 0}]}, {"chargeID": 1365348414, "codeType": "AIR", "amt": 1285, "curr": "AED", "originalAmt": 1285, "originalCurr": "AED", "status": 1, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "WEB:AIR manual cancel - radixx", "comment": "WEB:AIR", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348414:*********", "paymentID": *********, "amt": 1285, "approveCode": 0}]}, {"chargeID": 1364915021, "codeType": "AIR", "amt": 1285, "curr": "AED", "originalAmt": 1285, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:13:35", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915021:*********", "paymentID": *********, "amt": 1285, "approveCode": 0}]}, {"chargeID": 1364921356, "codeType": "PMNT", "amt": 78.53, "curr": "AED", "originalAmt": 78.53, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:21:49", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364921356:*********", "paymentID": *********, "amt": 78.53, "approveCode": 0}]}, {"chargeID": 1365347714, "codeType": "PMNT", "amt": -78.53, "curr": "AED", "originalAmt": -78.53, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-28T08:27:25", "billDate": "2025-05-28T08:27:25", "desc": "Payment Fee manual cancel - radixx", "comment": "Payment Fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364921356, "paymentMap": [{"key": "1365347714:*********", "paymentID": *********, "amt": -78.53, "approveCode": 0}]}, {"chargeID": 1364918544, "codeType": "PMNT", "amt": 78.53, "curr": "AED", "originalAmt": 78.53, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T23:17:54", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364918544:*********", "paymentID": *********, "amt": 78.53, "approveCode": 0}]}, {"chargeID": 1364943933, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915027, "paymentMap": []}, {"chargeID": 1365348419, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "20kg BAG INCLUDED IN FARE manual cancel - radixx", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364943933, "paymentMap": []}, {"chargeID": 1364915027, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364915021, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364943927, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915039, "paymentMap": [{"key": "1364943927:*********", "paymentID": *********, "amt": -2617.7, "approveCode": 0}, {"key": "1364943927:*********", "paymentID": *********, "amt": 2617.7, "approveCode": 0}]}, {"chargeID": 1365348413, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Standard meal manual cancel - radixx", "comment": "Standard meal", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364943927, "paymentMap": []}, {"chargeID": 1364915039, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185106"}]}, {"recNum": 2, "charges": [{"chargeID": 1364943936, "codeType": "INSU", "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "INSU", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915038, "paymentMap": [{"key": "1364943936:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-28T00:34:16"}, {"chargeID": 1365348422, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "INSU manual cancel - radixx", "comment": "INSU", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348422:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}]}, {"chargeID": 1364915038, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915038:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1364943935, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "amt": -110, "curr": "AED", "originalAmt": -110, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Departure Passenger Airport Tax - (Intl.)", "comment": "Departure Passenger Airport Tax - (Intl.)", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915032, "paymentMap": [{"key": "1364943935:*********", "paymentID": *********, "amt": -110, "approveCode": 0}]}, {"chargeID": 1364943940, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915030, "paymentMap": [{"key": "1364943940:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364943941, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915031, "paymentMap": [{"key": "1364943941:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1365348421, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "amt": 110, "curr": "AED", "originalAmt": 110, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Departure Passenger Airport Tax - (Intl.) manual cancel - radixx", "comment": "Departure Passenger Airport Tax - (Intl.)", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348421:*********", "paymentID": *********, "amt": 110, "approveCode": 0}]}, {"chargeID": 1365348426, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Advanced passenger information fee manual cancel - radixx", "comment": "Advanced passenger information fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348426:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1365348427, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "YQ - DUMMY manual cancel - radixx", "comment": "YQ - DUMMY", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348427:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1364915032, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "taxChargeID": 1364915029, "amt": 110, "curr": "AED", "originalAmt": 110, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Departure Passenger Airport Tax - (Intl.)", "comment": "Departure Passenger Airport Tax - (Intl.)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915032:*********", "paymentID": *********, "amt": 110, "approveCode": 0}]}, {"chargeID": 1364915030, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364915029, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915030:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364915031, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364915029, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915031:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1364943939, "codeType": "AIR", "amt": -692, "curr": "AED", "originalAmt": -692, "originalCurr": "AED", "status": 0, "billDate": "2025-05-28T00:34:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915029, "paymentMap": [{"key": "1364943939:*********", "paymentID": *********, "amt": -692, "approveCode": 0}]}, {"chargeID": 1365348425, "codeType": "AIR", "amt": 692, "curr": "AED", "originalAmt": 692, "originalCurr": "AED", "status": 1, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "WEB:AIR manual cancel - radixx", "comment": "WEB:AIR", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365348425:*********", "paymentID": *********, "amt": 692, "approveCode": 0}]}, {"chargeID": 1364915029, "codeType": "AIR", "amt": 692, "curr": "AED", "originalAmt": 692, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:13:35", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364915029:*********", "paymentID": *********, "amt": 692, "approveCode": 0}]}, {"chargeID": 1364943938, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915033, "paymentMap": []}, {"chargeID": 1365348424, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "20kg BAG INCLUDED IN FARE manual cancel - radixx", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364943938, "paymentMap": []}, {"chargeID": 1364915033, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364915029, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364943937, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:34:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364915040, "paymentMap": []}, {"chargeID": 1365348423, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "exchRateDate": "2025-05-28T08:27:47", "billDate": "2025-05-28T08:27:47", "desc": "Standard meal manual cancel - radixx", "comment": "Standard meal", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364943937, "paymentMap": []}, {"chargeID": 1364915040, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:13:35", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185105"}]}], "parentPNRs": [], "childPNRs": []}