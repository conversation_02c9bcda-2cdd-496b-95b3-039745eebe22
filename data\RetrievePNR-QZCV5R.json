{"seriesNum": "299", "PNR": "QZCV5R", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82618307", "bookDate": "2025-05-07T09:26:19", "modifyDate": "2025-05-09T13:24:11", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "b02e4fu7uc44i7gc62z7b4o3y183b1adf2020905815a", "securityGUID": "b02e4fu7uc44i7gc62z7b4o3y183b1adf2020905815a", "lastLoadGUID": "c7434805-b0e3-4ead-889d-7a5c80f69cd7", "isAsyncPNR": false, "MasterPNR": "QZCV5R", "segments": [{"segKey": "16087379:16087379:5/16/2025 8:35:00 PM", "LFID": 16087379, "depDate": "2025-05-16T00:00:00", "flightGroupId": "16087379", "org": "DXB", "dest": "COK", "depTime": "2025-05-16T20:35:00", "depTimeGMT": "2025-05-16T16:35:00", "arrTime": "2025-05-17T02:15:00", "operCarrier": "FZ", "operFlightNum": "453", "mrktCarrier": "FZ ", "mrktFlightNum": "453", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181140, "depDate": "2025-05-16T20:35:00", "legKey": "16087379:181140:5/16/2025 8:35:00 PM", "customerKey": "BB7A42581E13B1BC60D8BDA51E848C43461098F743E54A94945555933A5D82F6"}], "active": true, "changeType": "TK"}, {"segKey": "16087379:16087379:5/9/2025 8:35:00 PM", "LFID": 16087379, "depDate": "2025-05-09T00:00:00", "flightGroupId": "16087379", "org": "DXB", "dest": "COK", "depTime": "2025-05-09T20:35:00", "depTimeGMT": "2025-05-09T16:35:00", "arrTime": "2025-05-10T02:15:00", "operCarrier": "FZ", "operFlightNum": "453", "mrktCarrier": "FZ", "mrktFlightNum": "453", "persons": [{"recNum": 2, "status": 0}, {"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181140, "depDate": "2025-05-09T20:35:00", "legKey": "16087379:181140:5/9/2025 8:35:00 PM", "customerKey": "D586BF6898D13188B7C9B838E458C280ED6E1F61BF932AE8ECCBEA001CBFCCCF"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268171547, "fName": "JULIAN", "lName": "SPADIGAM", "title": "MR", "PTCID": 1, "gender": "M", "FFNum": "111433070", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/7/2025 9:26:20 AM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "MA8SV-ULMLH-INS", "insuTransID": "MA8SV-ULMLH-INS/391913ec-32a7-486f-bb54-ac4ddce84114", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681b249e000777000000410b#1#1#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-07T09:26:20"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "mohammed.haroon", "cancelAgent": "PLUSGRADE_FZ", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/7/2025 9:26:20 AM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "MA8SV-ULMLH-INS/391913ec-32a7-486f-bb54-ac4ddce84114", "toRecNum": 3, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681c831e000778000000068a#268171547#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "cancelReasonID": 0, "bookDate": "2025-05-08T10:12:46"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "PLUSGRADE_FZ", "statusReasonID": 0, "markFareClass": "Z", "insuPurchasedate": "5/7/2025 9:26:20 AM", "provider": "<PERSON>", "status": 5, "fareClass": "Z", "operFareClass": "Z", "FBC": "ZOFFER", "fareBrand": "Business", "cabin": "BUSINESS", "discloseEmergencyContact": 0, "insuTransID": "MA8SV-ULMLH-INS/391913ec-32a7-486f-bb54-ac4ddce84114", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "fareTypeID": 15, "channelID": 28, "bookDate": "2025-05-08T20:51:58"}]}], "payments": [{"paymentID": *********, "paxID": 268297719, "method": "VISA", "status": "1", "paidDate": "2025-05-08T10:15:54", "cardNum": "************3668", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 150.38, "baseCurr": "AED", "baseAmt": 150.38, "userID": "paybylink", "channelID": 2, "cardHolderName": "Soccer Italian Style Football Training", "authCode": "060734", "reference": "22911806", "externalReference": "22911806", "tranId": "21284708", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21284708}, {"paymentID": *********, "paxID": 268290750, "method": "VISA", "status": "1", "paidDate": "2025-05-08T09:25:58", "cardNum": "************3668", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1159.47, "baseCurr": "AED", "baseAmt": 1159.47, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "Soccer Training", "authCode": "371309", "reference": "22911171", "externalReference": "22911171", "tranId": "21283520", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21283520}, {"paymentID": *********, "paxID": 268360132, "method": "VISA", "status": "1", "paidDate": "2025-05-08T20:51:51", "cardNum": "************3668", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 155, "baseCurr": "AED", "baseAmt": 569.32, "userID": "PLUSGRADE_FZ", "channelID": 28, "cardHolderName": "Julian <PERSON>", "authCode": "647140", "reference": "22922278", "externalReference": "22922278", "tranId": "21295764", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPMUSD", "exchangeRate": "0.27", "resExternalPaymentID": 21295764}, {"paymentID": *********, "paxID": 268171678, "method": "VISA", "status": "1", "paidDate": "2025-05-07T09:27:00", "cardNum": "************7502", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 29.87, "baseCurr": "AED", "baseAmt": 29.87, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "Julian <PERSON>", "authCode": "604161", "reference": "22889158", "externalReference": "22889158", "tranId": "21262228", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21262228}], "OAFlights": null, "physicalFlights": [{"key": "16087379:181140:2025-05-09T08:35:00 PM", "LFID": 16087379, "PFID": 181140, "org": "DXB", "dest": "COK", "depDate": "2025-05-09T20:35:00", "depTime": "2025-05-09T20:35:00", "arrTime": "2025-05-10T02:15:00", "carrier": "FZ", "flightNum": "453", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "453", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "COK", "operatingCarrier": "FZ", "flightDuration": 15000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kochi International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:34:20 AM"}, {"key": "16087379:181140:2025-05-16T08:35:00 PM", "LFID": 16087379, "PFID": 181140, "org": "DXB", "dest": "COK", "depDate": "2025-05-16T20:35:00", "depTime": "2025-05-16T20:35:00", "arrTime": "2025-05-17T02:15:00", "carrier": "FZ", "flightNum": "453", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "453", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "COK", "operatingCarrier": "FZ", "flightDuration": 15000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kochi International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:34:20 AM"}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": 1338950056, "codeType": "BLTR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-09T13:24:11", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181140", "POSAirport": "DXB", "workStationID": "LTFZ7564", "ssrCommentId": "*********", "isSSR": true, "parameter1Name": "BATCH_ID", "parameter1Value": "77f947z7fajd1tu2ze2564d9f443af7da8f007edc05e", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-09T20:35:00\",\"fltNum\":\"453\",\"depDate\":\"2025-05-09T00:00:00\",\"board\":\"DXB\",\"off\":\"COK\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1143590670_DXBCOK", "ChargeBookDate": "2025-05-09T13:24:11"}, {"chargeID": 1338132514, "codeType": "INSU", "taxChargeID": 1338132485, "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132514:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}]}, {"chargeID": 1338132487, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338132485, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132487:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1338132510, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338132485, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132510:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1338132509, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338132485, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132509:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338132486, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338132485, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132486:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1338132488, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338132485, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132488:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338132485, "codeType": "AIR", "amt": 956, "curr": "AED", "originalAmt": 956, "originalCurr": "AED", "status": 1, "billDate": "2025-05-08T20:51:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 28, "basePoints": 600, "tierPoints": 600, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1338132485:*********", "paymentID": *********, "amt": 150.38, "approveCode": 0}, {"key": "1338132485:*********", "paymentID": *********, "amt": 805.62, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1338132513, "codeType": "PMNT", "taxChargeID": 1338132485, "amt": 4.38, "curr": "AED", "originalAmt": 4.38, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132513:*********", "paymentID": *********, "amt": 4.38, "approveCode": 0}]}, {"chargeID": 1338132515, "codeType": "MFEE", "taxChargeID": 1338132485, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338132515:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1338132511, "codeType": "JBAG", "taxID": 6544, "taxCode": "JBAG", "taxChargeID": 1338132485, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "40kg Baggage allowance Business", "comment": "40kg Baggage allowance Business", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1338132512, "codeType": "IFPJ", "taxID": 6545, "taxCode": "IFPJ", "taxChargeID": 1338132485, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "In flight entertainment Business", "comment": "In flight entertainment Business", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1338132495, "codeType": "PGRD", "amt": 569.32, "curr": "AED", "originalAmt": 569.32, "originalCurr": "AED", "status": 1, "exchRate": 3.673, "billDate": "2025-05-08T20:51:58", "desc": "PGRD", "reasonID": 0, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1338132495:*********", "paymentID": *********, "amt": 569.32, "approveCode": 0, "saleCurrency": "USD", "saleAmount": 155}], "saleCurrency": "USD", "saleAmount": 155, "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1338132521, "codeType": "LNGN", "taxID": 13790, "taxCode": "LNGN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-08T20:51:58", "desc": "NO LOUNGE ACCESS.", "comment": "SSR Added", "reasonID": 2, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181140", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": 1337298844, "codeType": "INSU", "taxChargeID": 1337298821, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:48", "desc": "INSU", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648427, "paymentMap": [{"key": "1337298844:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-08T10:12:48"}, {"chargeID": 1335648427, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648427:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1337298836, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337298821, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "Passenger Service Charge (Intl)", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648418, "paymentMap": [{"key": "1337298836:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1337298838, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337298821, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "Passengers Security & Safety Service Fees", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648420, "paymentMap": [{"key": "1337298838:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1337298841, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337298821, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "Advanced passenger information fee", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648421, "paymentMap": [{"key": "1337298841:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1337298842, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337298821, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "Passenger Facilities Charge.", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648419, "paymentMap": [{"key": "1337298842:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1337298843, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337298821, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:48", "desc": "YQ - DUMMY", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648422, "paymentMap": [{"key": "1337298843:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1335648418, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1335648417, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648418:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1335648420, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1335648417, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648420:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1335648421, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1335648417, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648421:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1335648419, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1335648417, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648419:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1335648422, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1335648417, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648422:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1337298821, "codeType": "AIR", "amt": -870, "curr": "AED", "originalAmt": -870, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T10:12:47", "desc": "WEB:AIR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648417, "paymentMap": [{"key": "1337298821:*********", "paymentID": *********, "amt": -870, "approveCode": 0}]}, {"chargeID": 1335648417, "codeType": "AIR", "amt": 870, "curr": "AED", "originalAmt": 870, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T09:26:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335648417:*********", "paymentID": *********, "amt": 870, "approveCode": 0}]}, {"chargeID": 1337199978, "codeType": "PMNT", "amt": 33.77, "curr": "AED", "originalAmt": 33.77, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T09:26:03", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337199978:*********", "paymentID": *********, "amt": 33.77, "approveCode": 0}]}, {"chargeID": 1335651854, "codeType": "PMNT", "amt": 0.87, "curr": "AED", "originalAmt": 0.87, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T09:27:04", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335651854:*********", "paymentID": *********, "amt": 0.87, "approveCode": 0}]}, {"chargeID": 1335651855, "codeType": "BHFT", "amt": 29, "curr": "AED", "originalAmt": 29, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T09:27:04", "desc": "Hold Book Charge", "comment": "TLTRULE:102", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335651855:*********", "paymentID": *********, "amt": 29, "approveCode": 0}], "parameter1Name": "TLT_EXTENSION_HR", "parameter1Value": "24", "parameter2Name": "TLT_EXTENSION_FEE", "parameter2Value": "29.0"}, {"chargeID": 1337298839, "codeType": "FRST", "taxChargeID": 1337298821, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "FRST", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648428, "paymentMap": [], "PFID": "181140"}, {"chargeID": 1335648428, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181140", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181140"}, {"chargeID": 1337298840, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337298821, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "Included seat", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648424, "paymentMap": []}, {"chargeID": 1335648424, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1335648417, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337298835, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1337298821, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:47", "desc": "40kg BAG INCLUDED IN FARE", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648423, "paymentMap": []}, {"chargeID": 1335648423, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1335648417, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337298845, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337298821, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:12:48", "desc": "Standard meal", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335648449, "paymentMap": [], "PFID": "181140"}, {"chargeID": 1335648449, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T09:26:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181140"}]}, {"recNum": 2, "charges": [{"chargeID": 1338132490, "codeType": "INSU", "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:10:54", "billDate": "2025-05-08T20:51:58", "desc": "Special Service Request", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298877, "paymentMap": [{"key": "1338132490:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":9.72,\"tax\":0.46,\"currency\":\"USD\",\"segPaxCount\":1}", "ChargeBookDate": "2025-05-08T20:51:58"}, {"chargeID": 1337298877, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:10:54", "billDate": "2025-05-08T10:12:48", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298877:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":9.72,\"tax\":0.46,\"currency\":\"USD\",\"segPaxCount\":1}"}, {"chargeID": 1338132489, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337298858, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "AE: Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298859, "paymentMap": [{"key": "1338132489:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1338132445, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337298858, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "F6: Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298860, "paymentMap": [{"key": "1338132445:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1338132448, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337298858, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "ZR: Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298862, "paymentMap": [{"key": "1338132448:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1338132491, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337298858, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298861, "paymentMap": [{"key": "1338132491:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1338132492, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337298858, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "YQ: YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298864, "paymentMap": [{"key": "1338132492:*********", "paymentID": *********, "amt": -50.3, "approveCode": 0}, {"key": "1338132492:*********", "paymentID": *********, "amt": -39.7, "approveCode": 0}]}, {"chargeID": 1337298859, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337298858, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298859:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1337298860, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337298858, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298860:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1337298861, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337298858, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298861:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337298862, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337298858, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298862:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337298864, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337298858, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298864:*********", "paymentID": *********, "amt": 39.7, "approveCode": 0}, {"key": "1337298864:*********", "paymentID": *********, "amt": 50.3, "approveCode": 0}]}, {"chargeID": 1338132446, "codeType": "AIR", "amt": -956, "curr": "AED", "originalAmt": -956, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "FZ 453 DXB-COK 09May2025 Fri 20:35 02:15\r\n", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298858, "paymentMap": [{"key": "1338132446:*********", "paymentID": *********, "amt": -956, "approveCode": 0}]}, {"chargeID": 1337298858, "codeType": "AIR", "amt": 956, "curr": "AED", "originalAmt": 956, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "FZ 453 DXB-COK 09May2025 Fri 20:35 02:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298858:*********", "paymentID": *********, "amt": 956, "approveCode": 0}]}, {"chargeID": 1338132447, "codeType": "PMNT", "amt": -4.38, "curr": "AED", "originalAmt": -4.38, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T20:51:58", "desc": "Payment Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337304777, "paymentMap": [{"key": "1338132447:*********", "paymentID": *********, "amt": -4.38, "approveCode": 0}]}, {"chargeID": 1337304777, "codeType": "PMNT", "amt": 4.38, "curr": "AED", "originalAmt": 4.38, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T10:16:00", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337304777:*********", "paymentID": *********, "amt": 4.38, "approveCode": 0}]}, {"chargeID": 1338132493, "codeType": "MFEE", "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T20:51:58", "desc": "Special Service Request", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 28, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337298875, "paymentMap": [{"key": "1338132493:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1337298875, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337298875:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1337298878, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:11:38", "billDate": "2025-05-08T10:12:48", "desc": "Special Service Request:FRST-7D", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181140"}, {"chargeID": 1337298870, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337298858, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337298865, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1337298858, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337298866, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337298858, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:12:48", "billDate": "2025-05-08T10:12:48", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181140"}]}], "parentPNRs": [], "childPNRs": []}