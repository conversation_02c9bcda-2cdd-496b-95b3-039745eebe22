{"seriesNum": "299", "PNR": "TPEBUR", "bookAgent": "maryam.g", "IATA": "091120DP", "resCurrency": "AED", "PNRPin": "82548590", "bookDate": "2025-05-05T07:58:46", "modifyDate": "2025-05-12T11:46:24", "resType": "STANDARD", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "", "securityGUID": "", "lastLoadGUID": "a29bd37e-245a-45d2-bf4f-8615f60ef0b7", "isAsyncPNR": false, "MasterPNR": "TPEBUR", "segments": [{"segKey": "16087815:16087815:5/16/2025 5:50:00 AM", "LFID": 16087815, "depDate": "2025-05-16T00:00:00", "flightGroupId": "16087815", "org": "FRU", "dest": "DXB", "depTime": "2025-05-16T05:50:00", "depTimeGMT": "2025-05-15T23:50:00", "arrTime": "2025-05-16T08:10:00", "operCarrier": "FZ", "operFlightNum": "1690", "mrktCarrier": "FZ ", "mrktFlightNum": "1690", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181591, "depDate": "2025-05-16T05:50:00", "legKey": "16087815:181591:5/16/2025 5:50:00 AM", "customerKey": "7E96488A873E1A960623800E55FA9BA17F07C46F3B6C4DB3D45EC6BE9C539222"}], "active": true}, {"segKey": "16863116:16863116:5/6/2025 10:55:00 PM", "LFID": 16863116, "depDate": "2025-05-06T00:00:00", "flightGroupId": "16863116", "org": "DXB", "dest": "ALA", "depTime": "2025-05-06T22:55:00", "depTimeGMT": "2025-05-06T18:55:00", "arrTime": "2025-05-07T04:05:00", "operCarrier": "FZ", "operFlightNum": "1735", "mrktCarrier": "FZ", "mrktFlightNum": "1735", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 186411, "depDate": "2025-05-06T22:55:00", "legKey": "16863116:186411:5/6/2025 10:55:00 PM", "customerKey": "DC66F3653BAE9C97E6C6F3203D3FE9599EE065D210BFD7D2DE22216DC624DBBC"}], "active": true}, {"segKey": "16863093:16863093:5/16/2025 5:05:00 AM", "LFID": 16863093, "depDate": "2025-05-16T00:00:00", "flightGroupId": "16863093", "org": "ALA", "dest": "DXB", "depTime": "2025-05-16T05:05:00", "depTimeGMT": "2025-05-16T00:05:00", "arrTime": "2025-05-16T08:45:00", "operCarrier": "FZ", "operFlightNum": "1736", "mrktCarrier": "FZ", "mrktFlightNum": "1736", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 186408, "depDate": "2025-05-16T05:05:00", "legKey": "16863093:186408:5/16/2025 5:05:00 AM", "customerKey": "39D23D3CFCB942818EF3A73D4486297AB0D081AFCEEC271C30FE364D592E036E"}], "active": true}], "persons": [{"paxID": 267912494, "fName": "ARIF", "lName": "BARAKAT", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1971-01-14T00:00:00", "FFNum": "279836034", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "maryam.g", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/5/2025 12:15:05 PM", "provider": "AIG", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "a03b9ce1-6f81-4f97-b41d-2f727c347da7", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68186f2e0007780000000bad#1#1#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 0, "bookDate": "2025-05-05T07:58:46"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "maryam.g", "cancelAgent": "95000222akhin", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/5/2025 12:15:05 PM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "a03b9ce1-6f81-4f97-b41d-2f727c347da7", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68186f2e0007780000000bad#1#2#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 0, "bookDate": "2025-05-05T07:58:46"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "95000222akhin", "statusReasonID": 0, "markFareClass": "U", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821d66a00077800000015b4#267912494#2#TA#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 16, "bookDate": "2025-05-12T11:27:27"}]}], "payments": [{"paymentID": *********, "paxID": 14141150, "method": "INVC", "status": "1", "paidDate": "2025-05-12T11:46:19", "IATANum": "95000222", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 435, "baseCurr": "AED", "baseAmt": 435, "userID": "95000222akhin", "channelID": 16, "authCode": "110691003", "reference": "A5352430", "externalReference": "A5352430", "tranId": "21359587", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21359587}, {"paymentID": 207960185, "paxID": 267950735, "method": "VISA", "status": "2", "paidDate": "2025-05-05T12:20:24", "cardNum": "************7902", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 85.8, "baseCurr": "AED", "baseAmt": 85.8, "userID": "dalila.hasan", "channelID": 1, "cardHolderName": "ARIF BARAKAT", "reference": "22849612", "externalReference": "22849612", "tranId": "21221174", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21221174}, {"paymentID": *********, "paxID": 267916519, "method": "RPOS", "status": "1", "paidDate": "2025-05-05T08:24:33", "paidCurr": "AED", "paidAmt": 1889, "baseCurr": "AED", "baseAmt": 1889, "userID": "cashier.nib1", "channelID": 1, "paymentComment": "22641", "authCode": "X33182", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}, {"paymentID": *********, "paxID": 267951168, "method": "VISA", "status": "1", "paidDate": "2025-05-05T12:32:54", "cardNum": "************7902", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 85.8, "baseCurr": "AED", "baseAmt": 85.8, "userID": "paybylink", "channelID": 2, "cardHolderName": "ARIF BARAKAT", "authCode": "139574", "reference": "22849773", "externalReference": "22849773", "tranId": "21221572", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21221572}], "OAFlights": null, "physicalFlights": [{"key": "16863116:186411:2025-05-06T10:55:00 PM", "LFID": 16863116, "PFID": 186411, "org": "DXB", "dest": "ALA", "depDate": "2025-05-06T22:55:00", "depTime": "2025-05-06T22:55:00", "arrTime": "2025-05-07T04:05:00", "carrier": "FZ", "flightNum": "1735", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "1735", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ALA", "operatingCarrier": "FZ", "flightDuration": 15000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Almaty", "isActive": false}, {"key": "16863093:186408:2025-05-16T05:05:00 AM", "LFID": 16863093, "PFID": 186408, "org": "ALA", "dest": "DXB", "depDate": "2025-05-16T05:05:00", "depTime": "2025-05-16T05:05:00", "arrTime": "2025-05-16T08:45:00", "carrier": "FZ", "flightNum": "1736", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "1736", "flightStatus": "CLOSED", "originMetroGroup": "ALA", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16800, "reaccomChangeAlert": false, "originName": "Almaty", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087815:181591:2025-05-16T05:50:00 AM", "LFID": 16087815, "PFID": 181591, "org": "FRU", "dest": "DXB", "depDate": "2025-05-16T05:50:00", "depTime": "2025-05-16T05:50:00", "arrTime": "2025-05-16T08:10:00", "carrier": "FZ", "flightNum": "1690", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1690", "flightStatus": "CLOSED", "originMetroGroup": "FRU", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 15600, "reaccomChangeAlert": false, "originName": "Bishkek", "destinationName": "Dubai International Airport", "isActive": false}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-05T12:19:11", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T12:19:11"}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1332287494, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287494:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1332287495, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287495:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1332287496, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "JN: International Advanced Passenger Information Fee.", "comment": "JN: International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287496:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1332287497, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287497:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 310, "curr": "AED", "originalAmt": 310, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "FZ 1735 DXB-ALA 06May2025 Tue 22:55 04:05\r\n", "reasonID": 0, "channelID": 1, "basePoints": 200, "tierPoints": 200, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 310, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332800409, "codeType": "PMNT", "amt": 2.5, "curr": "AED", "originalAmt": 2.5, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T12:32:59", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332800409:*********", "paymentID": *********, "amt": 2.5, "approveCode": 0}]}, {"chargeID": 1332287500, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:25", "billDate": "2025-05-05T07:58:46", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287500:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1332315232, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T08:11:21", "billDate": "2025-05-05T08:11:27", "desc": "Special Service Request:XLGR-16A", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332315232:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "186411", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332773574, "codeType": "BUPL", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-05T12:19:11", "desc": "BUPL", "comment": "FLXID:GCC-AE DXB-GYD/TBS/TURKEYZONE/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332773574:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332287498, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332287499, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "186411", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1334767428, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-06T16:46:23", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "186411", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T12:19:12", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T12:19:12"}, {"chargeID": **********, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": **********, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-12T11:27:27", "desc": "CS: Aviation Security Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-12T11:27:27", "desc": "ZR: Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332287505, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1342526097, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-12T11:27:27", "desc": "YQ: YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332287506, "paymentMap": [{"key": "1342526097:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1342526100, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": **********, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-12T11:27:27", "desc": "UJ: Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332287504, "paymentMap": [{"key": "1342526100:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1342526099, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": **********, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-12T11:27:27", "desc": "JN: International Advanced Passenger Information Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332287503, "paymentMap": [{"key": "1342526099:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": **********, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "CS: Aviation Security Fee", "comment": "CS: Aviation Security Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1332287503, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "JN: International Advanced Passenger Information Fee.", "comment": "JN: International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287503:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1332287504, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": **********, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "UJ: Passenger Service Charge", "comment": "UJ: Passenger Service Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287504:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1332287505, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287505:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1332287506, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287506:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1342526095, "codeType": "AIR", "amt": -310, "curr": "AED", "originalAmt": -310, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-12T11:27:27", "desc": "FZ 1736 ALA-DXB 16May2025 Fri 05:05 08:45\r\n", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1342526095:*********", "paymentID": *********, "amt": -310, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 310, "curr": "AED", "originalAmt": 310, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "FZ 1736 ALA-DXB 16May2025 Fri 05:05 08:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 310, "approveCode": 0}]}, {"chargeID": 1332287529, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:25", "billDate": "2025-05-05T07:58:46", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332287529:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342526096, "codeType": "XLGR", "amt": -177, "curr": "AED", "originalAmt": -177, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T08:11:19", "billDate": "2025-05-12T11:27:27", "desc": "Special Service Request:XLGR-16A", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332315233, "paymentMap": [{"key": "1342526096:*********", "paymentID": *********, "amt": -177, "approveCode": 0}], "PFID": "186408"}, {"chargeID": 1332315233, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T08:11:19", "billDate": "2025-05-05T08:11:27", "desc": "Special Service Request:XLGR-16A", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332315233:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "186408"}, {"chargeID": 1342526101, "codeType": "PNLT", "amt": 200, "curr": "AED", "originalAmt": 200, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T11:27:27", "desc": "Penalty AddedDueToModify FZ  1736 ALA  - DXB  16-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342526101:*********", "paymentID": *********, "amt": 200, "approveCode": 0}]}, {"chargeID": 1332287507, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1332287508, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-05T07:58:45", "billDate": "2025-05-05T07:58:46", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186408"}]}, {"recNum": 3, "charges": [{"chargeID": 1342526142, "codeType": "TAX", "taxID": 12491, "taxCode": "Q3", "taxChargeID": 1342526141, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "Passenger Terminal Use Charge", "comment": "Passenger Terminal Use Charge", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342526142:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-12T11:27:27"}, {"chargeID": 1342526144, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342526141, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342526144:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342526145, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342526141, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342526145:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1342526143, "codeType": "TAX", "taxID": 8685, "taxCode": "HL", "taxChargeID": 1342526141, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "Airport Development Tax", "comment": "Airport Development Tax", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342526143:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342526146, "codeType": "TAX", "taxID": 12489, "taxCode": "KG", "taxChargeID": 1342526141, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342526146:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1342526141, "codeType": "AIR", "amt": 475, "curr": "AED", "originalAmt": 475, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T11:27:27", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 16, "basePoints": 200, "tierPoints": 200, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1342526141:*********", "paymentID": *********, "amt": 258, "approveCode": 0}, {"key": "1342526141:*********", "paymentID": *********, "amt": 217, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1342531511, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-12T11:27:27", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181591", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1342531511:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181591", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1342526147, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1342526141, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1342526173, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-12T11:27:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181591", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}