{"seriesNum": "299", "PNR": "7FWMK0", "bookAgent": "MOBILE_APP", "resCurrency": "EUR", "PNRPin": "82519553", "bookDate": "2025-05-03T21:21:17", "modifyDate": "2025-05-29T13:35:33", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "446co15bx031ua03ke6asuu4f011aef3f7a659080b2a", "securityGUID": "446co15bx031ua03ke6asuu4f011aef3f7a659080b2a", "lastLoadGUID": "b37e2fb2-f0ee-47ad-b377-3d5707d9128d", "isAsyncPNR": false, "MasterPNR": "7FWMK0", "segments": [{"segKey": "16087827:16087827:5/29/2025 6:55:00 PM", "LFID": 16087827, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16087827", "org": "DXB", "dest": "BEG", "depTime": "2025-05-29T18:55:00", "depTimeGMT": "2025-05-29T14:55:00", "arrTime": "2025-05-29T22:45:00", "operCarrier": "FZ", "operFlightNum": "1749", "mrktCarrier": "FZ ", "mrktFlightNum": "1749", "persons": [{"recNum": 4, "status": 5}], "legDetails": [{"PFID": 181557, "depDate": "2025-05-29T18:55:00", "legKey": "16087827:181557:5/29/2025 6:55:00 PM", "customerKey": "A523B46C2C44156424D452B872B8EBD4A55A8E889E53BA7240EEB17DC85BAD07"}], "active": true, "changeType": "TK"}, {"segKey": "16087760:16087760:5/21/2025 1:55:00 PM", "LFID": 16087760, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087760", "org": "BEG", "dest": "DXB", "depTime": "2025-05-21T13:55:00", "depTimeGMT": "2025-05-21T11:55:00", "arrTime": "2025-05-21T21:15:00", "operCarrier": "FZ", "operFlightNum": "1746", "mrktCarrier": "FZ ", "mrktFlightNum": "1746", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181550, "depDate": "2025-05-21T13:55:00", "legKey": "16087760:181550:5/21/2025 1:55:00 PM", "customerKey": "CE9F9590B325ECC6D734C659329B464850D528CFFA04AB4D5D76344D4C0F2774"}], "active": true, "changeType": "TK"}, {"segKey": "16087760:16087760:5/20/2025 1:55:00 PM", "LFID": 16087760, "depDate": "2025-05-20T00:00:00", "flightGroupId": "16087760", "org": "BEG", "dest": "DXB", "depTime": "2025-05-20T13:55:00", "depTimeGMT": "2025-05-20T11:55:00", "arrTime": "2025-05-20T21:15:00", "operCarrier": "FZ", "operFlightNum": "1746", "mrktCarrier": "FZ ", "mrktFlightNum": "1746", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181550, "depDate": "2025-05-20T13:55:00", "legKey": "16087760:181550:5/20/2025 1:55:00 PM", "customerKey": "B2D3894D44014FB56BEC2C04EDA1290E9265E04FE86A858B1EC9991CA6FBFAAC"}], "active": true, "changeType": "TK"}, {"segKey": "16087759:16087759:5/29/2025 9:05:00 AM", "LFID": 16087759, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16087759", "org": "DXB", "dest": "BEG", "depTime": "2025-05-29T09:05:00", "depTimeGMT": "2025-05-29T05:05:00", "arrTime": "2025-05-29T12:55:00", "operCarrier": "FZ", "operFlightNum": "1745", "mrktCarrier": "FZ ", "mrktFlightNum": "1745", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181549, "depDate": "2025-05-29T09:05:00", "legKey": "16087759:181549:5/29/2025 9:05:00 AM", "customerKey": "7017657ED4ED7A860CD581FFA668A3106C1ED48A764EE98FD614F9897A5513CD"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 267801567, "fName": "ALEKSANDAR", "lName": "MILOVANOVIC", "title": "MR", "PTCID": 1, "gender": "M", "FFNum": "671795036", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "V", "status": 0, "fareClass": "V", "operFareClass": "V", "FBC": "VRL8RS5", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681687d50007780000004416#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-03T21:21:17"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "N", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRL8RS2", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682acb320007780000000aee#267801567#2#MOBILE#SFQE#CHANGE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-03T21:21:17"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "V", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VR6RS5", "fareBrand": "Lite", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682cdfcd0007780000003f89#267801567#1#MOBILE#SFQE#CHANGE", "fareTypeID": 21, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-19T06:21:23"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "L", "insuPurchasedate": "5/20/2025 8:04:58 PM", "provider": "<PERSON>", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LRL8RS5", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "4PDDJ-CK2DF-INS/ddbde97c-8177-49f7-a09d-daed1b2a49b6", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682cdfcd0007780000003f89#267801567#2#MOBILE#SFQE#CHANGE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-05-20T20:04:57"}]}], "payments": [{"paymentID": *********, "paxID": 267801567, "method": "VCHR", "status": "1", "paidDate": "2025-05-19T06:21:21", "voucherNum": 3262021, "paidCurr": "EUR", "paidAmt": -4.68, "baseCurr": "EUR", "baseAmt": -4.68, "userID": "MOBILE_APP", "channelID": 12, "tierID": "3", "voucherNumFull": "C29GN2", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 267801589, "method": "IPAY", "status": "1", "paidDate": "2025-05-03T21:21:22", "cardNum": "************3563", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 595.09, "baseCurr": "EUR", "baseAmt": 595.09, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "625877", "reference": "22819692", "externalReference": "22819692", "tranId": "21191816", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "SRBMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21191816}, {"paymentID": *********, "paxID": 269629690, "method": "IPAY", "status": "1", "paidDate": "2025-05-20T20:05:04", "cardNum": "************3563", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 11.87, "baseCurr": "EUR", "baseAmt": 11.87, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "734384", "reference": "23152118", "externalReference": "23152118", "tranId": "21531169", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "SRBMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21531169}], "OAFlights": null, "physicalFlights": [{"key": "16087760:181550:2025-05-20T01:55:00 PM", "LFID": 16087760, "PFID": 181550, "org": "BEG", "dest": "DXB", "depDate": "2025-05-20T13:55:00", "depTime": "2025-05-20T13:55:00", "arrTime": "2025-05-20T21:15:00", "carrier": "FZ", "flightNum": "1746", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1746", "flightStatus": "CLOSED", "originMetroGroup": "BEG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Belgrade", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:06:45 AM"}, {"key": "16087760:181550:2025-05-21T01:55:00 PM", "LFID": 16087760, "PFID": 181550, "org": "BEG", "dest": "DXB", "depDate": "2025-05-21T13:55:00", "depTime": "2025-05-21T13:55:00", "arrTime": "2025-05-21T21:15:00", "carrier": "FZ", "flightNum": "1746", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1746", "flightStatus": "CLOSED", "originMetroGroup": "BEG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Belgrade", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:06:45 AM"}, {"key": "16087759:181549:2025-05-29T09:05:00 AM", "LFID": 16087759, "PFID": 181549, "org": "DXB", "dest": "BEG", "depDate": "2025-05-29T09:05:00", "depTime": "2025-05-29T09:05:00", "arrTime": "2025-05-29T12:55:00", "carrier": "FZ", "flightNum": "1745", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1745", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BEG", "operatingCarrier": "FZ", "flightDuration": 21000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Belgrade", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:05:34 AM"}, {"key": "16087827:181557:2025-05-29T06:55:00 PM", "LFID": 16087827, "PFID": 181557, "org": "DXB", "dest": "BEG", "depDate": "2025-05-29T18:55:00", "depTime": "2025-05-29T18:55:00", "arrTime": "2025-05-29T22:45:00", "carrier": "FZ", "flightNum": "1749", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1749", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BEG", "operatingCarrier": "FZ", "flightDuration": 21000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Belgrade", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:12:47 AM"}], "chargeInfos": [{"recNum": 4, "charges": [{"chargeID": 1354864186, "codeType": "INSU", "amt": 8.62, "curr": "EUR", "originalAmt": 8.62, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-20T20:04:58", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354864186:*********", "paymentID": *********, "amt": 8.62, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-20T20:04:58"}, {"chargeID": 1354862428, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1354862427, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354862428:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1354862552, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1354862427, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354862552:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1354862549, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1354862427, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354862549:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1354862550, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1354862427, "amt": 18.29, "curr": "EUR", "originalAmt": 18.29, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354862550:*********", "paymentID": *********, "amt": 18.29, "approveCode": 0}]}, {"chargeID": 1354862551, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1354862427, "amt": 10.98, "curr": "EUR", "originalAmt": 10.98, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354862551:*********", "paymentID": *********, "amt": 10.98, "approveCode": 0}]}, {"chargeID": 1354862427, "codeType": "AIR", "amt": 181.5, "curr": "EUR", "originalAmt": 181.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-20T20:04:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 600, "tierPoints": 600, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1354862427:*********", "paymentID": *********, "amt": 181.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354864261, "codeType": "XLGR", "amt": 43.16, "curr": "EUR", "originalAmt": 43.16, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-20T20:04:58", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181557", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1354864261:*********", "paymentID": *********, "amt": 11.52, "approveCode": 0}, {"key": "1354864261:*********", "paymentID": *********, "amt": 31.64, "approveCode": 0}], "PFID": "181557", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354862554, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1354862427, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354862553, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1354862427, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354862560, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181557", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1367614600, "codeType": "CKIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-29T13:35:33", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181557", "ssrCommentId": "*********"}]}, {"recNum": 1, "charges": [{"chargeID": 1351968976, "codeType": "TAX", "taxID": 4964, "taxCode": "RF", "taxChargeID": 1330874136, "amt": -0.98, "curr": "EUR", "originalAmt": -0.98, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "CAD Passenger charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874139, "paymentMap": [{"key": "1351968976:*********", "paymentID": *********, "amt": -0.98, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-19T06:21:24"}, {"chargeID": 1351968972, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1330874136, "amt": -1.21, "curr": "EUR", "originalAmt": -1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874137, "paymentMap": [{"key": "1351968972:*********", "paymentID": *********, "amt": -1.21, "approveCode": 0}]}, {"chargeID": 1351968975, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1330874136, "amt": -75, "curr": "EUR", "originalAmt": -75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874140, "paymentMap": [{"key": "1351968975:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1351968977, "codeType": "TAX", "taxID": 9086, "taxCode": "LG", "taxChargeID": 1330874136, "amt": -6.47, "curr": "EUR", "originalAmt": -6.47, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "Security Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874142, "paymentMap": [{"key": "1351968977:*********", "paymentID": *********, "amt": -6.47, "approveCode": 0}]}, {"chargeID": 1351968973, "codeType": "TAX", "taxID": 8264, "taxCode": "RS", "taxChargeID": 1330874136, "amt": -21.49, "curr": "EUR", "originalAmt": -21.49, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874141, "paymentMap": [{"key": "1351968973:*********", "paymentID": *********, "amt": -21.49, "approveCode": 0}]}, {"chargeID": 1330874139, "codeType": "TAX", "taxID": 4964, "taxCode": "RF", "taxChargeID": 1330874136, "amt": 0.98, "curr": "EUR", "originalAmt": 0.98, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "CAD Passenger charge", "comment": "CAD Passenger charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874139:*********", "paymentID": *********, "amt": 0.98, "approveCode": 0}]}, {"chargeID": 1330874137, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1330874136, "amt": 1.21, "curr": "EUR", "originalAmt": 1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874137:*********", "paymentID": *********, "amt": 1.21, "approveCode": 0}]}, {"chargeID": 1330874141, "codeType": "TAX", "taxID": 8264, "taxCode": "RS", "taxChargeID": 1330874136, "amt": 21.49, "curr": "EUR", "originalAmt": 21.49, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874141:*********", "paymentID": *********, "amt": 21.49, "approveCode": 0}]}, {"chargeID": 1330874140, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1330874136, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874140:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1330874142, "codeType": "TAX", "taxID": 9086, "taxCode": "LG", "taxChargeID": 1330874136, "amt": 6.47, "curr": "EUR", "originalAmt": 6.47, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Security Charge", "comment": "Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874142:*********", "paymentID": *********, "amt": 6.47, "approveCode": 0}]}, {"chargeID": 1351968978, "codeType": "AIR", "amt": -101.5, "curr": "EUR", "originalAmt": -101.5, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-19T06:21:24", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874136, "paymentMap": [{"key": "1351968978:*********", "paymentID": *********, "amt": -96.82, "approveCode": 0}, {"key": "1351968978:*********", "paymentID": *********, "amt": -4.68, "approveCode": 0}]}, {"chargeID": 1330874136, "codeType": "AIR", "amt": 101.5, "curr": "EUR", "originalAmt": 101.5, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-03T21:21:17", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874136:*********", "paymentID": *********, "amt": 101.5, "approveCode": 0}]}, {"chargeID": 1330874729, "codeType": "PMNT", "amt": 17.33, "curr": "EUR", "originalAmt": 17.33, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-03T21:21:26", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874729:*********", "paymentID": *********, "amt": 17.33, "approveCode": 0}]}, {"chargeID": 1351968974, "codeType": "XLGR", "amt": -42.64, "curr": "EUR", "originalAmt": -42.64, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874175, "paymentMap": [{"key": "1351968974:*********", "paymentID": *********, "amt": -42.64, "approveCode": 0}], "PFID": "181550"}, {"chargeID": 1330874175, "codeType": "XLGR", "amt": 42.64, "curr": "EUR", "originalAmt": 42.64, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181550", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874175:*********", "paymentID": *********, "amt": 42.64, "approveCode": 0}], "PFID": "181550"}, {"chargeID": 1330874143, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1330874136, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1330874138, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1330874136, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1330874178, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181550"}]}, {"recNum": 2, "charges": [{"chargeID": 1354862522, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1330874145, "amt": -10.84, "curr": "EUR", "originalAmt": -10.84, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874170, "paymentMap": [{"key": "1354862522:*********", "paymentID": *********, "amt": -10.84, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-20T20:04:58"}, {"chargeID": 1354862521, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1330874145, "amt": -1.21, "curr": "EUR", "originalAmt": -1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874146, "paymentMap": [{"key": "1354862521:*********", "paymentID": *********, "amt": -1.21, "approveCode": 0}]}, {"chargeID": 1354862520, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1330874145, "amt": -18.07, "curr": "EUR", "originalAmt": -18.07, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874169, "paymentMap": [{"key": "1354862520:*********", "paymentID": *********, "amt": -18.07, "approveCode": 0}]}, {"chargeID": 1354862524, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1330874145, "amt": -1.21, "curr": "EUR", "originalAmt": -1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874171, "paymentMap": [{"key": "1354862524:*********", "paymentID": *********, "amt": -1.21, "approveCode": 0}]}, {"chargeID": 1354862519, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1330874145, "amt": -75, "curr": "EUR", "originalAmt": -75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874148, "paymentMap": [{"key": "1354862519:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1330874146, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1330874145, "amt": 1.21, "curr": "EUR", "originalAmt": 1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874146:*********", "paymentID": *********, "amt": 1.21, "approveCode": 0}]}, {"chargeID": 1330874169, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1330874145, "amt": 18.07, "curr": "EUR", "originalAmt": 18.07, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874169:*********", "paymentID": *********, "amt": 18.07, "approveCode": 0}]}, {"chargeID": 1330874148, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1330874145, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874148:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1330874170, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1330874145, "amt": 10.84, "curr": "EUR", "originalAmt": 10.84, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874170:*********", "paymentID": *********, "amt": 10.84, "approveCode": 0}]}, {"chargeID": 1330874171, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1330874145, "amt": 1.21, "curr": "EUR", "originalAmt": 1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874171:*********", "paymentID": *********, "amt": 1.21, "approveCode": 0}]}, {"chargeID": 1354862518, "codeType": "AIR", "amt": -179.5, "curr": "EUR", "originalAmt": -179.5, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-20T20:04:58", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874145, "paymentMap": [{"key": "1354862518:*********", "paymentID": *********, "amt": -179.5, "approveCode": 0}]}, {"chargeID": 1330874145, "codeType": "AIR", "amt": 179.5, "curr": "EUR", "originalAmt": 179.5, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-03T21:21:17", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874145:*********", "paymentID": *********, "amt": 179.5, "approveCode": 0}]}, {"chargeID": 1354862523, "codeType": "XLGR", "amt": -42.64, "curr": "EUR", "originalAmt": -42.64, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-20T20:04:58", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330874177, "paymentMap": [{"key": "1354862523:*********", "paymentID": *********, "amt": -42.64, "approveCode": 0}], "PFID": "181549"}, {"chargeID": 1330874177, "codeType": "XLGR", "amt": 42.64, "curr": "EUR", "originalAmt": 42.64, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181549", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330874177:*********", "paymentID": *********, "amt": 42.64, "approveCode": 0}], "PFID": "181549"}, {"chargeID": 1330874172, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1330874145, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1330874147, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1330874145, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1330874179, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T21:21:17", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181549"}]}, {"recNum": 3, "charges": [{"chargeID": 1351968958, "codeType": "TAX", "taxID": 9086, "taxCode": "LG", "taxChargeID": 1351968956, "amt": 6.47, "curr": "EUR", "originalAmt": 6.47, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "Security Charge", "comment": "Security Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351968958:*********", "paymentID": *********, "amt": 6.47, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-19T06:21:24"}, {"chargeID": 1351968959, "codeType": "TAX", "taxID": 4964, "taxCode": "RF", "taxChargeID": 1351968956, "amt": 0.98, "curr": "EUR", "originalAmt": 0.98, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "CAD Passenger charge", "comment": "CAD Passenger charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351968959:*********", "paymentID": *********, "amt": 0.98, "approveCode": 0}]}, {"chargeID": 1351968961, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1351968956, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351968961:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1351968957, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1351968956, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351968957:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1351968960, "codeType": "TAX", "taxID": 8264, "taxCode": "RS", "taxChargeID": 1351968956, "amt": 21.49, "curr": "EUR", "originalAmt": 21.49, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T06:21:24", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351968960:*********", "paymentID": *********, "amt": 21.49, "approveCode": 0}]}, {"chargeID": 1351968956, "codeType": "AIR", "amt": 101.5, "curr": "EUR", "originalAmt": 101.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-19T06:21:24", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1351968956:*********", "paymentID": *********, "amt": 101.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354865615, "codeType": "PMNT", "amt": 0.35, "curr": "EUR", "originalAmt": 0.35, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-20T20:05:09", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354865615:*********", "paymentID": *********, "amt": 0.35, "approveCode": 0}]}, {"chargeID": 1351983216, "codeType": "XLGR", "amt": 37.95, "curr": "EUR", "originalAmt": 37.95, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-19T06:21:24", "desc": "XLGR", "comment": "FLXID:XLGR_EMER_ZONE3_MID::181550", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1351983216:*********", "paymentID": *********, "amt": 37.95, "approveCode": 0}], "PFID": "181550", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}