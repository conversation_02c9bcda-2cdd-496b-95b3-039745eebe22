{"seriesNum": "299", "PNR": "RTO322", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82802130", "bookDate": "2025-05-13T19:38:08", "modifyDate": "2025-05-14T13:27:39", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "5eadab33600943e4a8nxl9u0u7s66408764c9ec5e816", "securityGUID": "5eadab33600943e4a8nxl9u0u7s66408764c9ec5e816", "lastLoadGUID": "385665ab-7289-4a7e-98c1-26456951a4ff", "isAsyncPNR": false, "MasterPNR": "RTO322", "segments": [{"segKey": "16066159:16066159:5/14/2025 7:40:00 PM", "LFID": 16066159, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16066159", "org": "DXB", "dest": "DOH", "depTime": "2025-05-14T19:40:00", "depTimeGMT": "2025-05-14T15:40:00", "arrTime": "2025-05-14T19:50:00", "operCarrier": "FZ", "operFlightNum": "005", "mrktCarrier": "FZ ", "mrktFlightNum": "005", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181000, "depDate": "2025-05-14T19:40:00", "legKey": "16066159:181000:5/14/2025 7:40:00 PM", "customerKey": "9196095FE3BDE6ABEE29E458FE061365D7E9C4C45ABC650E7A06727B89BCD2B0"}], "active": true, "changeType": "TK"}, {"segKey": "16087271:16087271:5/15/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-05-15T17:05:00", "depTimeGMT": "2025-05-15T14:05:00", "arrTime": "2025-05-15T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ ", "mrktFlightNum": "018", "persons": [{"recNum": 2, "status": 0}, {"recNum": 4, "status": 5}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-15T17:05:00", "legKey": "16087271:181022:5/15/2025 5:05:00 PM", "customerKey": "7B8440B666CC584FD1D10F12C7B34E219113DE400F6743D5407A1DE9A13D6AAC"}], "active": true, "changeType": "TK"}, {"segKey": "16087263:16087263:5/14/2025 9:15:00 PM", "LFID": 16087263, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087263", "org": "DXB", "dest": "DOH", "depTime": "2025-05-14T21:15:00", "depTimeGMT": "2025-05-14T17:15:00", "arrTime": "2025-05-14T21:25:00", "operCarrier": "FZ", "operFlightNum": "019", "mrktCarrier": "FZ", "mrktFlightNum": "019", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181014, "depDate": "2025-05-14T21:15:00", "legKey": "16087263:181014:5/14/2025 9:15:00 PM", "customerKey": "84F35DA48E1996064C0C14A39CCE17339212F65B7174F6F2902EC3FC38E85372"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268877811, "fName": "CEM", "lName": "OZKAYA", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1976-01-25T00:00:00", "nationality": "792", "FFNum": "109222201", "FFTier": "SILVER", "TierID": "4", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/13/2025 7:38:08 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "discloseEmergencyContact": 0, "insuConfNum": "GDKXW-UPVR8-INS", "insuTransID": "GDKXW-UPVR8-INS/8384e6f9-a9b2-4ac2-b855-9b7cf2eac346", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68239f0b0007780000009599#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-13T19:38:08"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "O", "insuPurchasedate": "5/13/2025 7:38:08 PM", "provider": "<PERSON>", "status": 0, "fareClass": "O", "operFareClass": "O", "FBC": "ORB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "discloseEmergencyContact": 0, "insuConfNum": "GDKXW-UPVR8-INS", "insuTransID": "GDKXW-UPVR8-INS/8384e6f9-a9b2-4ac2-b855-9b7cf2eac346", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68239f0b0007780000009599#1#2#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 30, "bookDate": "2025-05-13T19:38:08"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "saood.hossain", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/13/2025 7:38:08 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "GDKXW-UPVR8-INS/8384e6f9-a9b2-4ac2-b855-9b7cf2eac346", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68249759000778000000354b#268877811#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-14T13:18:53"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "saood.hossain", "statusReasonID": 0, "markFareClass": "O", "insuPurchasedate": "5/13/2025 7:38:08 PM", "provider": "<PERSON>", "status": 5, "fareClass": "O", "operFareClass": "O", "FBC": "ORB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "GDKXW-UPVR8-INS/8384e6f9-a9b2-4ac2-b855-9b7cf2eac346", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68249759000778000000354b#268877811#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-14T13:18:53"}]}], "payments": [{"paymentID": *********, "paxID": 268904629, "method": "IPAY", "status": "1", "paidDate": "2025-05-14T06:06:08", "cardNum": "************7889", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 98.88, "baseCurr": "AED", "baseAmt": 98.88, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "authCode": "599352", "reference": "23023757", "externalReference": "23023757", "tranId": "21395497", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21395497}, {"paymentID": *********, "paxID": 268877865, "method": "VISA", "status": "1", "paidDate": "2025-05-13T19:38:31", "cardNum": "************6824", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2621.04, "baseCurr": "AED", "baseAmt": 2621.04, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "CEM OZKAYA", "authCode": "599349", "reference": "23017220", "externalReference": "23017220", "tranId": "21391520", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21391520}, {"paymentID": *********, "paxID": 268966446, "method": "IPAY", "status": "1", "paidDate": "2025-05-14T13:20:04", "cardNum": "************7889", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 680.83, "baseCurr": "AED", "baseAmt": 680.83, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "599355", "reference": "23029764", "externalReference": "23029764", "tranId": "21406297", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21406297}, {"paymentID": *********, "paxID": 268967365, "method": "IPAY", "status": "1", "paidDate": "2025-05-14T13:27:32", "cardNum": "************7889", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 49.44, "baseCurr": "AED", "baseAmt": 49.44, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "authCode": "599356", "reference": "23029838", "externalReference": "23029838", "tranId": "21406424", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21406424}], "OAFlights": null, "physicalFlights": [{"key": "16066159:181000:2025-05-14T07:40:00 PM", "LFID": 16066159, "PFID": 181000, "org": "DXB", "dest": "DOH", "depDate": "2025-05-14T19:40:00", "depTime": "2025-05-14T19:40:00", "arrTime": "2025-05-14T19:50:00", "carrier": "FZ", "flightNum": "005", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "005", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "TK", "flightChangeTime": "11/29/2024 4:28:29 AM"}, {"key": "16087263:181014:2025-05-14T09:15:00 PM", "LFID": 16087263, "PFID": 181014, "org": "DXB", "dest": "DOH", "depDate": "2025-05-14T21:15:00", "depTime": "2025-05-14T21:15:00", "arrTime": "2025-05-14T21:25:00", "carrier": "FZ", "flightNum": "019", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "019", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "AC", "flightChangeTime": "5/5/2025 9:35:10 AM"}, {"key": "16087271:181022:2025-05-15T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-15T17:05:00", "depTime": "2025-05-15T17:05:00", "arrTime": "2025-05-15T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "3/10/2025 10:39:22 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1344949600, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949600:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-13T19:38:07"}, {"chargeID": 1346097483, "codeType": "INSU", "taxChargeID": 1346097479, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:54", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949600, "paymentMap": [{"key": "1346097483:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": *********2, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": *********1, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********2:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": *********3, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********1, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********3:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": *********5, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********1, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********5:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********6, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********1, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********6:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********4, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": *********1, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********4:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********7, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********1, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********7:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1346097480, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346097479, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:54", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********2, "paymentMap": [{"key": "1346097480:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1346097482, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346097479, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:54", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********7, "paymentMap": [{"key": "1346097482:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1346097484, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346097479, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:54", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********4, "paymentMap": [{"key": "1346097484:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346097485, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346097479, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:54", "desc": "Passenger Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********6, "paymentMap": [{"key": "1346097485:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1346097487, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346097479, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********5, "paymentMap": [{"key": "1346097487:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346097488, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346097479, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********3, "paymentMap": [{"key": "1346097488:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": *********1, "codeType": "AIR", "amt": 445, "curr": "AED", "originalAmt": 445, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T19:38:08", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********1:*********", "paymentID": *********, "amt": 445, "approveCode": 0}]}, {"chargeID": 1346097479, "codeType": "AIR", "amt": -445, "curr": "AED", "originalAmt": -445, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T13:18:54", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********1, "paymentMap": [{"key": "1346097479:*********", "paymentID": *********, "amt": -445, "approveCode": 0}]}, {"chargeID": 1345292770, "codeType": "PMNT", "amt": 2.88, "curr": "AED", "originalAmt": 2.88, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T06:06:12", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345292770:*********", "paymentID": *********, "amt": 2.88, "approveCode": 0}]}, {"chargeID": 1344951021, "codeType": "PMNT", "amt": 76.34, "curr": "AED", "originalAmt": 76.34, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T19:38:35", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344951021:*********", "paymentID": *********, "amt": 76.34, "approveCode": 0}]}, {"chargeID": 1345292174, "codeType": "SPST", "amt": 48, "curr": "AED", "originalAmt": 48, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T06:05:39", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345292174:*********", "paymentID": *********, "amt": 48, "approveCode": 0}], "PFID": "181000", "ssrCommentId": "135833880"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 525, "curr": "AED", "originalAmt": 525, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:55", "billDate": "2025-05-14T13:18:55", "desc": "CancelNoRefund FZ 005 DXB - DOH 14.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 525, "approveCode": 0}]}, {"chargeID": *********8, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********1, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346097481, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1346097479, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:54", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********8, "paymentMap": []}, {"chargeID": 1344949603, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181000"}, {"chargeID": 1346097486, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346097479, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949603, "paymentMap": [], "PFID": "181000"}]}, {"recNum": 2, "charges": [{"chargeID": 1344949602, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949602:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-13T19:38:07"}, {"chargeID": 1346097512, "codeType": "INSU", "taxChargeID": 1346097510, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "INSU", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949602, "paymentMap": [{"key": "1346097512:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1344949591, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344949590, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949591:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1344949596, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1344949590, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949596:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1344949593, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344949590, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949593:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1344949595, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1344949590, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949595:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1344949594, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1344949590, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949594:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1344949592, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1344949590, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949592:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346097511, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1346097510, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Passenger Facility Charge.", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949594, "paymentMap": [{"key": "1346097511:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1346097513, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1346097510, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Airport Fee.", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949595, "paymentMap": [{"key": "1346097513:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1346097514, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346097510, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "YQ - DUMMY", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949593, "paymentMap": [{"key": "1346097514:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1346097516, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346097510, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Advanced passenger information fee", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949591, "paymentMap": [{"key": "1346097516:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346097519, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346097510, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:56", "desc": "Passenger Service Charge", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949592, "paymentMap": [{"key": "1346097519:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1346097520, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1346097510, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:56", "desc": "Passenger safety and security fees (PSSF)", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949596, "paymentMap": [{"key": "1346097520:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1344949590, "codeType": "AIR", "amt": 1599, "curr": "AED", "originalAmt": 1599, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T19:38:08", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344949590:*********", "paymentID": *********, "amt": 1599, "approveCode": 0}]}, {"chargeID": 1346097510, "codeType": "AIR", "amt": -1599, "curr": "AED", "originalAmt": -1599, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T13:18:55", "desc": "WEB:AIR", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949590, "paymentMap": [{"key": "1346097510:*********", "paymentID": *********, "amt": -1599, "approveCode": 0}]}, {"chargeID": 1345292175, "codeType": "SPST", "amt": 48, "curr": "AED", "originalAmt": 48, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T06:05:39", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345292175:*********", "paymentID": *********, "amt": 48, "approveCode": 0}], "PFID": "181022", "ssrCommentId": "*********"}, {"chargeID": 1346097518, "codeType": "SPST", "taxChargeID": 1346097510, "amt": -48, "curr": "AED", "originalAmt": -48, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T13:18:55", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345292175, "paymentMap": [{"key": "1346097518:*********", "paymentID": *********, "amt": -48, "approveCode": 0}], "PFID": "181022", "ssrCommentId": "*********"}, {"chargeID": 1344949597, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1344949590, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346097517, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1346097510, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949597, "paymentMap": []}, {"chargeID": 1344949604, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T19:38:08", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1346097515, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346097510, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T13:18:55", "desc": "Standard meal", "comment": "MOD", "reasonID": 30, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1344949604, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 3, "charges": [{"chargeID": 1346097538, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:15:15", "billDate": "2025-05-14T13:18:56", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097538:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T13:18:56"}, {"chargeID": 1346097529, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346097528, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097529:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1346097530, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346097528, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097530:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1346097531, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346097528, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097531:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346097532, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346097528, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097532:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346097533, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346097528, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097533:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346097534, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346097528, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097534:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1346097528, "codeType": "AIR", "amt": 475, "curr": "AED", "originalAmt": 475, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "FZ 019 DXB-DOH 14May2025 Wed 21:15 21:25\r\n", "reasonID": 2, "channelID": 1, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 40, "PromoTier": 0, "paymentMap": [{"key": "1346097528:*********", "paymentID": *********, "amt": 475, "approveCode": 0}], "bonusMiles": 40, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346111756, "codeType": "PMNT", "amt": 1.44, "curr": "AED", "originalAmt": 1.44, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T13:27:39", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346111756:*********", "paymentID": *********, "amt": 1.44, "approveCode": 0}]}, {"chargeID": 1346100598, "codeType": "PMNT", "amt": 19.83, "curr": "AED", "originalAmt": 19.83, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T13:20:10", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346100598:*********", "paymentID": *********, "amt": 19.83, "approveCode": 0}]}, {"chargeID": 1346097537, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097537:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1346108948, "codeType": "SPST", "amt": 48, "curr": "AED", "originalAmt": 48, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T13:25:41", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1346108948:*********", "paymentID": *********, "amt": 48, "approveCode": 0}], "PFID": "181014", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1346097535, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1346097528, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346097536, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346097528, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181014", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1346097557, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:15:15", "billDate": "2025-05-14T13:18:57", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097557:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T13:18:57"}, {"chargeID": 1346097546, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346097539, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:57", "billDate": "2025-05-14T13:18:57", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097546:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346097551, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346097539, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:57", "billDate": "2025-05-14T13:18:57", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097551:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1346097542, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1346097539, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "G4: Passenger Facility Charge.", "comment": "G4: Passenger Facility Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097542:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1346097543, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1346097539, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "QA: Airport Fee.", "comment": "QA: Airport Fee.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097543:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1346097544, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1346097539, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "R9: Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097544:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346097545, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346097539, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:57", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097545:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346097539, "codeType": "AIR", "amt": 1645, "curr": "AED", "originalAmt": 1645, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "desc": "FZ 018 DOH-DXB 15May2025 Thu 17:05 19:20\r\n", "reasonID": 2, "channelID": 1, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 40, "PromoTier": 0, "paymentMap": [{"key": "1346097539:*********", "paymentID": *********, "amt": 1258.85, "approveCode": 0}, {"key": "1346097539:*********", "paymentID": *********, "amt": 48, "approveCode": 0}, {"key": "1346097539:*********", "paymentID": *********, "amt": 338.15, "approveCode": 0}], "bonusMiles": 40, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346109149, "codeType": "SPST", "amt": 48, "curr": "AED", "originalAmt": 48, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T13:25:46", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1346109149:*********", "paymentID": *********, "amt": 48, "approveCode": 0}], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346109108, "codeType": "SPST", "amt": -48, "curr": "AED", "originalAmt": -48, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T13:25:46", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346097541, "paymentMap": [{"key": "1346109108:*********", "paymentID": *********, "amt": -48, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1346097541, "codeType": "SPST", "amt": 48, "curr": "AED", "originalAmt": 48, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:56", "billDate": "2025-05-14T13:18:56", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346097541:*********", "paymentID": *********, "amt": 48, "approveCode": 0}], "PFID": "181022", "ssrCommentId": "*********"}, {"chargeID": 1346097555, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1346097539, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:57", "billDate": "2025-05-14T13:18:57", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346097556, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346097539, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T13:18:57", "billDate": "2025-05-14T13:18:57", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}