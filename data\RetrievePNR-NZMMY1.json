{"seriesNum": "299", "PNR": "NZMMY1", "bookAgent": "WEB2_LIVE", "resCurrency": "USD", "PNRPin": "81304331", "bookDate": "2025-03-19T07:49:01", "modifyDate": "2025-05-23T16:25:51", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "c7a857h2e7h45eu6ef5a640cg6ia841d271e3d17b562", "securityGUID": "c7a857h2e7h45eu6ef5a640cg6ia841d271e3d17b562", "lastLoadGUID": "d4236c7f-c812-4739-b27e-788969d879f0", "isAsyncPNR": false, "MasterPNR": "NZMMY1", "segments": [{"segKey": "16087504:16087504:5/24/2025 2:05:00 PM", "LFID": 16087504, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16087504", "org": "SAW", "dest": "DXB", "depTime": "2025-05-24T14:05:00", "depTimeGMT": "2025-05-24T11:05:00", "arrTime": "2025-05-24T19:35:00", "operCarrier": "FZ", "operFlightNum": "752", "mrktCarrier": "FZ ", "mrktFlightNum": "752", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181255, "depDate": "2025-05-24T14:05:00", "legKey": "16087504:181255:5/24/2025 2:05:00 PM", "customerKey": "16BE2421963AC600598BCD253757CC870996193DCE0C7515D71A97F5E9EB772C"}], "active": true, "changeType": "AC"}, {"segKey": "16087490:16087490:6/1/2025 9:15:00 AM", "LFID": 16087490, "depDate": "2025-06-01T00:00:00", "flightGroupId": "16087490", "org": "DXB", "dest": "SAW", "depTime": "2025-06-01T09:15:00", "depTimeGMT": "2025-06-01T05:15:00", "arrTime": "2025-06-01T13:05:00", "operCarrier": "FZ", "operFlightNum": "751", "mrktCarrier": "FZ ", "mrktFlightNum": "751", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181221, "depDate": "2025-06-01T09:15:00", "legKey": "16087490:181221:6/1/2025 9:15:00 AM", "customerKey": "979D54A73B75A1A10572F1682A492A64234D547BACEBDA1E387FBF277A39DA36"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 263017907, "fName": "MEHMET NURI", "lName": "KUTUKCU", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1980-02-28T00:00:00", "FFNum": "668061936", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "U", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7TR5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "67da75c6000778000003a3be#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "bookDate": "2025-03-19T07:49:01"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/23/2025 4:25:42 PM", "provider": "<PERSON>", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7TR2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "MM3CE-BTB4W-INS/ef20ff89-276d-470c-9ae9-013fa83d4bf0", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "67da75c6000778000003a3be#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-03-19T07:49:01"}]}], "payments": [{"paymentID": *********, "paxID": 263018042, "method": "MSCD", "status": "1", "paidDate": "2025-03-19T07:49:54", "cardNum": "************9010", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 564.56, "baseCurr": "USD", "baseAmt": 564.56, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "890100", "reference": "21870138", "externalReference": "21870138", "tranId": "20259782", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 20259782}, {"paymentID": *********, "paxID": 269952198, "method": "MSCD", "status": "1", "paidDate": "2025-05-23T16:25:48", "cardNum": "************9010", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 10.01, "baseCurr": "USD", "baseAmt": 10.01, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "754547", "reference": "23208861", "externalReference": "23208861", "tranId": "21590302", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 21590302}], "OAFlights": null, "physicalFlights": [{"key": "16087504:181255:2025-05-24T02:05:00 PM", "LFID": 16087504, "PFID": 181255, "org": "SAW", "dest": "DXB", "depDate": "2025-05-24T14:05:00", "depTime": "2025-05-24T14:05:00", "arrTime": "2025-05-24T19:35:00", "carrier": "FZ", "flightNum": "752", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "752", "flightStatus": "CLOSED", "originMetroGroup": "IST", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16200, "reaccomChangeAlert": false, "originName": "Istanbul Sabiha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:56:27 AM"}, {"key": "16087490:181221:2025-06-01T09:15:00 AM", "LFID": 16087490, "PFID": 181221, "org": "DXB", "dest": "SAW", "depDate": "2025-06-01T09:15:00", "depTime": "2025-06-01T09:15:00", "arrTime": "2025-06-01T13:05:00", "carrier": "FZ", "flightNum": "751", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "751", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Sabiha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:57:09 AM"}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1359169178, "codeType": "INSU", "amt": 9.72, "curr": "USD", "originalAmt": 9.72, "originalCurr": "USD", "status": 1, "exchRate": 1, "billDate": "2025-05-23T16:25:42", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359169178:*********", "paymentID": *********, "amt": 9.72, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.0\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-23T16:25:42"}, {"chargeID": 1269677047, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1269677045, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677047:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1269677049, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1269677045, "amt": 20.4, "curr": "USD", "originalAmt": 20.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677049:*********", "paymentID": *********, "amt": 20.4, "approveCode": 0}]}, {"chargeID": 1269677051, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1269677045, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677051:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1269677048, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1269677045, "amt": 81.76, "curr": "USD", "originalAmt": 81.76, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677048:*********", "paymentID": *********, "amt": 81.76, "approveCode": 0}]}, {"chargeID": 1269677050, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1269677045, "amt": 12.3, "curr": "USD", "originalAmt": 12.3, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677050:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}]}, {"chargeID": 1269677045, "codeType": "AIR", "amt": 249, "curr": "USD", "originalAmt": 249, "originalCurr": "USD", "status": 1, "billDate": "2025-03-19T07:49:01", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1269677045:*********", "paymentID": *********, "amt": 249, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1269677046, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1269677045, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1269677056, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": 1269677021, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1269677018, "amt": 16.4, "curr": "USD", "originalAmt": 16.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677021:*********", "paymentID": *********, "amt": 16.4, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-03-19T07:49:01"}, {"chargeID": 1269677022, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1269677018, "amt": 3.3, "curr": "USD", "originalAmt": 3.3, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677022:*********", "paymentID": *********, "amt": 3.3, "approveCode": 0}]}, {"chargeID": 1269677020, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1269677018, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677020:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1269677023, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1269677018, "amt": 81.76, "curr": "USD", "originalAmt": 81.76, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269677023:*********", "paymentID": *********, "amt": 81.76, "approveCode": 0}]}, {"chargeID": 1269677018, "codeType": "AIR", "amt": 79, "curr": "USD", "originalAmt": 79, "originalCurr": "USD", "status": 1, "billDate": "2025-03-19T07:49:01", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1269677018:*********", "paymentID": *********, "amt": 79, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1269681810, "codeType": "PMNT", "amt": 16.44, "curr": "USD", "originalAmt": 16.44, "originalCurr": "USD", "status": 1, "billDate": "2025-03-19T07:49:59", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1269681810:*********", "paymentID": *********, "amt": 16.44, "approveCode": 0}]}, {"chargeID": 1359171913, "codeType": "PMNT", "amt": 0.29, "curr": "USD", "originalAmt": 0.29, "originalCurr": "USD", "status": 1, "billDate": "2025-05-23T16:25:51", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359171913:*********", "paymentID": *********, "amt": 0.29, "approveCode": 0}]}, {"chargeID": 1269677019, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1269677018, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1269677055, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-03-19T07:49:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181255", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}