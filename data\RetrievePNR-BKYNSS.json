{"seriesNum": "299", "PNR": "BKYNSS", "bookAgent": "apit<PERSON>ae", "IATA": "95007517", "resCurrency": "AED", "PNRPin": "82448701", "bookDate": "2025-04-30T21:29:13", "modifyDate": "2025-05-17T18:12:56", "resType": "TPAPI", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "4ed25440t4l977n7u4d7vf61g3nbfe151a3bdeee991e", "securityGUID": "4ed25440t4l977n7u4d7vf61g3nbfe151a3bdeee991e", "lastLoadGUID": "2eac9920-13a8-40a5-bb3e-40575d134029", "isAsyncPNR": false, "MasterPNR": "BKYNSS", "segments": [{"segKey": "16087474:16087474:5/18/2025 6:05:00 AM", "LFID": 16087474, "depDate": "2025-05-18T00:00:00", "flightGroupId": "16087474", "org": "TBS", "dest": "DXB", "depTime": "2025-05-18T06:05:00", "depTimeGMT": "2025-05-18T02:05:00", "arrTime": "2025-05-18T09:15:00", "operCarrier": "FZ", "operFlightNum": "714", "mrktCarrier": "FZ", "mrktFlightNum": "714", "persons": [{"recNum": 5, "status": 5}, {"recNum": 6, "status": 5}], "legDetails": [{"PFID": 181235, "depDate": "2025-05-18T06:05:00", "legKey": "16087474:181235:5/18/2025 6:05:00 AM", "customerKey": "17DEA212BC649955404D345C00A0645169F0B903D17BFC99EE985A333877A37D"}], "active": true, "changeType": "TK"}, {"segKey": "16087456:16087456:5/5/2025 1:40:00 AM", "LFID": 16087456, "depDate": "2025-05-05T00:00:00", "flightGroupId": "16087456", "org": "DXB", "dest": "TBS", "depTime": "2025-05-05T01:40:00", "depTimeGMT": "2025-05-04T21:40:00", "arrTime": "2025-05-05T05:05:00", "operCarrier": "FZ", "operFlightNum": "713", "mrktCarrier": "FZ ", "mrktFlightNum": "713", "persons": [{"recNum": 1, "status": 5}, {"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181207, "depDate": "2025-05-05T01:40:00", "legKey": "16087456:181207:5/5/2025 1:40:00 AM", "customerKey": "B39C45293135ED2878BBA5453FED86510A4B4A82E6E428FCCA85EF40B36C1D21"}], "active": true, "changeType": "TK"}, {"segKey": "16087476:16087476:5/14/2025 4:35:00 PM", "LFID": 16087476, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087476", "org": "TBS", "dest": "DXB", "depTime": "2025-05-14T16:35:00", "depTimeGMT": "2025-05-14T12:35:00", "arrTime": "2025-05-14T19:45:00", "operCarrier": "FZ", "operFlightNum": "712", "mrktCarrier": "FZ ", "mrktFlightNum": "712", "persons": [{"recNum": 3, "status": 0}, {"recNum": 4, "status": 0}], "legDetails": [{"PFID": 181237, "depDate": "2025-05-14T16:35:00", "legKey": "16087476:181237:5/14/2025 4:35:00 PM", "customerKey": "2D0461935EF96C2FF7EB751E31EABE9D92C0D907DC718B0D8541D6B110C76F42"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 267520193, "fName": "ELHAM", "lName": "REISI", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1980-10-24T00:00:00", "recNum": [1, 3, 5]}, {"paxID": 267520194, "fName": "HARB", "lName": "HARB", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1994-10-16T00:00:00", "recNum": [2, 4, 6]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "apit<PERSON>ae", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/4/2025 7:52:56 PM", "provider": "AIG", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "UR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "emergencyContactID": 267790806, "discloseEmergencyContact": 1, "insuConfNum": "987174551", "insuTransID": "1cf09d5d-f878-45b5-98ef-8d26119dd5f0", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68129528000777000000d85d#1#1#TPAPI#VAYANT#CREATE", "fareTypeID": 11, "channelID": 5, "bookDate": "2025-04-30T21:29:13"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "apit<PERSON>ae", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/4/2025 7:52:56 PM", "provider": "AIG", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "UR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "emergencyContactID": 267790830, "discloseEmergencyContact": 1, "insuConfNum": "987174551", "insuTransID": "1cf09d5d-f878-45b5-98ef-8d26119dd5f0", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68129528000777000000d85d#1#1#TPAPI#VAYANT#CREATE", "fareTypeID": 11, "channelID": 5, "bookDate": "2025-04-30T21:29:13"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "apit<PERSON>ae", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/4/2025 7:52:56 PM", "provider": "AIG", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KR6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987174551", "insuTransID": "1cf09d5d-f878-45b5-98ef-8d26119dd5f0", "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68129528000777000000d85d#1#2#TPAPI#VAYANT#CREATE", "fareTypeID": 11, "channelID": 5, "cancelReasonID": 1, "bookDate": "2025-04-30T21:29:13"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "apit<PERSON>ae", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/4/2025 7:52:56 PM", "provider": "AIG", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KR6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987174551", "insuTransID": "1cf09d5d-f878-45b5-98ef-8d26119dd5f0", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68129528000777000000d85d#1#2#TPAPI#VAYANT#CREATE", "fareTypeID": 11, "channelID": 5, "cancelReasonID": 1, "bookDate": "2025-04-30T21:29:13"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "noon.arbab", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/10/2025 11:53:47 AM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "UR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "emergencyContactID": 269306105, "discloseEmergencyContact": 1, "insuTransID": "QK738-Z3<PERSON>R-INS/771ff4a5-0328-4d99-9f40-882676e8bb39", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f3d2e0007770000008d50#267520193#2#ENT#VAYANT#CHANGE", "fareTypeID": 11, "channelID": 1, "bookDate": "2025-05-10T11:53:43"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "noon.arbab", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/10/2025 11:53:47 AM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "UR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "emergencyContactID": 269306106, "discloseEmergencyContact": 1, "insuTransID": "QK738-Z3<PERSON>R-INS/771ff4a5-0328-4d99-9f40-882676e8bb39", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f3d2e0007770000008d50#267520194#2#ENT#VAYANT#CHANGE", "fareTypeID": 11, "channelID": 1, "bookDate": "2025-05-10T11:53:43"}]}], "payments": [{"paymentID": *********, "paxID": 267790801, "method": "IPAY", "status": "1", "paidDate": "2025-05-03T17:58:48", "cardNum": "************1739", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 63.86, "baseCurr": "AED", "baseAmt": 63.86, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "authCode": "592893", "reference": "22817694", "externalReference": "22817694", "tranId": "21189593", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21189593}, {"paymentID": *********, "paxID": 267789312, "method": "IPAY", "status": "1", "paidDate": "2025-05-03T17:38:53", "cardNum": "************1739", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 53.56, "baseCurr": "AED", "baseAmt": 53.56, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "592892", "reference": "22817463", "externalReference": "22817463", "tranId": "21189275", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21189275}, {"paymentID": *********, "paxID": 267852755, "method": "IPAY", "status": "1", "paidDate": "2025-05-04T13:38:59", "cardNum": "************1739", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 82.4, "baseCurr": "AED", "baseAmt": 82.4, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "592897", "reference": "22830659", "externalReference": "22830659", "tranId": "21202037", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21202037}, {"paymentID": *********, "paxID": 267520194, "method": "APOS", "status": "1", "paidDate": "2025-05-04T20:00:19", "paidCurr": "AED", "paidAmt": 176.6, "baseCurr": "AED", "baseAmt": 176.6, "userID": "cashier.nib4", "channelID": 1, "paymentComment": "20343", "authCode": "592900", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}, {"paymentID": *********, "paxID": 99431511, "method": "INVC", "status": "1", "paidDate": "2025-04-30T21:29:16", "IATANum": "95007517", "paidCurr": "AED", "paidAmt": 2670, "baseCurr": "AED", "baseAmt": 2670, "userID": "apit<PERSON>ae", "channelID": 5, "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1", "resExternalPaymentID": 1}, {"paymentID": 209381469, "paxID": 269306055, "method": "IPAY", "status": "1", "paidDate": "2025-05-17T18:12:50", "cardNum": "************1739", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 63.86, "baseCurr": "AED", "baseAmt": 63.86, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "authCode": "592969", "reference": "23093562", "externalReference": "23093562", "tranId": "21470174", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21470174}, {"paymentID": 209371986, "paxID": 269296982, "method": "IPAY", "status": "1", "paidDate": "2025-05-17T15:50:56", "cardNum": "************1739", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 199.82, "baseCurr": "AED", "baseAmt": 199.82, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "592968", "reference": "23092132", "externalReference": "23092132", "tranId": "21468202", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21468202}, {"paymentID": *********, "paxID": 268504767, "method": "IPAY", "status": "1", "paidDate": "2025-05-10T11:56:37", "cardNum": "************1739", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1371.34, "baseCurr": "AED", "baseAmt": 1371.34, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "592935", "reference": "22950631", "externalReference": "22950631", "tranId": "21322081", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21322081}], "OAFlights": null, "physicalFlights": [{"key": "16087456:181207:2025-05-05T01:40:00 AM", "LFID": 16087456, "PFID": 181207, "org": "DXB", "dest": "TBS", "depDate": "2025-05-05T01:40:00", "depTime": "2025-05-05T01:40:00", "arrTime": "2025-05-05T05:05:00", "carrier": "FZ", "flightNum": "713", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "713", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:49 PM"}, {"key": "16087476:181237:2025-05-14T04:35:00 PM", "LFID": 16087476, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-05-14T16:35:00", "depTime": "2025-05-14T16:35:00", "arrTime": "2025-05-14T19:45:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "4/25/2025 12:37:17 PM"}, {"key": "16087474:181235:2025-05-18T06:05:00 AM", "LFID": 16087474, "PFID": 181235, "org": "TBS", "dest": "DXB", "depDate": "2025-05-18T06:05:00", "depTime": "2025-05-18T06:05:00", "arrTime": "2025-05-18T09:15:00", "carrier": "FZ", "flightNum": "714", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "714", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:26 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1331892057, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-04T19:56:30", "billDate": "2025-05-04T19:57:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1331892057:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-04T19:57:06"}, {"chargeID": 1327372793, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1327372791, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372793:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1327372792, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1327372791, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372792:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1327372795, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1327372791, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372795:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327372796, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1327372791, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372796:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327372797, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1327372791, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372797:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1331892058, "codeType": "AFEE", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-04T19:56:50", "billDate": "2025-05-04T19:57:06", "desc": "Special Service Request", "comment": "AIRPORT ACCESS FEES", "reasonID": 12, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1331892058:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1327372791, "codeType": "AIR", "amt": 380, "curr": "AED", "originalAmt": 380, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T21:29:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372791:*********", "paymentID": *********, "amt": 380, "approveCode": 0}]}, {"chargeID": 1331533042, "codeType": "PMNT", "amt": 2.4, "curr": "AED", "originalAmt": 2.4, "originalCurr": "AED", "status": 1, "billDate": "2025-05-04T13:39:04", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1331533042:*********", "paymentID": *********, "amt": 2.4, "approveCode": 0}]}, {"chargeID": 1330736010, "codeType": "PMNT", "amt": 1.86, "curr": "AED", "originalAmt": 1.86, "originalCurr": "AED", "status": 1, "billDate": "2025-05-03T17:58:53", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330736010:*********", "paymentID": *********, "amt": 1.86, "approveCode": 0}]}, {"chargeID": 1330717387, "codeType": "PMNT", "amt": 1.56, "curr": "AED", "originalAmt": 1.56, "originalCurr": "AED", "status": 1, "billDate": "2025-05-03T17:38:57", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330717387:*********", "paymentID": *********, "amt": 1.56, "approveCode": 0}]}, {"chargeID": 1327372794, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1327372791, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372794:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1330735490, "codeType": "NSST", "amt": 32, "curr": "AED", "originalAmt": 32, "originalCurr": "AED", "status": 1, "billDate": "2025-05-03T17:58:25", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330735490:*********", "paymentID": *********, "amt": 32, "approveCode": 0}], "PFID": "181207", "ssrCommentId": "*********"}, {"chargeID": 1330716583, "codeType": "BAGB", "amt": 26, "curr": "AED", "originalAmt": 26, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-03T17:38:49", "desc": "BAGB", "comment": "FLXID:GCC-AE DXB-TBS/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330716583:*********", "paymentID": *********, "amt": 26, "approveCode": 0}]}]}, {"recNum": 3, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-04T19:56:30", "billDate": "2025-05-04T19:57:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-04T19:57:06"}, {"chargeID": **********, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": **********, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "Passenger Fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "Advanced passenger information fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372801, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1339996126, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": **********, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "Airport Passenger Security Fee (International)", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372803, "paymentMap": [{"key": "1339996126:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1339996127, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "YQ - DUMMY", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372799, "paymentMap": [{"key": "1339996127:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1327372801, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1327372798, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372801:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327372803, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1327372798, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372803:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1327372799, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1327372798, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372799:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1327372798, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -290, "curr": "AED", "originalAmt": -290, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T11:53:43", "desc": "WEB:AIR", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372798, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -290, "approveCode": 0}]}, {"chargeID": 1327372798, "codeType": "AIR", "amt": 290, "curr": "AED", "originalAmt": 290, "originalCurr": "AED", "status": 0, "billDate": "2025-04-30T21:29:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372798:*********", "paymentID": *********, "amt": 290, "approveCode": 0}]}, {"chargeID": 1327372800, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1327372798, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372800:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1339996128, "codeType": "PNLT", "amt": 480, "curr": "AED", "originalAmt": 480, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "CancelNoRefund FZ 712 TBS - DXB 14.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996128:*********", "paymentID": *********, "amt": 480, "approveCode": 0}]}, {"chargeID": 1339996124, "codeType": "BAGB", "taxChargeID": **********, "amt": -26, "curr": "AED", "originalAmt": -26, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T11:53:44", "desc": "BAGB", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330716584, "paymentMap": [{"key": "1339996124:*********", "paymentID": *********, "amt": -26, "approveCode": 0}]}, {"chargeID": 1330716584, "codeType": "BAGB", "amt": 26, "curr": "AED", "originalAmt": 26, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-03T17:38:49", "desc": "BAGB", "comment": "FLXID:GCC-AE DXB-TBS/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330716584:*********", "paymentID": *********, "amt": 26, "approveCode": 0}]}]}, {"recNum": 5, "charges": [{"chargeID": 1339996175, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:49:23", "billDate": "2025-05-10T11:53:44", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996175:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-10T11:53:44"}, {"chargeID": 1339996170, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1339996169, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996170:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1339996171, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1339996169, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996171:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1339996172, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1339996169, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996172:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339996173, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1339996169, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996173:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1339996169, "codeType": "AIR", "amt": 380, "curr": "AED", "originalAmt": 380, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "FZ 714 TBS-DXB 18May2025 Sun 06:05 09:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996169:*********", "paymentID": *********, "amt": 135, "approveCode": 0}, {"key": "1339996169:*********", "paymentID": *********, "amt": 245, "approveCode": 0}]}, {"chargeID": 1339999380, "codeType": "PMNT", "amt": 39.94, "curr": "AED", "originalAmt": 39.94, "originalCurr": "AED", "status": 1, "billDate": "2025-05-10T11:56:45", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339999380:*********", "paymentID": *********, "amt": 39.94, "approveCode": 0}]}, {"chargeID": 1350438071, "codeType": "PMNT", "amt": 5.82, "curr": "AED", "originalAmt": 5.82, "originalCurr": "AED", "status": 1, "billDate": "2025-05-17T15:51:04", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350438071:209371986", "paymentID": 209371986, "amt": 5.82, "approveCode": 0}]}, {"chargeID": 1350561991, "codeType": "PMNT", "amt": 1.86, "curr": "AED", "originalAmt": 1.86, "originalCurr": "AED", "status": 1, "billDate": "2025-05-17T18:12:56", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350561991:209381469", "paymentID": 209381469, "amt": 1.86, "approveCode": 0}]}, {"chargeID": 1339996174, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996174:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1350561576, "codeType": "NSST", "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "billDate": "2025-05-17T18:12:27", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350561576:209381469", "paymentID": 209381469, "amt": 30, "approveCode": 0}], "PFID": "181235", "ssrCommentId": "136093388"}, {"chargeID": 1350436855, "codeType": "BAGL", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-17T15:50:50", "desc": "BAGL", "comment": "FLXID:BAGL_GLOBAL_Hub_Z2:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350436855:*********", "paymentID": *********, "amt": 80, "approveCode": 0}, {"key": "1350436855:*********", "paymentID": *********, "amt": 26, "approveCode": 0}, {"key": "1350436855:209371986", "paymentID": 209371986, "amt": 44, "approveCode": 0}]}, {"chargeID": 1339996176, "codeType": "BAGB", "amt": 26, "curr": "AED", "originalAmt": 26, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:49:23", "billDate": "2025-05-10T11:53:44", "desc": "BAGB", "comment": "FLXID:GCC-AE DXB-TBS/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996176:*********", "paymentID": *********, "amt": 26, "approveCode": 0}]}, {"chargeID": 1350436351, "codeType": "BAGB", "amt": -26, "curr": "AED", "originalAmt": -26, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:49:23", "billDate": "2025-05-17T15:50:50", "desc": "BAGB", "comment": "Cancel SSR Refund As Paid", "reasonID": 4, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339996176, "paymentMap": [{"key": "1350436351:*********", "paymentID": *********, "amt": -26, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1331892060, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-04T19:56:30", "billDate": "2025-05-04T19:57:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1331892060:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-04T19:57:06"}, {"chargeID": 1327372850, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1327372804, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372850:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1327372806, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1327372804, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372806:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1327372805, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1327372804, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372805:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1327372808, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1327372804, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372808:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327372849, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1327372804, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372849:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327372804, "codeType": "AIR", "amt": 380, "curr": "AED", "originalAmt": 380, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T21:29:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372804:*********", "paymentID": *********, "amt": 380, "approveCode": 0}]}, {"chargeID": 1327372807, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1327372804, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372807:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1330735500, "codeType": "NSST", "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "billDate": "2025-05-03T17:58:25", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330735500:*********", "paymentID": *********, "amt": 30, "approveCode": 0}], "PFID": "181207", "ssrCommentId": "*********"}]}, {"recNum": 4, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-04T19:56:30", "billDate": "2025-05-04T19:57:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-04T19:57:06"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "YQ - DUMMY", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "Advanced passenger information fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372854, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1339996196, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": **********, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "Passenger Fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372855, "paymentMap": [{"key": "1339996196:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1339996197, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": **********, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T11:53:44", "desc": "Airport Passenger Security Fee (International)", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372856, "paymentMap": [{"key": "1339996197:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1327372851, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1327372854, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1327372851, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372854:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327372855, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1327372851, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372855:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1327372856, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1327372851, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372856:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -290, "curr": "AED", "originalAmt": -290, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T11:53:44", "desc": "WEB:AIR", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327372851, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -290, "approveCode": 0}]}, {"chargeID": 1327372851, "codeType": "AIR", "amt": 290, "curr": "AED", "originalAmt": 290, "originalCurr": "AED", "status": 0, "billDate": "2025-04-30T21:29:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372851:*********", "paymentID": *********, "amt": 290, "approveCode": 0}]}, {"chargeID": 1327372853, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1327372851, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T21:29:13", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327372853:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1339996198, "codeType": "PNLT", "amt": 480, "curr": "AED", "originalAmt": 480, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "CancelNoRefund FZ 712 TBS - DXB 14.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996198:*********", "paymentID": *********, "amt": 480, "approveCode": 0}]}, {"chargeID": 1331531626, "codeType": "BAGB", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-04T13:38:54", "desc": "BAGB", "comment": "FLXID:BAGB_GLOBAL_Hub_Z2:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1331531626:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1339996191, "codeType": "BAGB", "taxChargeID": **********, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T11:53:44", "desc": "BAGB", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1331531626, "paymentMap": [{"key": "1339996191:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}]}, {"recNum": 6, "charges": [{"chargeID": 1339996205, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:49:23", "billDate": "2025-05-10T11:53:45", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996205:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-10T11:53:45"}, {"chargeID": 1339996200, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1339996199, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996200:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1339996201, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1339996199, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996201:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1339996202, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1339996199, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:45", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996202:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339996203, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1339996199, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:45", "billDate": "2025-05-10T11:53:45", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996203:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1339996199, "codeType": "AIR", "amt": 380, "curr": "AED", "originalAmt": 380, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:44", "billDate": "2025-05-10T11:53:44", "desc": "FZ 714 TBS-DXB 18May2025 Sun 06:05 09:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996199:*********", "paymentID": *********, "amt": 135, "approveCode": 0}, {"key": "1339996199:*********", "paymentID": *********, "amt": 245, "approveCode": 0}]}, {"chargeID": 1339996204, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T11:53:45", "billDate": "2025-05-10T11:53:45", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996204:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1350561575, "codeType": "NSST", "amt": 32, "curr": "AED", "originalAmt": 32, "originalCurr": "AED", "status": 1, "billDate": "2025-05-17T18:12:27", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350561575:209381469", "paymentID": 209381469, "amt": 32, "approveCode": 0}], "PFID": "181235", "ssrCommentId": "136093387"}, {"chargeID": 1350436856, "codeType": "BAGL", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-17T15:50:50", "desc": "BAGL", "comment": "FLXID:BAGL_GLOBAL_Hub_Z2:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350436856:209371986", "paymentID": 209371986, "amt": 150, "approveCode": 0}]}, {"chargeID": 1339996206, "codeType": "BAGB", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:49:23", "billDate": "2025-05-10T11:53:45", "desc": "BAGB", "comment": "FLXID:BAGB_GLOBAL_Hub_Z2:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339996206:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1350436352, "codeType": "BAGB", "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:49:23", "billDate": "2025-05-17T15:50:50", "desc": "BAGB", "comment": "Cancel SSR Refund As Paid", "reasonID": 4, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339996206, "paymentMap": [{"key": "1350436352:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}]}], "parentPNRs": [], "childPNRs": []}