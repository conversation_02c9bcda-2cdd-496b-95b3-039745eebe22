{"seriesNum": "299", "PNR": "ZJ711Z", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82989091", "bookDate": "2025-05-20T14:53:32", "modifyDate": "2025-05-21T16:13:35", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "28fff59875d14ec5dcgdub4dg085teo9abu8f57b2255", "securityGUID": "28fff59875d14ec5dcgdub4dg085teo9abu8f57b2255", "lastLoadGUID": "35A805193E3C031BE0631F206F0A8890", "isAsyncPNR": false, "MasterPNR": "ZJ711Z", "segments": [{"segKey": "16087532:16087532:5/22/2025 9:15:00 PM", "LFID": 16087532, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16087532", "org": "DXB", "dest": "IST", "depTime": "2025-05-22T21:15:00", "depTimeGMT": "2025-05-22T17:15:00", "arrTime": "2025-05-23T01:10:00", "operCarrier": "FZ", "operFlightNum": "755", "mrktCarrier": "FZ ", "mrktFlightNum": "755", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181283, "depDate": "2025-05-22T21:15:00", "legKey": "16087532:181283:5/22/2025 9:15:00 PM", "customerKey": "F56BDCC40319D5DF64EDB3FAB7115C7C276DA812BB8990CB045A4F5C592B5CB4"}], "active": true, "changeType": "AC"}, {"segKey": "16087532:16087532:5/21/2025 9:15:00 PM", "LFID": 16087532, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087532", "org": "DXB", "dest": "IST", "depTime": "2025-05-21T21:15:00", "depTimeGMT": "2025-05-21T17:15:00", "arrTime": "2025-05-22T01:10:00", "operCarrier": "FZ", "operFlightNum": "755", "mrktCarrier": "FZ", "mrktFlightNum": "755", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181283, "depDate": "2025-05-21T21:15:00", "legKey": "16087532:181283:5/21/2025 9:15:00 PM", "customerKey": "B21C2C760ED27516EB0D0F5BF1A6D93AC6534DA8EE299FE7FE7C2E8AB304C8ED"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 269601794, "fName": "ERHAN", "lName": "SARISU", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1976-03-30T00:00:00", "nationality": "792", "FFNum": "119629263", "FFTier": "GOLD", "TierID": "5", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "H", "insuPurchasedate": "5/20/2025 2:53:32 PM", "provider": "<PERSON>", "status": 0, "fareClass": "H", "operFareClass": "H", "FBC": "HOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "discloseEmergencyContact": 0, "insuConfNum": "J72LN-PKUK4-INS", "insuTransID": "J72LN-PKUK4-INS/574f5638-ee9e-4306-bb65-3c848311e855", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682c96d200077800000029ba#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-20T14:53:32"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "aamir.miya", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/20/2025 2:53:32 PM", "provider": "<PERSON>", "status": 5, "fareClass": "H", "operFareClass": "H", "FBC": "HOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "J72LN-PKUK4-INS/574f5638-ee9e-4306-bb65-3c848311e855", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682dfa150007770000008b41#269601794#1#ENT#SFQE#CHANGE", "fareTypeID": 23, "channelID": 1, "bookDate": "2025-05-21T16:07:43"}]}], "payments": [{"paymentID": *********, "paxID": 269611287, "method": "IPAY", "status": "1", "paidDate": "2025-05-20T16:24:49", "cardNum": "************5699", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 350.2, "baseCurr": "AED", "baseAmt": 350.2, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "096555", "reference": "23149479", "externalReference": "23149479", "tranId": "21527530", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21527530}, {"paymentID": *********, "paxID": 269601794, "method": "APOS", "status": "1", "paidDate": "2025-05-21T16:10:39", "paidCurr": "AED", "paidAmt": 107, "baseCurr": "AED", "baseAmt": 107, "userID": "cashier.nib4", "channelID": 1, "paymentComment": "020860", "tierID": "5", "authCode": "077429", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}, {"paymentID": *********, "paxID": 242910450, "method": "VCHR", "status": "1", "paidDate": "2025-05-20T14:53:35", "voucherNum": 3152156, "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1052.7, "baseCurr": "AED", "baseAmt": 1052.7, "userID": "MOBILE_APP", "channelID": 12, "tierID": "5", "voucherNumFull": "TCQXD1", "authCode": "TCQXD1", "reference": "A5402676", "externalReference": "A5402676", "tranId": "21525807", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21525807}], "OAFlights": null, "physicalFlights": [{"key": "16087532:181283:2025-05-21T09:15:00 PM", "LFID": 16087532, "PFID": 181283, "org": "DXB", "dest": "IST", "depDate": "2025-05-21T21:15:00", "depTime": "2025-05-21T21:15:00", "arrTime": "2025-05-22T01:10:00", "carrier": "FZ", "flightNum": "755", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "755", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:56:29 AM"}, {"key": "16087532:181283:2025-05-22T09:15:00 PM", "LFID": 16087532, "PFID": 181283, "org": "DXB", "dest": "IST", "depDate": "2025-05-22T21:15:00", "depTime": "2025-05-22T21:15:00", "arrTime": "2025-05-23T01:10:00", "carrier": "FZ", "flightNum": "755", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "755", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:56:29 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1354479954, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479954:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-20T14:53:31"}, {"chargeID": 1356210891, "codeType": "INSU", "taxChargeID": 1356210800, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479954, "paymentMap": [{"key": "1356210891:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1354479888, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1354479884, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479888:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1354479950, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1354479884, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479950:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1354479887, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1354479884, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479887:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1354479949, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1354479884, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479949:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1354479885, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1354479884, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479885:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1356210802, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1356210800, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479888, "paymentMap": [{"key": "1356210802:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1356210805, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1356210800, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479950, "paymentMap": [{"key": "1356210805:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1356210806, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1356210800, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479887, "paymentMap": [{"key": "1356210806:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1356210890, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1356210800, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479949, "paymentMap": [{"key": "1356210890:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1356210892, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1356210800, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479885, "paymentMap": [{"key": "1356210892:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1354479884, "codeType": "AIR", "amt": 520, "curr": "AED", "originalAmt": 520, "originalCurr": "AED", "status": 0, "billDate": "2025-05-20T14:53:32", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354479884:*********", "paymentID": *********, "amt": 520, "approveCode": 0}]}, {"chargeID": 1356210800, "codeType": "AIR", "amt": -520, "curr": "AED", "originalAmt": -520, "originalCurr": "AED", "status": 0, "billDate": "2025-05-21T16:07:44", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479884, "paymentMap": [{"key": "1356210800:*********", "paymentID": *********, "amt": -520, "approveCode": 0}]}, {"chargeID": 1354610215, "codeType": "PMNT", "amt": 10.2, "curr": "AED", "originalAmt": 10.2, "originalCurr": "AED", "status": 0, "billDate": "2025-05-20T16:24:53", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354610215:*********", "paymentID": *********, "amt": 10.2, "approveCode": 0}]}, {"chargeID": 1354479955, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181283", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1354479955:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181283", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356210801, "codeType": "XLGR", "taxChargeID": 1356210800, "amt": -177, "curr": "AED", "originalAmt": -177, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "XLGR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479955, "paymentMap": [{"key": "1356210801:*********", "paymentID": *********, "amt": -177, "approveCode": 0}], "PFID": "181283"}, {"chargeID": 1354479951, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1354479884, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356210808, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1356210800, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479951, "paymentMap": []}, {"chargeID": 1354479886, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1354479884, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356210803, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1356210800, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479886, "paymentMap": []}, {"chargeID": 1356210889, "codeType": "BUPZ", "taxChargeID": 1356210800, "amt": -340, "curr": "AED", "originalAmt": -340, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-21T16:07:44", "desc": "BUPZ", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354609425, "paymentMap": [{"key": "1356210889:*********", "paymentID": *********, "amt": -340, "approveCode": 0}]}, {"chargeID": 1354609425, "codeType": "BUPZ", "amt": 340, "curr": "AED", "originalAmt": 340, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-20T16:24:45", "desc": "BUPZ", "comment": "FLXID:BUPZ_GLOBAL_Hub_Z3:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1354609425:*********", "paymentID": *********, "amt": 340, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354479956, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:53:32", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181283", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356210893, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1356210800, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:07:44", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354479956, "paymentMap": [], "PFID": "181283"}]}, {"recNum": 2, "charges": [{"chargeID": 1356210910, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:08", "billDate": "2025-05-21T16:07:45", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210910:*********", "paymentID": *********, "amt": 8.7, "approveCode": 0}, {"key": "1356210910:*********", "paymentID": *********, "amt": 27, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-21T16:07:45"}, {"chargeID": 1356210901, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1356210900, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210901:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1356210902, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1356210900, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210902:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1356210903, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1356210900, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210903:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1356210904, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1356210900, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210904:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1356210905, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1356210900, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210905:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1356210912, "codeType": "AFEE", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:34", "billDate": "2025-05-21T16:07:45", "desc": "Special Service Request", "comment": "AIRPORT ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210912:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1356210900, "codeType": "AIR", "amt": 547, "curr": "AED", "originalAmt": 547, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:44", "billDate": "2025-05-21T16:07:45", "desc": "FZ 755 DXB-IST 21May2025 Wed 21:15 01:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 600, "tierPoints": 600, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 450, "PromoTier": 0, "paymentMap": [{"key": "1356210900:*********", "paymentID": *********, "amt": 547, "approveCode": 0}], "bonusMiles": 450, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356210909, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:02", "billDate": "2025-05-21T16:07:45", "desc": "Special Service Request:XLGR-15A", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210909:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181283"}, {"chargeID": 1356210908, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1356210900, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1356210906, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1356210900, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1356210911, "codeType": "BUPZ", "amt": 340, "curr": "AED", "originalAmt": 340, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:08", "billDate": "2025-05-21T16:07:45", "desc": "BUPZ", "comment": "FLXID:BUPZ_GLOBAL_Hub_Z3:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356210911:*********", "paymentID": *********, "amt": 340, "approveCode": 0}]}, {"chargeID": 1356210907, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1356210900, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:07:45", "billDate": "2025-05-21T16:07:45", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181283"}]}], "parentPNRs": [], "childPNRs": []}