{"seriesNum": "299", "PNR": "AFOZFV", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82571611", "bookDate": "2025-05-05T19:40:18", "modifyDate": "2025-05-21T13:31:17", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "95043d73970a452e702abaudr7j86741h34bd3e38c93", "securityGUID": "95043d73970a452e702abaudr7j86741h34bd3e38c93", "lastLoadGUID": "621f529f-e61c-4ea5-900c-17beb1f288ad", "isAsyncPNR": false, "MasterPNR": "AFOZFV", "segments": [{"segKey": "16659886:16659886:5/27/2025 3:50:00 PM", "LFID": 16659886, "depDate": "2025-05-27T00:00:00", "flightGroupId": "16659886", "org": "TLV", "dest": "DXB", "depTime": "2025-05-27T15:50:00", "depTimeGMT": "2025-05-27T12:50:00", "arrTime": "2025-05-27T20:10:00", "operCarrier": "FZ", "operFlightNum": "1082", "mrktCarrier": "FZ", "mrktFlightNum": "1082", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 185105, "depDate": "2025-05-27T15:50:00", "legKey": "16659886:185105:5/27/2025 3:50:00 PM", "customerKey": "D4A511C012A545626E5533487F5463C0EDA44EE723530E7972A4A1CD2AAE8983"}], "active": true}, {"segKey": "16659887:16659887:5/13/2025 3:45:00 PM", "LFID": 16659887, "depDate": "2025-05-13T00:00:00", "flightGroupId": "16659887", "org": "DXB", "dest": "TLV", "depTime": "2025-05-13T15:45:00", "depTimeGMT": "2025-05-13T11:45:00", "arrTime": "2025-05-13T18:15:00", "operCarrier": "FZ", "operFlightNum": "1125", "mrktCarrier": "FZ ", "mrktFlightNum": "1125", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 185106, "depDate": "2025-05-13T15:45:00", "legKey": "16659887:185106:5/13/2025 3:45:00 PM", "customerKey": "FC3C1E9CD31A81D24A999CD6FE3388A4AFC5BB84D6780D106A15C0B028A4B790"}], "active": true}, {"segKey": "16087734:16087734:5/23/2025 1:40:00 PM", "LFID": 16087734, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087734", "org": "TLV", "dest": "DXB", "depTime": "2025-05-23T13:40:00", "depTimeGMT": "2025-05-23T10:40:00", "arrTime": "2025-05-23T18:00:00", "operCarrier": "FZ", "operFlightNum": "1212", "mrktCarrier": "FZ ", "mrktFlightNum": "1212", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181473, "depDate": "2025-05-23T13:40:00", "legKey": "16087734:181473:5/23/2025 1:40:00 PM", "customerKey": "1DCA72E1A02BCB11E67572624A6A8C949E0092969C98D9A51D6495908B50CF11"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 267996279, "fName": "TAMARA", "lName": "KUDRIASHOVA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/5/2025 8:02:39 PM", "provider": "AIG", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 268681842, "discloseEmergencyContact": 1, "insuConfNum": "987221950", "insuTransID": "3c8ea554-9402-4ee6-a8fe-3350c1f6b7ac", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681910cf0007770000003b7d#1#1#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-05T19:40:19"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/5/2025 8:02:39 PM", "provider": "AIG", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987221950", "insuTransID": "3c8ea554-9402-4ee6-a8fe-3350c1f6b7ac", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681910cf0007770000003b7d#1#2#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-05T19:40:19"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "as<PERSON>.kam<PERSON>", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/21/2025 1:09:01 PM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 270283354, "discloseEmergencyContact": 1, "insuTransID": "RY7UT-DUVP7-INS/4f0d2c0f-017a-4120-8165-f67f2939a745", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682dcfd5000778000000720f#267996279#2#ENT#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-21T13:08:55"}]}], "payments": [{"paymentID": 208010161, "paxID": 267998158, "method": "VISA", "status": "2", "paidDate": "2025-05-05T20:06:37", "cardNum": "************1499", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 243.39, "baseCurr": "AED", "baseAmt": 243.39, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "reference": "22859214", "externalReference": "22859214", "tranId": "21230689", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21230689}, {"paymentID": *********, "paxID": 28404146, "method": "INVC", "status": "1", "paidDate": "2025-05-21T13:31:11", "IATANum": "CALL", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 40.7, "baseCurr": "AED", "baseAmt": 40.7, "userID": "callaltynai1", "channelID": 16, "authCode": "110768040", "reference": "A5408826", "externalReference": "A5408826", "tranId": "21545676", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21545676}, {"paymentID": *********, "paxID": 267997093, "method": "VISA", "status": "1", "paidDate": "2025-05-05T19:52:57", "cardNum": "************1499", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2480.24, "baseCurr": "AED", "baseAmt": 2480.24, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "227247", "reference": "22858808", "externalReference": "22858808", "tranId": "21230298", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallmotoaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21230298}, {"paymentID": *********, "paxID": 267998160, "method": "VISA", "status": "1", "paidDate": "2025-05-05T20:08:58", "cardNum": "************1499", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 243.39, "baseCurr": "AED", "baseAmt": 243.39, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "291564", "reference": "22859374", "externalReference": "22859374", "tranId": "21230689", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21230689}], "OAFlights": null, "physicalFlights": [{"key": "16659887:185106:2025-05-13T03:45:00 PM", "LFID": 16659887, "PFID": 185106, "org": "DXB", "dest": "TLV", "depDate": "2025-05-13T15:45:00", "depTime": "2025-05-13T15:45:00", "arrTime": "2025-05-13T18:15:00", "carrier": "FZ", "flightNum": "1125", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1125", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false}, {"key": "16087734:181473:2025-05-23T01:40:00 PM", "LFID": 16087734, "PFID": 181473, "org": "TLV", "dest": "DXB", "depDate": "2025-05-23T13:40:00", "depTime": "2025-05-23T13:40:00", "arrTime": "2025-05-23T18:00:00", "carrier": "FZ", "flightNum": "1212", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "1212", "flightStatus": "CLOSED", "originMetroGroup": "TLV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Tel Aviv Ben Gurion", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 2:18:43 PM"}, {"key": "16659886:185105:2025-05-27T03:50:00 PM", "LFID": 16659886, "PFID": 185105, "org": "TLV", "dest": "DXB", "depDate": "2025-05-27T15:50:00", "depTime": "2025-05-27T15:50:00", "arrTime": "2025-05-27T20:10:00", "carrier": "FZ", "flightNum": "1082", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1082", "flightStatus": "CLOSED", "originMetroGroup": "TLV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Tel Aviv Ben Gurion", "destinationName": "Dubai International Airport", "isActive": false}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T20:06:01", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T20:06:01"}, {"chargeID": **********, "codeType": "WCHS", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-06T07:25:42", "billDate": "2025-05-06T07:25:50", "desc": "Special Service Request", "comment": "Wheelchair (cannot climb stairs)", "reasonID": 12, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1333360017, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360017:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333360015, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360015:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1333360018, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360018:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333360019, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360019:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 840, "curr": "AED", "originalAmt": 840, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T19:40:19", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 840, "approveCode": 0}]}, {"chargeID": 1333377972, "codeType": "PMNT", "amt": 72.24, "curr": "AED", "originalAmt": 72.24, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T19:53:02", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333377972:*********", "paymentID": *********, "amt": 72.24, "approveCode": 0}]}, {"chargeID": 1333391145, "codeType": "PMNT", "amt": 7.09, "curr": "AED", "originalAmt": 7.09, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T20:09:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333391145:*********", "paymentID": *********, "amt": 7.09, "approveCode": 0}]}, {"chargeID": 1333360031, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::185106", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360031:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "185106"}, {"chargeID": 1333360021, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1333360020, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1333360033, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185106"}, {"chargeID": 1344008367, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-13T09:46:23", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185106", "ssrCommentId": "*********"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T20:06:01", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T20:06:01"}, {"chargeID": **********, "codeType": "WCHS", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-06T07:25:42", "billDate": "2025-05-21T13:08:56", "desc": "Special Service Request", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": []}, {"chargeID": **********, "codeType": "WCHS", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-06T07:25:42", "billDate": "2025-05-06T07:25:50", "desc": "Special Service Request", "comment": "Wheelchair (cannot climb stairs)", "reasonID": 12, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355909597, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T13:08:56", "desc": "Advanced passenger information fee", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360024, "paymentMap": [{"key": "1355909597:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1355909600, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T13:08:56", "desc": "YQ - DUMMY", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360025, "paymentMap": [{"key": "1355909600:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1355909602, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "taxChargeID": **********, "amt": -110, "curr": "AED", "originalAmt": -110, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T13:08:56", "desc": "Departure Passenger Airport Tax - (Intl.)", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360026, "paymentMap": [{"key": "1355909602:*********", "paymentID": *********, "amt": -110, "approveCode": 0}]}, {"chargeID": 1333360026, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "taxChargeID": 1333360023, "amt": 110, "curr": "AED", "originalAmt": 110, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Departure Passenger Airport Tax - (Intl.)", "comment": "Departure Passenger Airport Tax - (Intl.)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360026:*********", "paymentID": *********, "amt": 110, "approveCode": 0}]}, {"chargeID": 1333360024, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333360023, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360024:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333360025, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333360023, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360025:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -775, "curr": "AED", "originalAmt": -775, "originalCurr": "AED", "status": 0, "billDate": "2025-05-21T13:08:56", "desc": "WEB:AIR", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360023, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -775, "approveCode": 0}]}, {"chargeID": 1333360023, "codeType": "AIR", "amt": 775, "curr": "AED", "originalAmt": 775, "originalCurr": "AED", "status": 0, "billDate": "2025-05-05T19:40:19", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333360023:*********", "paymentID": *********, "amt": 775, "approveCode": 0}]}, {"chargeID": 1355909598, "codeType": "XLGR", "taxChargeID": **********, "amt": -188, "curr": "AED", "originalAmt": -188, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-21T13:08:56", "desc": "XLGR", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333386929, "paymentMap": [{"key": "1355909598:*********", "paymentID": *********, "amt": -188, "approveCode": 0}], "PFID": "181473"}, {"chargeID": 1333386929, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T20:06:01", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181473", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333386929:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "181473"}, {"chargeID": 1355909595, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T13:08:56", "desc": "Included seat", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360028, "paymentMap": []}, {"chargeID": 1333360028, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1333360023, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355909596, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T13:08:56", "desc": "30kg BAG INCLUDED IN FARE", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360027, "paymentMap": []}, {"chargeID": 1333360027, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1333360023, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355909601, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-21T13:08:56", "desc": "Standard meal", "comment": "CHANGE DATE DUE AS PER PAX RQST 74957370101 No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333360034, "paymentMap": [], "PFID": "181473"}, {"chargeID": 1333360034, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T19:40:19", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181473"}]}, {"recNum": 3, "charges": [{"chargeID": 1355909631, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:06:43", "billDate": "2025-05-21T13:08:56", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355909631:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-21T13:08:56"}, {"chargeID": 1355909632, "codeType": "WCHS", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:06:43", "billDate": "2025-05-21T13:08:56", "desc": "Special Service Request", "comment": "Wheelchair (cannot climb stairs)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355909604, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "taxChargeID": 1355909603, "amt": 110, "curr": "AED", "originalAmt": 110, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "IL: Departure Passenger Airport Tax - (Intl.)", "comment": "IL: Departure Passenger Airport Tax - (Intl.)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355909604:*********", "paymentID": *********, "amt": 110, "approveCode": 0}]}, {"chargeID": 1355909605, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1355909603, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355909605:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1355909606, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1355909603, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355909606:*********", "paymentID": *********, "amt": 175, "approveCode": 0}, {"key": "1355909606:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1355909603, "codeType": "AIR", "amt": 780, "curr": "AED", "originalAmt": 780, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "FZ 1082 TLV-DXB 27May2025 Tue 15:50 20:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355909603:*********", "paymentID": *********, "amt": 780, "approveCode": 0}]}, {"chargeID": 1355909630, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:06:38", "billDate": "2025-05-21T13:08:56", "desc": "Special Service Request:XLGR-6D", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355909630:*********", "paymentID": *********, "amt": 5, "approveCode": 0}, {"key": "1355909630:*********", "paymentID": *********, "amt": 183, "approveCode": 0}], "PFID": "185105"}, {"chargeID": 1355909629, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1355909603, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355909607, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1355909603, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355909608, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1355909603, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T13:08:56", "billDate": "2025-05-21T13:08:56", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185105"}]}], "parentPNRs": [], "childPNRs": []}