{"seriesNum": "299", "PNR": "XFEAHB", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "83222487", "bookDate": "2025-05-29T01:16:03", "modifyDate": "2025-06-01T04:08:30", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "ah9d35974b2anb04mebc4edbe1t2gedau47e35f9880a", "securityGUID": "ah9d35974b2anb04mebc4edbe1t2gedau47e35f9880a", "lastLoadGUID": "4f34f12b-7ab0-4c2f-81f3-5c6ecc8ad233", "isAsyncPNR": false, "MasterPNR": "XFEAHB", "segments": [{"segKey": "16827898:16827898:6/1/2025 8:55:00 AM", "LFID": 16827898, "depDate": "2025-06-01T00:00:00", "flightGroupId": "16827898", "org": "DXB", "dest": "TLV", "depTime": "2025-06-01T08:55:00", "depTimeGMT": "2025-06-01T04:55:00", "arrTime": "2025-06-01T11:30:00", "operCarrier": "FZ", "operFlightNum": "1635", "mrktCarrier": "FZ", "mrktFlightNum": "1635", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 186157, "depDate": "2025-06-01T08:55:00", "legKey": "16827898:186157:6/1/2025 8:55:00 AM", "customerKey": "17CD1B8E62D2613365986619CFBB61FD3FA7A3375A2EF98D77C89426A602CB27"}], "active": true, "changeType": "AC"}, {"segKey": "16087850:16087850:5/31/2025 8:55:00 PM", "LFID": 16087850, "depDate": "2025-05-31T00:00:00", "flightGroupId": "16087850", "org": "DXB", "dest": "TLV", "depTime": "2025-05-31T20:55:00", "depTimeGMT": "2025-05-31T16:55:00", "arrTime": "2025-05-31T23:35:00", "operCarrier": "FZ", "operFlightNum": "1807", "mrktCarrier": "FZ ", "mrktFlightNum": "1807", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181580, "depDate": "2025-05-31T20:55:00", "legKey": "16087850:181580:5/31/2025 8:55:00 PM", "customerKey": "233542BD92BB7843769083A324A79C03A99B69E18CC0F88F876558206911CB2B"}], "active": true, "changeType": "TK"}, {"segKey": "17063714:17063714:6/13/2025 7:45:00 PM", "LFID": 17063714, "depDate": "2025-06-13T00:00:00", "flightGroupId": "17063714", "org": "TLV", "dest": "DXB", "depTime": "2025-06-13T19:45:00", "depTimeGMT": "2025-06-13T16:45:00", "arrTime": "2025-06-14T00:05:00", "operCarrier": "FZ", "operFlightNum": "1126", "mrktCarrier": "FZ ", "mrktFlightNum": "1126", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 187677, "depDate": "2025-06-13T19:45:00", "legKey": "17063714:187677:6/13/2025 7:45:00 PM", "customerKey": "B07F0BB55EC3CC0F05F61EB9DE68B6564CE18DE24D5460B02EFD6ABB594DCA20"}], "active": true}], "persons": [{"paxID": *********, "fName": "MOHAMMAD", "lName": "SBIHAT", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1986-06-07T00:00:00", "FFNum": "00906246924", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/29/2025 1:16:03 AM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "WN8L9-PYSTW-INS", "insuTransID": "WN8L9-PYSTW-INS/7303e644-e780-4ba1-a415-7ea79018992a", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6837b3970007770000009e28#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-29T01:16:03"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/29/2025 1:16:03 AM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "WN8L9-PYSTW-INS", "insuTransID": "WN8L9-PYSTW-INS/7303e644-e780-4ba1-a415-7ea79018992a", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b2c8d0007780000014ee5#*********#2#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 2, "bookDate": "2025-05-29T01:16:03"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "summer.rod<PERSON>ue", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/29/2025 1:16:03 AM", "provider": "<PERSON>", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WN8L9-PYSTW-INS/7303e644-e780-4ba1-a415-7ea79018992a", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b2c8d0007780000014ee5#*********#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-31T16:25:23"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "iatci", "statusReasonID": 0, "status": 1, "cabin": "Y", "primaryPax": false, "travelsWithPaxID": "0", "channelID": 15, "bookDate": "2025-05-31T19:16:12"}]}], "payments": [{"paymentID": *********, "paxID": *********, "method": "VISA", "status": "1", "paidDate": "2025-05-29T01:16:08", "cardNum": "************6063", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2162.12, "baseCurr": "AED", "baseAmt": 2162.12, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "08238D", "reference": "23311706", "externalReference": "23311706", "tranId": "21694058", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21694058}, {"paymentID": *********, "paxID": *********, "method": "VISA", "status": "1", "paidDate": "2025-05-31T16:28:33", "cardNum": "************6063", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1334.88, "baseCurr": "AED", "baseAmt": 1334.88, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "05014D", "reference": "23366713", "externalReference": "23366713", "tranId": "21750725", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21750725}], "OAFlights": null, "physicalFlights": [{"key": "16087850:181580:2025-05-31T08:55:00 PM", "LFID": 16087850, "PFID": 181580, "org": "DXB", "dest": "TLV", "depDate": "2025-05-31T20:55:00", "depTime": "2025-05-31T20:55:00", "arrTime": "2025-05-31T23:35:00", "carrier": "FZ", "flightNum": "1807", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1807", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 13200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 2:47:18 PM"}, {"key": "16827898:186157:2025-06-01T08:55:00 AM", "LFID": 16827898, "PFID": 186157, "org": "DXB", "dest": "TLV", "depDate": "2025-06-01T08:55:00", "depTime": "2025-06-01T08:55:00", "arrTime": "2025-06-01T11:30:00", "carrier": "FZ", "flightNum": "1635", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1635", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12900, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false, "changeType": "AC", "flightChangeTime": "4/14/2025 12:39:07 PM"}, {"key": "17063714:187677:2025-06-13T07:45:00 PM", "LFID": 17063714, "PFID": 187677, "org": "TLV", "dest": "DXB", "depDate": "2025-06-13T19:45:00", "depTime": "2025-06-13T19:45:00", "arrTime": "2025-06-14T00:05:00", "carrier": "FZ", "flightNum": "1126", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1126", "flightStatus": "OPEN", "originMetroGroup": "TLV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Tel Aviv Ben Gurion", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": 1370992416, "codeType": "XBAG", "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T19:21:20", "comment": "60.00", "reasonID": 25, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370790761, "paymentMap": [], "PFID": "186157", "POSAirport": "DXB", "isSSR": true, "ChargeBookDate": "2025-06-01T04:08:30"}, {"chargeID": 1370790761, "codeType": "XBAG", "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T19:21:20", "comment": "60.00", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186157", "POSAirport": "DXB", "workStationID": "INBOUND.IATCI", "parameter1Name": "BATCH_ID", "parameter1Value": "e8f871hbd6tchcu4t6je64r4tbg9a61137bf4ed9439a", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-06-01T08:55:00\",\"fltNum\":\"1635\",\"depDate\":\"2025-06-01T00:00:00\",\"board\":\"DXB\",\"off\":\"TLV\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1154015438_DXBTLV_XBAG"}, {"chargeID": 1370610835, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:21:35", "billDate": "2025-05-31T16:25:24", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610835:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1370610827, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1370610826, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610827:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1370610828, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370610826, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610828:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1370610829, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1370610826, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610829:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370610830, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370610826, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610830:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370610831, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370610826, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610831:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1370610826, "codeType": "AIR", "amt": 1056, "curr": "AED", "originalAmt": 1056, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "FZ 1635 DXB-TLV 01Jun2025 Sun 08:55 11:30\r\n", "reasonID": 2, "channelID": 1, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1370610826:*********", "paymentID": *********, "amt": 175.15, "approveCode": 0}, {"key": "1370610826:*********", "paymentID": *********, "amt": 880.85, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370613906, "codeType": "PMNT", "amt": 38.88, "curr": "AED", "originalAmt": 38.88, "originalCurr": "AED", "status": 1, "billDate": "2025-05-31T16:28:36", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370613906:*********", "paymentID": *********, "amt": 38.88, "approveCode": 0}]}, {"chargeID": 1370610834, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610834:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1370992417, "codeType": "GHA", "taxChargeID": 1370790761, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T19:21:20", "reasonID": 25, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370790762, "paymentMap": [], "PFID": "186157", "POSAirport": "DXB"}, {"chargeID": 1370790762, "codeType": "GHA", "taxChargeID": 1370790761, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T19:21:20", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186157", "POSAirport": "DXB", "workStationID": "INBOUND.IATCI", "parameter1Name": "BATCH_ID", "parameter1Value": "e8f871hbd6tchcu4t6je64r4tbg9a61137bf4ed9439a", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-06-01T08:55:00\",\"fltNum\":\"1635\",\"depDate\":\"2025-06-01T00:00:00\",\"board\":\"DXB\",\"off\":\"TLV\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1154015438_DXBTLV_XBAG"}, {"chargeID": 1370610832, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370610826, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370610833, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370610826, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "186157", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": 1370610820, "codeType": "INSU", "taxChargeID": 1370610814, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "INSU", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621100, "paymentMap": [{"key": "1370610820:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-31T16:25:24"}, {"chargeID": 1366621100, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621100:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1370610815, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1370610814, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "Passenger Service Charge (Intl)", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621085, "paymentMap": [{"key": "1370610815:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1370610816, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1370610814, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "Passengers Security & Safety Service Fees", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621087, "paymentMap": [{"key": "1370610816:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370610819, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370610814, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "Advanced passenger information fee", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621088, "paymentMap": [{"key": "1370610819:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370610821, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370610814, "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "YQ - DUMMY", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621089, "paymentMap": [{"key": "1370610821:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1370610822, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370610814, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "Passenger Facilities Charge.", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621086, "paymentMap": [{"key": "1370610822:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1366621085, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1366621084, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621085:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1366621087, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1366621084, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621087:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1366621086, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366621084, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621086:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1366621089, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366621084, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621089:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1366621088, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366621084, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621088:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370610814, "codeType": "AIR", "amt": -669, "curr": "AED", "originalAmt": -669, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T16:25:24", "desc": "WEB:AIR", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621084, "paymentMap": [{"key": "1370610814:*********", "paymentID": *********, "amt": -669, "approveCode": 0}]}, {"chargeID": 1366621084, "codeType": "AIR", "amt": 669, "curr": "AED", "originalAmt": 669, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T01:16:03", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621084:*********", "paymentID": *********, "amt": 669, "approveCode": 0}]}, {"chargeID": 1366621880, "codeType": "PMNT", "amt": 62.97, "curr": "AED", "originalAmt": 62.97, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T01:16:10", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621880:*********", "paymentID": *********, "amt": 62.97, "approveCode": 0}]}, {"chargeID": 1370610823, "codeType": "PNLT", "amt": 849, "curr": "AED", "originalAmt": 849, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:25:24", "billDate": "2025-05-31T16:25:24", "desc": "CancelNoRefund FZ 1807 DXB - TLV 31.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610823:*********", "paymentID": *********, "amt": 849, "approveCode": 0}]}, {"chargeID": 1370610817, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370610814, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "20kg BAG INCLUDED IN FARE", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621090, "paymentMap": []}, {"chargeID": 1366621090, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366621084, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370610818, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370610814, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "Standard meal", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621103, "paymentMap": [], "PFID": "181580"}, {"chargeID": 1366621103, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580"}]}, {"recNum": 2, "charges": [{"chargeID": 1370610824, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T16:21:35", "billDate": "2025-05-31T16:25:24", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370610824:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-31T16:25:24"}, {"chargeID": 1370610825, "codeType": "INSU", "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T16:25:24", "desc": "INSU", "comment": ". No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366621102, "paymentMap": [{"key": "1370610825:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1366621102, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621102:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1366621095, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "taxChargeID": 1366621092, "amt": 110, "curr": "AED", "originalAmt": 110, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Departure Passenger Airport Tax - (Intl.)", "comment": "Departure Passenger Airport Tax - (Intl.)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621095:*********", "paymentID": *********, "amt": 110, "approveCode": 0}]}, {"chargeID": 1366621093, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366621092, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621093:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1366621094, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366621092, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366621094:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1366621092, "codeType": "AIR", "amt": 780, "curr": "AED", "originalAmt": 780, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T01:16:03", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 500, "tierPoints": 500, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1366621092:*********", "paymentID": *********, "amt": 780, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1366621097, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1366621092, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1366621096, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1366621092, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1366621104, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T01:16:03", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "187677", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}