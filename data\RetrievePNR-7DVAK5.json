{"seriesNum": "299", "PNR": "7DVAK5", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82440957", "bookDate": "2025-04-30T14:37:10", "modifyDate": "2025-05-14T13:24:50", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "af788ec701014a584b89yal7y3p5ubhay8630192fcb8", "securityGUID": "af788ec701014a584b89yal7y3p5ubhay8630192fcb8", "lastLoadGUID": "603d3640-b2a1-46c6-bd6f-25992c6391fd", "isAsyncPNR": false, "MasterPNR": "7DVAK5", "segments": [{"segKey": "16087482:16087482:5/14/2025 6:35:00 PM", "LFID": 16087482, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087482", "org": "IST", "dest": "DXB", "depTime": "2025-05-14T18:35:00", "depTimeGMT": "2025-05-14T15:35:00", "arrTime": "2025-05-15T00:05:00", "operCarrier": "FZ", "operFlightNum": "728", "mrktCarrier": "FZ", "mrktFlightNum": "728", "persons": [{"recNum": 12, "status": 5}, {"recNum": 11, "status": 5}, {"recNum": 9, "status": 5}, {"recNum": 10, "status": 5}], "legDetails": [{"PFID": 181263, "depDate": "2025-05-14T18:35:00", "legKey": "16087482:181263:5/14/2025 6:35:00 PM", "customerKey": "AB6D035DC90594F6CB18A94A32C884EC3845F7ED85FFC03EB4299391863CCDA5"}], "active": true, "changeType": "AC"}, {"segKey": "16087490:16087490:5/8/2025 9:15:00 AM", "LFID": 16087490, "depDate": "2025-05-08T00:00:00", "flightGroupId": "16087490", "org": "DXB", "dest": "SAW", "depTime": "2025-05-08T09:15:00", "depTimeGMT": "2025-05-08T05:15:00", "arrTime": "2025-05-08T13:05:00", "operCarrier": "FZ", "operFlightNum": "751", "mrktCarrier": "FZ ", "mrktFlightNum": "751", "persons": [{"recNum": 4, "status": 5}, {"recNum": 3, "status": 5}, {"recNum": 2, "status": 5}, {"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181221, "depDate": "2025-05-08T09:15:00", "legKey": "16087490:181221:5/8/2025 9:15:00 AM", "customerKey": "823A343A6E903AF62362AB0F3E6840FE8F2B1C800B835EF5FC5A59C044ED3299"}], "active": true, "changeType": "AC"}, {"segKey": "16087482:16087482:5/12/2025 6:35:00 PM", "LFID": 16087482, "depDate": "2025-05-12T00:00:00", "flightGroupId": "16087482", "org": "IST", "dest": "DXB", "depTime": "2025-05-12T18:35:00", "depTimeGMT": "2025-05-12T15:35:00", "arrTime": "2025-05-13T00:05:00", "operCarrier": "FZ", "operFlightNum": "728", "mrktCarrier": "FZ ", "mrktFlightNum": "728", "persons": [{"recNum": 6, "status": 0}, {"recNum": 7, "status": 0}, {"recNum": 5, "status": 0}, {"recNum": 8, "status": 0}], "legDetails": [{"PFID": 181263, "depDate": "2025-05-12T18:35:00", "legKey": "16087482:181263:5/12/2025 6:35:00 PM", "customerKey": "6656B22ECAC835145C355B8A3D84A0C9013EF9AC2CC53BF6E6FE59C7014ADBBD"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 267488469, "fName": "FATEMEH", "lName": "SEHAT KASHANI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [4, 6, 12]}, {"paxID": 267488468, "fName": "MAHMOUD", "lName": "NAEIMI AKBAR", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [3, 7, 11]}, {"paxID": 267488466, "fName": "HOMAN", "lName": "NAEIMI AKBAR", "title": "MR", "PTCID": 1, "gender": "M", "FFNum": "109830755", "FFTier": "BLUE", "TierID": "3", "recNum": [2, 5, 9]}, {"paxID": 267488467, "fName": "HILDA", "lName": "NAIEMIAKBAR", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 8, 10]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681233d3000778000000b935#2#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681233d3000778000000b935#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681233d3000778000000b935#3#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681233d3000778000000b935#4#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "toRecNum": 9, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681233d3000778000000b935#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "toRecNum": 12, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681233d3000778000000b935#4#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "toRecNum": 11, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681233d3000778000000b935#3#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/5/2025 6:15:45 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987198226", "insuTransID": "f91fc80d-3220-41fa-be88-16f0763ecb64", "toRecNum": 10, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681233d3000778000000b935#2#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-04-30T14:37:10"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "maryam.syed", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/11/2025 12:48:53 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "U8YSZ-HR7TK-INS/1fb2d115-1165-450c-b12c-e38d45ecbd8b", "fromRecNum": 5, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68209ba7000777000000ce36#267488466#2#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-11T12:48:40"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "maryam.syed", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/11/2025 12:48:53 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "U8YSZ-HR7TK-INS/1fb2d115-1165-450c-b12c-e38d45ecbd8b", "fromRecNum": 8, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68209ba7000777000000ce36#267488467#2#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-11T12:48:40"}]}, {"recNum": 11, "recordDetails": [{"bookAgent": "maryam.syed", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/11/2025 12:48:53 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "U8YSZ-HR7TK-INS/1fb2d115-1165-450c-b12c-e38d45ecbd8b", "fromRecNum": 7, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68209ba7000777000000ce36#267488468#2#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-11T12:48:40"}]}, {"recNum": 12, "recordDetails": [{"bookAgent": "maryam.syed", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/11/2025 12:48:53 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "U8YSZ-HR7TK-INS/1fb2d115-1165-450c-b12c-e38d45ecbd8b", "fromRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68209ba7000777000000ce36#267488469#2#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-11T12:48:41"}]}], "payments": [{"paymentID": *********, "paxID": 267900204, "method": "VISA", "status": "1", "paidDate": "2025-05-05T06:17:51", "cardNum": "************7805", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 199, "baseCurr": "AED", "baseAmt": 199, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "515412", "reference": "22844140", "externalReference": "22844140", "tranId": "21211518", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21211518}, {"paymentID": *********, "paxID": 267899976, "method": "VISA", "status": "1", "paidDate": "2025-05-05T06:15:20", "cardNum": "************7805", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 525.3, "baseCurr": "AED", "baseAmt": 525.3, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "515411", "reference": "22844112", "externalReference": "22844112", "tranId": "21211454", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21211454}, {"paymentID": *********, "paxID": 267488536, "method": "VISA", "status": "1", "paidDate": "2025-04-30T14:37:43", "cardNum": "************7805", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 5706.2, "baseCurr": "AED", "baseAmt": 5706.2, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "515402", "reference": "22750584", "externalReference": "22750584", "tranId": "21128552", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21128552}, {"paymentID": *********, "paxID": 268600537, "method": "VISA", "status": "1", "paidDate": "2025-05-11T12:53:19", "cardNum": "************7805", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1053.48, "baseCurr": "AED", "baseAmt": 1053.48, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "515428", "reference": "22970718", "externalReference": "22970718", "tranId": "21340739", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21340739}], "OAFlights": null, "physicalFlights": [{"key": "16087490:181221:2025-05-08T09:15:00 AM", "LFID": 16087490, "PFID": 181221, "org": "DXB", "dest": "SAW", "depDate": "2025-05-08T09:15:00", "depTime": "2025-05-08T09:15:00", "arrTime": "2025-05-08T13:05:00", "carrier": "FZ", "flightNum": "751", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "751", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Sabiha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:57:08 AM"}, {"key": "16087482:181263:2025-05-12T06:35:00 PM", "LFID": 16087482, "PFID": 181263, "org": "IST", "dest": "DXB", "depDate": "2025-05-12T18:35:00", "depTime": "2025-05-12T18:35:00", "arrTime": "2025-05-13T00:05:00", "carrier": "FZ", "flightNum": "728", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "728", "flightStatus": "CLOSED", "originMetroGroup": "IST", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16200, "reaccomChangeAlert": false, "originName": "Istanbul Airport", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:19 AM"}, {"key": "16087482:181263:2025-05-14T06:35:00 PM", "LFID": 16087482, "PFID": 181263, "org": "IST", "dest": "DXB", "depDate": "2025-05-14T18:35:00", "depTime": "2025-05-14T18:35:00", "arrTime": "2025-05-15T00:05:00", "carrier": "FZ", "flightNum": "728", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "728", "flightStatus": "CLOSED", "originMetroGroup": "IST", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16200, "reaccomChangeAlert": false, "originName": "Istanbul Airport", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:19 AM"}], "chargeInfos": [{"recNum": 12, "charges": [{"chargeID": 1346107947, "codeType": "NACT", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T13:24:35", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 25, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346107637, "paymentMap": [], "PFID": "181263", "saleCurrency": "EUR", "saleAmount": 0, "POSAirport": "IST", "isSSR": true, "ChargeBookDate": "2025-05-14T13:24:50"}, {"chargeID": 1346107637, "codeType": "NACT", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T13:24:35", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263", "saleCurrency": "EUR", "saleAmount": 0, "POSAirport": "IST", "workStationID": "IST2CKBN20", "parameter1Name": "BATCH_ID", "parameter1Value": "91b40007bcf9489de9ca4b14rdhcu7i98d62y3ca43bc", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-14T18:35:00\",\"fltNum\":\"728\",\"depDate\":\"2025-05-14T00:00:00\",\"board\":\"IST\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1144607548_ISTDXB"}, {"chargeID": 1341268823, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:18", "billDate": "2025-05-11T12:48:45", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268823:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1341269968, "codeType": "WCHR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T12:49:40", "billDate": "2025-05-11T12:49:46", "desc": "Special Service Request", "comment": "WHEELCHAIR", "reasonID": 12, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346107576, "codeType": "WCHR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T12:49:40", "billDate": "2025-05-11T12:49:46", "desc": "Special Service Request", "comment": "WHEELCHAIR", "reasonID": 25, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341269968, "paymentMap": [], "POSAirport": "IST"}, {"chargeID": 1346107948, "codeType": "WCHS", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T13:24:50", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263", "saleCurrency": "EUR", "saleAmount": 0, "POSAirport": "IST", "workStationID": "IST2CKBN20", "parameter1Name": "BATCH_ID", "parameter1Value": "ca3b86a28ec74oc3u7xbddu542v415t3r2f3u5fd539f", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-14T18:35:00\",\"fltNum\":\"728\",\"depDate\":\"2025-05-14T00:00:00\",\"board\":\"IST\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1144607548_ISTDXB"}, {"chargeID": 1341268807, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341268806, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268807:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341268808, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341268806, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268808:*********", "paymentID": *********, "amt": 140, "approveCode": 0}, {"key": "1341268808:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1341268810, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1341268806, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "M6: International Flights Security Charge", "comment": "M6: International Flights Security Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268810:*********", "paymentID": *********, "amt": 5, "approveCode": 0}, {"key": "1341268810:*********", "paymentID": *********, "amt": 15, "approveCode": 0}]}, {"chargeID": 1341268813, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1341268806, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "TR: Airport Service Charge (International)", "comment": "TR: Airport Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268813:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1341268806, "codeType": "AIR", "amt": 445, "curr": "AED", "originalAmt": 445, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "FZ 728 IST-DXB 14May2025 Wed 18:35 00:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268806:*********", "paymentID": *********, "amt": 445, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268819, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268819:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1341268821, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:13", "billDate": "2025-05-11T12:48:45", "desc": "Special Service Request:FRST-8F", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268821:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268815, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341268806, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268817, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341268806, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "WCHR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-08T03:26:42", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "POSAirport": "DXB", "bonusMiles": 0, "bonusTierMiles": 0, "workStationID": "DXB2C124", "promoMiles": 0, "promoTierMiles": 0, "parameter1Name": "BATCH_ID", "parameter1Value": "12f330c162ea24a46518094a1c21t8r12euaf09d6455", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-08T09:15:00\",\"fltNum\":\"751\",\"depDate\":\"2025-05-08T00:00:00\",\"board\":\"DXB\",\"off\":\"SAW\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1140347837_DXBSAW"}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1326980485, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980491, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1326980485, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980491:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980489, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1326980485, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980489:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1326980488, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1326980485, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980488:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1326980490, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1326980485, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980490:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1326980485, "codeType": "AIR", "amt": 325, "curr": "AED", "originalAmt": 325, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1326980485:*********", "paymentID": *********, "amt": 325, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332124617, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181221", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332124617:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980486, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1326980485, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980514, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 6, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:44", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": **********, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:45", "desc": "Airport Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980496, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1341268802, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:45", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980495, "paymentMap": [{"key": "1341268802:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341268803, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": **********, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:45", "desc": "International Flights Security Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980497, "paymentMap": [{"key": "1341268803:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1326980497, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1326980493, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980497:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1326980495, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1326980493, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980495:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980496, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1326980493, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980496:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1326980493, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -435, "curr": "AED", "originalAmt": -435, "originalCurr": "AED", "status": 0, "billDate": "2025-05-11T12:48:44", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980493, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -435, "approveCode": 0}]}, {"chargeID": 1326980493, "codeType": "AIR", "amt": 435, "curr": "AED", "originalAmt": 435, "originalCurr": "AED", "status": 0, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980493:*********", "paymentID": *********, "amt": 435, "approveCode": 0}]}, {"chargeID": 1341268797, "codeType": "FRST", "taxChargeID": **********, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T12:48:44", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332124621, "paymentMap": [{"key": "1341268797:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1332124621, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181263", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332124621:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1341268805, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:45", "billDate": "2025-05-11T12:48:45", "desc": "CancelNoRefund FZ 728 IST - DXB 12.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268805:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1341268800, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:45", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980494, "paymentMap": []}, {"chargeID": 1326980494, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1326980493, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341268804, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:45", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980515, "paymentMap": [], "PFID": "181263"}, {"chargeID": 1326980515, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263"}]}, {"recNum": 3, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "WCHR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-08T03:27:35", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "POSAirport": "DXB", "bonusMiles": 0, "bonusTierMiles": 0, "workStationID": "DXB2C124", "promoMiles": 0, "promoTierMiles": 0, "parameter1Name": "BATCH_ID", "parameter1Value": "8f1zp8u14dm9v968e168d7s4h155b56cb79f348293f5", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-08T09:15:00\",\"fltNum\":\"751\",\"depDate\":\"2025-05-08T00:00:00\",\"board\":\"DXB\",\"off\":\"SAW\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1140347836_DXBSAW"}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1326980470, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1326980476, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1326980470, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980476:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980475, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1326980470, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980475:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1326980473, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1326980470, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980473:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1326980472, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1326980470, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980472:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980470, "codeType": "AIR", "amt": 325, "curr": "AED", "originalAmt": 325, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1326980470:*********", "paymentID": *********, "amt": 325, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332124616, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181221", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332124616:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980471, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1326980470, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980512, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 7, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:43", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": **********, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:44", "desc": "International Flights Security Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980482, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1341268766, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:44", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980483, "paymentMap": [{"key": "1341268766:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1341268768, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": **********, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:44", "desc": "Airport Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980481, "paymentMap": [{"key": "1341268768:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1326980481, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1326980478, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980481:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1326980483, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1326980478, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980483:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1326980482, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1326980478, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980482:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1326980478, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -435, "curr": "AED", "originalAmt": -435, "originalCurr": "AED", "status": 0, "billDate": "2025-05-11T12:48:43", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980478, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -435, "approveCode": 0}]}, {"chargeID": 1326980478, "codeType": "AIR", "amt": 435, "curr": "AED", "originalAmt": 435, "originalCurr": "AED", "status": 0, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980478:*********", "paymentID": *********, "amt": 435, "approveCode": 0}]}, {"chargeID": 1341268762, "codeType": "FRST", "taxChargeID": **********, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T12:48:43", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332124620, "paymentMap": [{"key": "1341268762:*********", "paymentID": *********, "amt": -60, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1332124620, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181263", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332124620:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1341268769, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "CancelNoRefund FZ 728 IST - DXB 12.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268769:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1341268767, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:44", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980479, "paymentMap": []}, {"chargeID": 1326980479, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1326980478, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341268765, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:44", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980513, "paymentMap": [], "PFID": "181263"}, {"chargeID": 1326980513, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263"}]}, {"recNum": 11, "charges": [{"chargeID": 1341268779, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:18", "billDate": "2025-05-11T12:48:44", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268779:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-11T12:48:44"}, {"chargeID": 1341269967, "codeType": "WCHR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T12:49:40", "billDate": "2025-05-11T12:49:46", "desc": "Special Service Request", "comment": "WHEELCHAIR", "reasonID": 12, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1346106978, "codeType": "WCHR", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T12:49:40", "billDate": "2025-05-11T12:49:46", "desc": "Special Service Request", "comment": "WHEELCHAIR", "reasonID": 25, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341269967, "paymentMap": [], "POSAirport": "IST"}, {"chargeID": 1346107209, "codeType": "WCHS", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T13:24:20", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263", "saleCurrency": "EUR", "saleAmount": 0, "POSAirport": "IST", "workStationID": "IST2CKBN20", "parameter1Name": "BATCH_ID", "parameter1Value": "1901b1b3451dlf67ud74f46by43b4a1c8a2eacb67274", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-14T18:35:00\",\"fltNum\":\"728\",\"depDate\":\"2025-05-14T00:00:00\",\"board\":\"IST\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1144607525_ISTDXB"}, {"chargeID": 1341268771, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341268770, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268771:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341268772, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341268770, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268772:*********", "paymentID": *********, "amt": 140, "approveCode": 0}, {"key": "1341268772:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1341268773, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1341268770, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "M6: International Flights Security Charge", "comment": "M6: International Flights Security Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268773:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "1341268773:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1341268774, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1341268770, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "TR: Airport Service Charge (International)", "comment": "TR: Airport Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268774:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1341268770, "codeType": "AIR", "amt": 445, "curr": "AED", "originalAmt": 445, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "FZ 728 IST-DXB 14May2025 Wed 18:35 00:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268770:*********", "paymentID": *********, "amt": 445, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268777, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268777:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1341268778, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:08", "billDate": "2025-05-11T12:48:44", "desc": "Special Service Request:FRST-8E", "comment": "FLXID:FRST_Zone3_73B_73M_MID:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268778:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268775, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341268770, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268776, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341268770, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:44", "billDate": "2025-05-11T12:48:44", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1326980443, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980443:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1326980442, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980442:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980445, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980445:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 325, "curr": "AED", "originalAmt": 325, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 325, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326985021, "codeType": "PMNT", "amt": 166.2, "curr": "AED", "originalAmt": 166.2, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T14:37:49", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326985021:*********", "paymentID": *********, "amt": 166.2, "approveCode": 0}]}, {"chargeID": 1332130015, "codeType": "PMNT", "amt": 15.3, "curr": "AED", "originalAmt": 15.3, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T06:15:25", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332130015:*********", "paymentID": *********, "amt": 15.3, "approveCode": 0}]}, {"chargeID": 1332133174, "codeType": "PMNT", "amt": 5.8, "curr": "AED", "originalAmt": 5.8, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T06:17:57", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332133174:*********", "paymentID": *********, "amt": 5.8, "approveCode": 0}]}, {"chargeID": 1332124614, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181221", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332124614:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980441, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980508, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 5, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": **********, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:41", "desc": "Airport Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:41", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980450, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341268588, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": **********, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:41", "desc": "International Flights Security Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980452, "paymentMap": [{"key": "1341268588:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1341268689, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:41", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980453, "paymentMap": [{"key": "1341268689:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1326980453, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1326980448, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980453:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1326980452, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1326980448, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980452:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1326980450, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1326980448, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980450:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1326980448, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -435, "curr": "AED", "originalAmt": -435, "originalCurr": "AED", "status": 0, "billDate": "2025-05-11T12:48:41", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980448, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -435, "approveCode": 0}]}, {"chargeID": 1326980448, "codeType": "AIR", "amt": 435, "curr": "AED", "originalAmt": 435, "originalCurr": "AED", "status": 0, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980448:*********", "paymentID": *********, "amt": 435, "approveCode": 0}]}, {"chargeID": 1341268585, "codeType": "FRST", "taxChargeID": **********, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T12:48:41", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332124618, "paymentMap": [{"key": "1341268585:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1332124618, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181263", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332124618:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1341268692, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "CancelNoRefund FZ 728 IST - DXB 12.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268692:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1341268690, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:41", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980449, "paymentMap": []}, {"chargeID": 1326980449, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1326980448, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341268691, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:42", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980509, "paymentMap": [], "PFID": "181263"}, {"chargeID": 1326980509, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263"}]}, {"recNum": 9, "charges": [{"chargeID": 1341268729, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:18", "billDate": "2025-05-11T12:48:42", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268729:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-11T12:48:42"}, {"chargeID": 1341268694, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341268693, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268694:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341268695, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341268693, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268695:*********", "paymentID": *********, "amt": 50, "approveCode": 0}, {"key": "1341268695:*********", "paymentID": *********, "amt": 140, "approveCode": 0}]}, {"chargeID": 1341268696, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1341268693, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "M6: International Flights Security Charge", "comment": "M6: International Flights Security Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268696:*********", "paymentID": *********, "amt": 5, "approveCode": 0}, {"key": "1341268696:*********", "paymentID": *********, "amt": 15, "approveCode": 0}]}, {"chargeID": 1341268697, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1341268693, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "TR: Airport Service Charge (International)", "comment": "TR: Airport Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268697:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1341268693, "codeType": "AIR", "amt": 445, "curr": "AED", "originalAmt": 445, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "FZ 728 IST-DXB 14May2025 Wed 18:35 00:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268693:*********", "paymentID": *********, "amt": 445, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341274363, "codeType": "PMNT", "amt": 30.68, "curr": "AED", "originalAmt": 30.68, "originalCurr": "AED", "status": 1, "billDate": "2025-05-11T12:53:28", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341274363:*********", "paymentID": *********, "amt": 30.68, "approveCode": 0}]}, {"chargeID": 1341268703, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268703:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1341268708, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:00", "billDate": "2025-05-11T12:48:42", "desc": "Special Service Request:FRST-8C", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268708:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268698, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341268693, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268699, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341268693, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:42", "billDate": "2025-05-11T12:48:42", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1326980458, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980458:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1326980460, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980460:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1326980457, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980457:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 325, "curr": "AED", "originalAmt": 325, "originalCurr": "AED", "status": 1, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 325, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332124615, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181221", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332124615:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980456, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1326980510, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181221", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 8, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:17:45", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T06:17:45"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": **********, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:43", "desc": "Airport Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": **********, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:42", "desc": "International Flights Security Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980467, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1341268744, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:42", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980465, "paymentMap": [{"key": "1341268744:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341268746, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:42", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980468, "paymentMap": [{"key": "1341268746:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1326980467, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1326980463, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980467:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1326980465, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1326980463, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980465:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1326980468, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1326980463, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980468:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1326980463, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": -435, "curr": "AED", "originalAmt": -435, "originalCurr": "AED", "status": 0, "billDate": "2025-05-11T12:48:42", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980463, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -435, "approveCode": 0}]}, {"chargeID": 1326980463, "codeType": "AIR", "amt": 435, "curr": "AED", "originalAmt": 435, "originalCurr": "AED", "status": 0, "billDate": "2025-04-30T14:37:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1326980463:*********", "paymentID": *********, "amt": 435, "approveCode": 0}]}, {"chargeID": 1341268749, "codeType": "FRST", "taxChargeID": **********, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T12:48:43", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332124619, "paymentMap": [{"key": "1341268749:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1332124619, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T06:15:13", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181263", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332124619:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181263"}, {"chargeID": 1341268750, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "CancelNoRefund FZ 728 IST - DXB 12.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268750:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1341268745, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:42", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980464, "paymentMap": []}, {"chargeID": 1326980464, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1326980463, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341268748, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-11T12:48:43", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1326980511, "paymentMap": [], "PFID": "181263"}, {"chargeID": 1326980511, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-30T14:37:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181263"}]}, {"recNum": 10, "charges": [{"chargeID": 1341268760, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:18", "billDate": "2025-05-11T12:48:43", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268760:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-11T12:48:43"}, {"chargeID": 1341268752, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341268751, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268752:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341268753, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341268751, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268753:*********", "paymentID": *********, "amt": 140, "approveCode": 0}, {"key": "1341268753:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1341268754, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1341268751, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "M6: International Flights Security Charge", "comment": "M6: International Flights Security Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268754:*********", "paymentID": *********, "amt": 5, "approveCode": 0}, {"key": "1341268754:*********", "paymentID": *********, "amt": 15, "approveCode": 0}]}, {"chargeID": 1341268755, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1341268751, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "TR: Airport Service Charge (International)", "comment": "TR: Airport Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268755:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1341268751, "codeType": "AIR", "amt": 445, "curr": "AED", "originalAmt": 445, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "FZ 728 IST-DXB 14May2025 Wed 18:35 00:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268751:*********", "paymentID": *********, "amt": 445, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268758, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341268758:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1341268759, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:45:04", "billDate": "2025-05-11T12:48:43", "desc": "Special Service Request:FRST-8D", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341268759:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268756, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341268751, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341268757, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341268751, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T12:48:43", "billDate": "2025-05-11T12:48:43", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181263", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}