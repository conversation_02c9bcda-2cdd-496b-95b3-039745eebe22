{"seriesNum": "299", "PNR": "AFTUUG", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82680277", "bookDate": "2025-05-09T11:38:20", "modifyDate": "2025-06-02T14:35:50", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "db84e300897soai41512u529lf664818451579e6f53a", "securityGUID": "db84e300897soai41512u529lf664818451579e6f53a", "lastLoadGUID": "3696701BDCF1028EE0630A57380A4CBB", "isAsyncPNR": false, "MasterPNR": "AFTUUG", "segments": [{"segKey": "16087707:16087707:5/15/2025 10:20:00 AM", "LFID": 16087707, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087707", "org": "DXB", "dest": "KBV", "depTime": "2025-05-15T10:20:00", "depTimeGMT": "2025-05-15T06:20:00", "arrTime": "2025-05-15T19:35:00", "operCarrier": "FZ", "operFlightNum": "1481", "mrktCarrier": "FZ ", "mrktFlightNum": "1481", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181437, "depDate": "2025-05-15T10:20:00", "legKey": "16087707:181437:5/15/2025 10:20:00 AM", "customerKey": "36A6D8A442BBCBB0A882E2924E3D3361B38243AAF25BFD7A092A504F1952B019"}], "active": true}, {"segKey": "16087740:16087740:5/22/2025 10:45:00 PM", "LFID": 16087740, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16087740", "org": "KBV", "dest": "DXB", "depTime": "2025-05-22T22:45:00", "depTimeGMT": "2025-05-22T15:45:00", "arrTime": "2025-05-23T02:50:00", "operCarrier": "FZ", "operFlightNum": "1482", "mrktCarrier": "FZ ", "mrktFlightNum": "1482", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181479, "depDate": "2025-05-22T22:45:00", "legKey": "16087740:181479:5/22/2025 10:45:00 PM", "customerKey": "7E1220D8477D701B247DDCBD990399394A0D6AD84CB5DFD7F0B23276847ACFA7"}], "active": true, "changeType": "TK"}, {"segKey": "16087707:16087707:5/20/2025 10:20:00 AM", "LFID": 16087707, "depDate": "2025-05-20T00:00:00", "flightGroupId": "16087707", "org": "DXB", "dest": "KBV", "depTime": "2025-05-20T10:20:00", "depTimeGMT": "2025-05-20T06:20:00", "arrTime": "2025-05-20T19:35:00", "operCarrier": "FZ", "operFlightNum": "1481", "mrktCarrier": "FZ", "mrktFlightNum": "1481", "persons": [{"recNum": 5, "status": 5}], "legDetails": [{"PFID": 181437, "depDate": "2025-05-20T10:20:00", "legKey": "16087707:181437:5/20/2025 10:20:00 AM", "customerKey": "3AC154613FD8C5664E46F7571B2EFA838F50C8AE16954ACDCDE1C2036210371D"}], "active": true}, {"segKey": "16087740:16087740:5/27/2025 10:45:00 PM", "LFID": 16087740, "depDate": "2025-05-27T00:00:00", "flightGroupId": "16087740", "org": "KBV", "dest": "DXB", "depTime": "2025-05-27T22:45:00", "depTimeGMT": "2025-05-27T15:45:00", "arrTime": "2025-05-28T02:50:00", "operCarrier": "FZ", "operFlightNum": "1482", "mrktCarrier": "FZ", "mrktFlightNum": "1482", "persons": [{"recNum": 6, "status": 5}], "legDetails": [{"PFID": 181479, "depDate": "2025-05-27T22:45:00", "legKey": "16087740:181479:5/27/2025 10:45:00 PM", "customerKey": "5939D2E0CA85396C5344769AA021CF27DE027B8C6ECD3D38432B5F6FE93D1403"}], "active": true, "changeType": "TK"}, {"segKey": "16087707:16087707:5/22/2025 10:20:00 AM", "LFID": 16087707, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16087707", "org": "DXB", "dest": "KBV", "depTime": "2025-05-22T10:20:00", "depTimeGMT": "2025-05-22T06:20:00", "arrTime": "2025-05-22T19:35:00", "operCarrier": "FZ", "operFlightNum": "1481", "mrktCarrier": "FZ", "mrktFlightNum": "1481", "persons": [{"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181437, "depDate": "2025-05-22T10:20:00", "legKey": "16087707:181437:5/22/2025 10:20:00 AM", "customerKey": "FC5EF34C51AC4239914D53A3AC5E3838414B23E74656D6DFFBA2DAC2613BAC09"}], "active": true}, {"segKey": "16087740:16087740:5/29/2025 10:45:00 PM", "LFID": 16087740, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16087740", "org": "KBV", "dest": "DXB", "depTime": "2025-05-29T22:45:00", "depTimeGMT": "2025-05-29T15:45:00", "arrTime": "2025-05-30T02:50:00", "operCarrier": "FZ", "operFlightNum": "1482", "mrktCarrier": "FZ", "mrktFlightNum": "1482", "persons": [{"recNum": 4, "status": 0}], "legDetails": [{"PFID": 181479, "depDate": "2025-05-29T22:45:00", "legKey": "16087740:181479:5/29/2025 10:45:00 PM", "customerKey": "0B0C75913CA07B654940B6D4635AC37E80774042B3B3C40FDEB7E2E0370B0F24"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268411405, "fName": "MOHAMED", "lName": "FAROUK ALI ABUBAKR", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3, 4, 5, 6]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "N", "insuPurchasedate": "5/9/2025 11:38:20 AM", "provider": "<PERSON>", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "HH3Q2-WCP66-INS", "insuTransID": "HH3Q2-WCP66-INS/880d901e-f7e5-40ad-ba10-916952ffd1c6", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681de8d90007770000004a94#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-09T11:38:20"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "N", "insuPurchasedate": "5/9/2025 11:38:20 AM", "provider": "<PERSON>", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "HH3Q2-WCP66-INS", "insuTransID": "HH3Q2-WCP66-INS/880d901e-f7e5-40ad-ba10-916952ffd1c6", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681de8d90007770000004a94#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-09T11:38:20"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "kizhakethil.a", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/9/2025 11:38:20 AM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "HH3Q2-WCP66-INS/880d901e-f7e5-40ad-ba10-916952ffd1c6", "toRecNum": 5, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682420850007770000000406#268411405#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-14T04:49:41"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "kizhakethil.a", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "N", "insuPurchasedate": "5/9/2025 11:38:20 AM", "provider": "<PERSON>", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "HH3Q2-WCP66-INS/880d901e-f7e5-40ad-ba10-916952ffd1c6", "toRecNum": 6, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682420850007770000000406#268411405#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-14T04:49:41"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "<PERSON><PERSON><PERSON>", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/9/2025 11:38:20 AM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "HH3Q2-WCP66-INS/880d901e-f7e5-40ad-ba10-916952ffd1c6", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68281e04000778000000769c#268411405#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-17T05:28:54"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "<PERSON><PERSON><PERSON>", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/9/2025 11:38:20 AM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "HH3Q2-WCP66-INS/880d901e-f7e5-40ad-ba10-916952ffd1c6", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68281e04000778000000769c#268411405#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-17T05:28:54"}]}], "payments": [{"paymentID": *********, "paxID": 268898005, "method": "IPAY", "status": "1", "paidDate": "2025-05-14T04:58:21", "cardNum": "************0939", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 453.2, "baseCurr": "AED", "baseAmt": 453.2, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "726592", "reference": "23022443", "externalReference": "23022443", "tranId": "21394389", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21394389}, {"paymentID": *********, "paxID": 269245073, "method": "IPAY", "status": "1", "paidDate": "2025-05-17T05:32:23", "cardNum": "************4778", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 453.2, "baseCurr": "AED", "baseAmt": 453.2, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "056551", "reference": "23082439", "externalReference": "23082439", "tranId": "21457039", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21457039}, {"paymentID": *********, "paxID": 268411418, "method": "IPAY", "status": "1", "paidDate": "2025-05-09T11:38:25", "cardNum": "************0939", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1926.25, "baseCurr": "AED", "baseAmt": 1926.25, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "726575", "reference": "22931865", "externalReference": "22931865", "tranId": "21304780", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21304780}], "OAFlights": null, "physicalFlights": [{"key": "16087707:181437:2025-05-15T10:20:00 AM", "LFID": 16087707, "PFID": 181437, "org": "DXB", "dest": "KBV", "depDate": "2025-05-15T10:20:00", "depTime": "2025-05-15T10:20:00", "arrTime": "2025-05-15T19:35:00", "carrier": "FZ", "flightNum": "1481", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1481", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "KBV", "operatingCarrier": "FZ", "flightDuration": 22500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": false}, {"key": "16087707:181437:2025-05-20T10:20:00 AM", "LFID": 16087707, "PFID": 181437, "org": "DXB", "dest": "KBV", "depDate": "2025-05-20T10:20:00", "depTime": "2025-05-20T10:20:00", "arrTime": "2025-05-20T19:35:00", "carrier": "FZ", "flightNum": "1481", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1481", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "KBV", "operatingCarrier": "FZ", "flightDuration": 22500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": false}, {"key": "16087707:181437:2025-05-22T10:20:00 AM", "LFID": 16087707, "PFID": 181437, "org": "DXB", "dest": "KBV", "depDate": "2025-05-22T10:20:00", "depTime": "2025-05-22T10:20:00", "arrTime": "2025-05-22T19:35:00", "carrier": "FZ", "flightNum": "1481", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1481", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "KBV", "operatingCarrier": "FZ", "flightDuration": 22500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": false}, {"key": "16087740:181479:2025-05-22T10:45:00 PM", "LFID": 16087740, "PFID": 181479, "org": "KBV", "dest": "DXB", "depDate": "2025-05-22T22:45:00", "depTime": "2025-05-22T22:45:00", "arrTime": "2025-05-23T02:50:00", "carrier": "FZ", "flightNum": "1482", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1482", "flightStatus": "CLOSED", "originMetroGroup": "KBV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 25500, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/1/2025 2:51:03 PM"}, {"key": "16087740:181479:2025-05-27T10:45:00 PM", "LFID": 16087740, "PFID": 181479, "org": "KBV", "dest": "DXB", "depDate": "2025-05-27T22:45:00", "depTime": "2025-05-27T22:45:00", "arrTime": "2025-05-28T02:50:00", "carrier": "FZ", "flightNum": "1482", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1482", "flightStatus": "CLOSED", "originMetroGroup": "KBV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 25500, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/1/2025 2:51:04 PM"}, {"key": "16087740:181479:2025-05-29T10:45:00 PM", "LFID": 16087740, "PFID": 181479, "org": "KBV", "dest": "DXB", "depDate": "2025-05-29T22:45:00", "depTime": "2025-05-29T22:45:00", "arrTime": "2025-05-30T02:50:00", "carrier": "FZ", "flightNum": "1482", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1482", "flightStatus": "CLOSED", "originMetroGroup": "KBV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 25500, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/1/2025 2:51:04 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1345195912, "codeType": "INSU", "taxChargeID": 1345195846, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799952, "paymentMap": [{"key": "1345195912:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-14T04:49:42"}, {"chargeID": 1338799952, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799952:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1345195914, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1345195846, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "International Arrival and Departure Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799939, "paymentMap": [{"key": "1345195914:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1345195915, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1345195846, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799941, "paymentMap": [{"key": "1345195915:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345195916, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345195846, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799935, "paymentMap": [{"key": "1345195916:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345195917, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1345195846, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "Advance Passenger Processing User Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799934, "paymentMap": [{"key": "1345195917:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1345195909, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1345195846, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799940, "paymentMap": [{"key": "1345195909:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1345195910, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345195846, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799937, "paymentMap": [{"key": "1345195910:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1345195911, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1345195846, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799938, "paymentMap": [{"key": "1345195911:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1338799934, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1338799933, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Advance Passenger Processing User Charge", "comment": "Advance Passenger Processing User Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799934:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338799935, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338799933, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799935:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338799941, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338799933, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799941:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338799939, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1338799933, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "International Arrival and Departure Fees", "comment": "International Arrival and Departure Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799939:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338799938, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338799933, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799938:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1338799937, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338799933, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799937:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1338799940, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338799933, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799940:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1345195846, "codeType": "AIR", "amt": -520, "curr": "AED", "originalAmt": -520, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T04:49:42", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799933, "paymentMap": [{"key": "1345195846:*********", "paymentID": *********, "amt": -520, "approveCode": 0}]}, {"chargeID": 1338799933, "codeType": "AIR", "amt": 520, "curr": "AED", "originalAmt": 520, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T11:38:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799933:*********", "paymentID": *********, "amt": 520, "approveCode": 0}]}, {"chargeID": 1338800795, "codeType": "PMNT", "amt": 56.1, "curr": "AED", "originalAmt": 56.1, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T11:38:29", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338800795:*********", "paymentID": *********, "amt": 56.1, "approveCode": 0}]}, {"chargeID": 1345195918, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:42", "billDate": "2025-05-14T04:49:42", "desc": "CancelNoRefund FZ 1481 DXB - KBV 15.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195918:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1345195913, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1345195846, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799936, "paymentMap": []}, {"chargeID": 1338799936, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1338799933, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1345195848, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1345195846, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:42", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799955, "paymentMap": [], "PFID": "181437"}, {"chargeID": 1338799955, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181437"}]}, {"recNum": 2, "charges": [{"chargeID": 1345195925, "codeType": "INSU", "taxChargeID": 1345195919, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799954, "paymentMap": [{"key": "1345195925:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-14T04:49:43"}, {"chargeID": 1338799954, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799954:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1345195920, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1345195919, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "Advance Passenger Processing User Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799944, "paymentMap": [{"key": "1345195920:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1345195921, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1345195919, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "International Arrival and Departure Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799949, "paymentMap": [{"key": "1345195921:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1345195922, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345195919, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799945, "paymentMap": [{"key": "1345195922:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345195926, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345195919, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799948, "paymentMap": [{"key": "1345195926:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1345195924, "codeType": "TAX", "taxID": 11652, "taxCode": "TS", "taxChargeID": 1345195919, "amt": -50, "curr": "AED", "originalAmt": -50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "Passenger Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799947, "paymentMap": [{"key": "1345195924:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1338799947, "codeType": "TAX", "taxID": 11652, "taxCode": "TS", "taxChargeID": 1338799943, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799947:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1338799945, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338799943, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799945:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338799949, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1338799943, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "International Arrival and Departure Fees", "comment": "International Arrival and Departure Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799949:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338799944, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1338799943, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Advance Passenger Processing User Charge", "comment": "Advance Passenger Processing User Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799944:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338799948, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338799943, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799948:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1345195919, "codeType": "AIR", "amt": -520, "curr": "AED", "originalAmt": -520, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T04:49:42", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799943, "paymentMap": [{"key": "1345195919:*********", "paymentID": *********, "amt": -520, "approveCode": 0}]}, {"chargeID": 1338799943, "codeType": "AIR", "amt": 520, "curr": "AED", "originalAmt": 520, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T11:38:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338799943:*********", "paymentID": *********, "amt": 520, "approveCode": 0}]}, {"chargeID": 1345195928, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "CancelNoRefund FZ 1482 KBV - DXB 22.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195928:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1345195927, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1345195919, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799946, "paymentMap": []}, {"chargeID": 1338799946, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1338799943, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1345195923, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1345195919, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T04:49:43", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338799956, "paymentMap": [], "PFID": "181479"}, {"chargeID": 1338799956, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:38:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181479"}]}, {"recNum": 3, "charges": [{"chargeID": 1345195940, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:48:09", "billDate": "2025-05-14T04:49:44", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195940:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T04:49:44"}, {"chargeID": 1349743562, "codeType": "INSU", "taxChargeID": 1349743557, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:48:09", "billDate": "2025-05-17T05:28:55", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195940, "paymentMap": [{"key": "1349743562:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1345195932, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345195929, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195932:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345195933, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1345195929, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "G8: International Arrival and Departure Fees", "comment": "G8: International Arrival and Departure Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195933:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1345195934, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1345195929, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195934:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1345195935, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1345195929, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "E7: Advance Passenger Processing User Charge", "comment": "E7: Advance Passenger Processing User Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195935:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1345195936, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345195929, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195936:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1345195930, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1345195929, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195930:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1345195931, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1345195929, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195931:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1349743558, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1349743557, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "G8: International Arrival and Departure Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195933, "paymentMap": [{"key": "1349743558:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1349743559, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349743557, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195932, "paymentMap": [{"key": "1349743559:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1349743563, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1349743557, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195930, "paymentMap": [{"key": "1349743563:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1349743564, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349743557, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195936, "paymentMap": [{"key": "1349743564:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1349743565, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1349743557, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "E7: Advance Passenger Processing User Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195935, "paymentMap": [{"key": "1349743565:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1349743566, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1349743557, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195934, "paymentMap": [{"key": "1349743566:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1349743567, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1349743557, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195931, "paymentMap": [{"key": "1349743567:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345195929, "codeType": "AIR", "amt": 530, "curr": "AED", "originalAmt": 530, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "FZ 1481 DXB-KBV 22May2025 Thu 10:20 19:35\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195929:*********", "paymentID": *********, "amt": 530, "approveCode": 0}]}, {"chargeID": 1349743557, "codeType": "AIR", "amt": -530, "curr": "AED", "originalAmt": -530, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:54", "desc": "FZ 1481 DXB-KBV 22May2025 Thu 10:20 19:35\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195929, "paymentMap": [{"key": "1349743557:*********", "paymentID": *********, "amt": -530, "approveCode": 0}]}, {"chargeID": 1345210922, "codeType": "PMNT", "amt": 13.2, "curr": "AED", "originalAmt": 13.2, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T04:58:27", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345210922:*********", "paymentID": *********, "amt": 13.2, "approveCode": 0}]}, {"chargeID": 1345195939, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:44", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195939:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1349743568, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:55", "billDate": "2025-05-17T05:28:55", "desc": "CancelNoRefund FZ 1481 DXB - KBV 22.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743568:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1345195937, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1345195929, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349743561, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1349743557, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195937, "paymentMap": []}, {"chargeID": 1345195938, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1345195929, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-14T04:49:43", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181437"}, {"chargeID": 1349743560, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1349743557, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:43", "billDate": "2025-05-17T05:28:55", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195938, "paymentMap": [], "PFID": "181437"}]}, {"recNum": 4, "charges": [{"chargeID": 1345195950, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:48:09", "billDate": "2025-05-14T04:49:44", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195950:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T04:49:44"}, {"chargeID": 1349743573, "codeType": "INSU", "taxChargeID": 1349743569, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:48:09", "billDate": "2025-05-17T05:28:56", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195950, "paymentMap": [{"key": "1349743573:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1345195942, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1345195941, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "G8: International Arrival and Departure Fees", "comment": "G8: International Arrival and Departure Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195942:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1345195943, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345195941, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195943:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345195944, "codeType": "TAX", "taxID": 11652, "taxCode": "TS", "taxChargeID": 1345195941, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "TS: Passenger Service Charge (International)", "comment": "TS: Passenger Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195944:*********", "paymentID": *********, "amt": 42.58, "approveCode": 0}, {"key": "1345195944:*********", "paymentID": *********, "amt": 7.42, "approveCode": 0}]}, {"chargeID": 1345195945, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1345195941, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "E7: Advance Passenger Processing User Charge", "comment": "E7: Advance Passenger Processing User Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195945:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1345195946, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345195941, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195946:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1349743572, "codeType": "TAX", "taxID": 11652, "taxCode": "TS", "taxChargeID": 1349743569, "amt": -50, "curr": "AED", "originalAmt": -50, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "TS: Passenger Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195944, "paymentMap": [{"key": "1349743572:*********", "paymentID": *********, "amt": -42.58, "approveCode": 0}, {"key": "1349743572:*********", "paymentID": *********, "amt": -7.42, "approveCode": 0}]}, {"chargeID": 1349743574, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349743569, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195946, "paymentMap": [{"key": "1349743574:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1349743577, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1349743569, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "E7: Advance Passenger Processing User Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195945, "paymentMap": [{"key": "1349743577:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1349743570, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1349743569, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "G8: International Arrival and Departure Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195942, "paymentMap": [{"key": "1349743570:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1349743571, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349743569, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195943, "paymentMap": [{"key": "1349743571:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345195941, "codeType": "AIR", "amt": 530, "curr": "AED", "originalAmt": 530, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "FZ 1482 KBV-DXB 29May2025 Thu 22:45 02:50\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195941:*********", "paymentID": *********, "amt": 530, "approveCode": 0}]}, {"chargeID": 1349743569, "codeType": "AIR", "amt": -530, "curr": "AED", "originalAmt": -530, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:55", "desc": "FZ 1482 KBV-DXB 29May2025 Thu 22:45 02:50\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195941, "paymentMap": [{"key": "1349743569:*********", "paymentID": *********, "amt": -530, "approveCode": 0}]}, {"chargeID": 1345195949, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345195949:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1349743578, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "CancelNoRefund FZ 1482 KBV - DXB 29.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743578:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1345195947, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1345195941, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349743575, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1349743569, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195947, "paymentMap": []}, {"chargeID": 1345195948, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1345195941, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-14T04:49:44", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181479"}, {"chargeID": 1349743576, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1349743569, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-14T04:49:44", "billDate": "2025-05-17T05:28:56", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1345195948, "paymentMap": [], "PFID": "181479"}]}, {"recNum": 5, "charges": [{"chargeID": 1349743590, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:26:33", "billDate": "2025-05-17T05:28:57", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743590:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-17T05:28:57"}, {"chargeID": 1349743580, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1349743579, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743580:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1349743581, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1349743579, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743581:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1349743582, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349743579, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743582:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1349743583, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1349743579, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "G8: International Arrival and Departure Fees", "comment": "G8: International Arrival and Departure Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743583:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1349743584, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1349743579, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743584:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1349743585, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1349743579, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:57", "desc": "E7: Advance Passenger Processing User Charge", "comment": "E7: Advance Passenger Processing User Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743585:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1349743586, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349743579, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743586:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1349743579, "codeType": "AIR", "amt": 540, "curr": "AED", "originalAmt": 540, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:56", "billDate": "2025-05-17T05:28:56", "desc": "FZ 1481 DXB-KBV 20May2025 Tue 10:20 19:35\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743579:*********", "paymentID": *********, "amt": 540, "approveCode": 0}]}, {"chargeID": 1349746440, "codeType": "PMNT", "amt": 13.2, "curr": "AED", "originalAmt": 13.2, "originalCurr": "AED", "status": 1, "billDate": "2025-05-17T05:32:30", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349746440:*********", "paymentID": *********, "amt": 13.2, "approveCode": 0}]}, {"chargeID": 1349743589, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743589:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1349743587, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1349743579, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349743588, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1349743579, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181437"}, {"chargeID": 1353408511, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-20T03:24:16", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181437", "ssrCommentId": "*********"}]}, {"recNum": 6, "charges": [{"chargeID": 1349743600, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:26:33", "billDate": "2025-05-17T05:28:57", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743600:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-17T05:28:57"}, {"chargeID": 1349743592, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1349743591, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "G8: International Arrival and Departure Fees", "comment": "G8: International Arrival and Departure Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743592:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1349743593, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349743591, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743593:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1349743594, "codeType": "TAX", "taxID": 11652, "taxCode": "TS", "taxChargeID": 1349743591, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "TS: Passenger Service Charge (International)", "comment": "TS: Passenger Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743594:*********", "paymentID": *********, "amt": 42.58, "approveCode": 0}, {"key": "1349743594:*********", "paymentID": *********, "amt": 7.42, "approveCode": 0}]}, {"chargeID": 1349743595, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1349743591, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "E7: Advance Passenger Processing User Charge", "comment": "E7: Advance Passenger Processing User Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743595:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1349743596, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349743591, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743596:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1349743591, "codeType": "AIR", "amt": 540, "curr": "AED", "originalAmt": 540, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "FZ 1482 KBV-DXB 27May2025 Tue 22:45 02:50\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743591:*********", "paymentID": *********, "amt": 277.58, "approveCode": 0}, {"key": "1349743591:*********", "paymentID": *********, "amt": 262.42, "approveCode": 0}]}, {"chargeID": 1349743599, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349743599:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1349743597, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1349743591, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349743598, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1349743591, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-17T05:28:57", "billDate": "2025-05-17T05:28:57", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181479"}]}], "parentPNRs": [], "childPNRs": []}