{"seriesNum": "299", "PNR": "N18FRD", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82994802", "bookDate": "2025-05-20T18:40:31", "modifyDate": "2025-05-22T21:05:35", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "05d84b32c3cf4f75z861hce6uet1026aobbe49b9822b", "securityGUID": "05d84b32c3cf4f75z861hce6uet1026aobbe49b9822b", "lastLoadGUID": "4471ba6f-9490-4409-9fd4-eeb47ab82260", "isAsyncPNR": false, "MasterPNR": "N18FRD", "segments": [{"segKey": "17093580:17093580:5/23/2025 12:35:00 AM", "LFID": 17093580, "depDate": "2025-05-23T00:00:00", "flightGroupId": "17093580", "org": "DXB", "dest": "LHE", "depTime": "2025-05-23T00:35:00", "depTimeGMT": "2025-05-22T20:35:00", "arrTime": "2025-05-23T04:45:00", "operCarrier": "FZ", "operFlightNum": "359", "mrktCarrier": "FZ ", "mrktFlightNum": "359", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 187837, "depDate": "2025-05-23T00:35:00", "legKey": "17093580:187837:5/23/2025 12:35:00 AM", "customerKey": "476D6CFA7136ADFEB43D4AD7ABCD64EA4202A6491630DD71B78E29E8E0F4B2A8"}], "active": true}, {"segKey": "17093580:17093580:5/24/2025 12:35:00 AM", "LFID": 17093580, "depDate": "2025-05-24T00:00:00", "flightGroupId": "17093580", "org": "DXB", "dest": "LHE", "depTime": "2025-05-24T00:35:00", "depTimeGMT": "2025-05-23T20:35:00", "arrTime": "2025-05-24T04:45:00", "operCarrier": "FZ", "operFlightNum": "359", "mrktCarrier": "FZ", "mrktFlightNum": "359", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 187837, "depDate": "2025-05-24T00:35:00", "legKey": "17093580:187837:5/24/2025 12:35:00 AM", "customerKey": "B88AC6823C39FF8C3CB52E046003AB876C7F9BCFEA707F84D0B871B55DCB6424"}], "active": true}], "persons": [{"paxID": 269623267, "fName": "DANIEL", "lName": "KHAN", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1980-03-16T00:00:00", "FFNum": "905736101", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "V", "insuPurchasedate": "5/20/2025 6:40:31 PM", "provider": "<PERSON>", "status": 0, "fareClass": "V", "operFareClass": "V", "FBC": "VOL6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "discloseEmergencyContact": 0, "insuConfNum": "RVRJ9-7EP4P-INS", "insuTransID": "RVRJ9-7EP4P-INS/281872cb-d150-49a2-922f-c352226cb166", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682ccb140007770000003b67#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-20T18:40:31"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "anandkrishnan.k", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/20/2025 6:40:31 PM", "provider": "<PERSON>", "status": 5, "fareClass": "H", "operFareClass": "H", "FBC": "HOB6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "RVRJ9-7EP4P-INS/281872cb-d150-49a2-922f-c352226cb166", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682f8e120007780000010de3#269623267#1#ENT#SFQE#CHANGE", "fareTypeID": 21, "channelID": 1, "bookDate": "2025-05-22T20:53:12"}]}], "payments": [{"paymentID": 209976456, "paxID": 269870707, "method": "DSCV", "status": "2", "paidDate": "2025-05-22T21:02:30", "cardNum": "************0010", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 618, "baseCurr": "AED", "baseAmt": 618, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "reference": "23192431", "externalReference": "23192431", "tranId": "21575390", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEAED", "exchangeRate": "1", "resExternalPaymentID": 21575390}, {"paymentID": 209718231, "paxID": 269624665, "method": "DSCV", "status": "2", "paidDate": "2025-05-20T18:40:38", "cardNum": "************0010", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 124.63, "baseCurr": "AED", "baseAmt": 448.77, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>han", "reference": "23151203", "externalReference": "23151203", "tranId": "21529923", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "MCCMPGSPEUSD", "exchangeRate": "0.27771423", "resExternalPaymentID": 21529923}, {"paymentID": *********, "paxID": 269870709, "method": "DSCV", "status": "1", "paidDate": "2025-05-22T21:05:27", "cardNum": "************0010", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 618, "baseCurr": "AED", "baseAmt": 618, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "02236R", "reference": "23192450", "externalReference": "23192450", "tranId": "21575390", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEAED", "exchangeRate": "1", "resExternalPaymentID": 21575390}, {"paymentID": *********, "paxID": 269624656, "method": "VISA", "status": "1", "paidDate": "2025-05-20T18:56:04", "cardNum": "************7752", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 124.63, "baseCurr": "AED", "baseAmt": 448.77, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "627053", "reference": "23151359", "externalReference": "23151359", "tranId": "21529923", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgmccecomusdallniall001", "exchangeRate": "0.27771423", "resExternalPaymentID": 21529923}], "OAFlights": null, "physicalFlights": [{"key": "17093580:187837:2025-05-23T12:35:00 AM", "LFID": 17093580, "PFID": 187837, "org": "DXB", "dest": "LHE", "depDate": "2025-05-23T00:35:00", "depTime": "2025-05-23T00:35:00", "arrTime": "2025-05-23T04:45:00", "carrier": "FZ", "flightNum": "359", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "359", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "LHE", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Lahore", "isActive": false}, {"key": "17093580:187837:2025-05-24T12:35:00 AM", "LFID": 17093580, "PFID": 187837, "org": "DXB", "dest": "LHE", "depDate": "2025-05-24T00:35:00", "depTime": "2025-05-24T00:35:00", "arrTime": "2025-05-24T04:45:00", "carrier": "FZ", "flightNum": "359", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "359", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "LHE", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Lahore", "isActive": false}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1358103602, "codeType": "NACT", "taxChargeID": 1358103601, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T20:53:13", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1358078780, "paymentMap": [], "PFID": "187837", "ssrCommentId": "*********", "isSSR": true, "parameter1Name": "BATCH_ID", "parameter1Value": "e23340603cc5468791c1d826ndmeues6x369n1ra437d", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-23T00:35:00\",\"fltNum\":\"359\",\"depDate\":\"2025-05-23T00:00:00\",\"board\":\"DXB\",\"off\":\"LHE\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1149415656_DXBLHE", "ChargeBookDate": "2025-05-22T20:53:13"}, {"chargeID": 1358078780, "codeType": "NACT", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T20:17:29", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187837", "POSAirport": "DXB", "workStationID": "DXB2G08A", "ssrCommentId": "*********", "parameter1Name": "BATCH_ID", "parameter1Value": "e23340603cc5468791c1d826ndmeues6x369n1ra437d", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-23T00:35:00\",\"fltNum\":\"359\",\"depDate\":\"2025-05-23T00:00:00\",\"board\":\"DXB\",\"off\":\"LHE\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1149415656_DXBLHE"}, {"chargeID": 1358103607, "codeType": "INSU", "taxChargeID": 1358103601, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "INSU", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777650, "paymentMap": [{"key": "1358103607:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1354777650, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777650:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1358103603, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1358103601, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "Passenger Facilities Charge.", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777588, "paymentMap": [{"key": "1358103603:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1358103605, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1358103601, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "Advanced passenger information fee", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777584, "paymentMap": [{"key": "1358103605:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1358103606, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1358103601, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "Passengers Security & Safety Service Fees", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777649, "paymentMap": [{"key": "1358103606:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1358103608, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1358103601, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "Passenger Service Charge (Intl)", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777587, "paymentMap": [{"key": "1358103608:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1358103609, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1358103601, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "YQ - DUMMY", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777586, "paymentMap": [{"key": "1358103609:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1354777588, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1354777583, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777588:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1354777584, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1354777583, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777584:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1354777586, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1354777583, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777586:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1354777649, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1354777583, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777649:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1354777587, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1354777583, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777587:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1358103601, "codeType": "AIR", "amt": -170, "curr": "AED", "originalAmt": -170, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T20:53:12", "desc": "WEB:AIR", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777583, "paymentMap": [{"key": "1358103601:*********", "paymentID": *********, "amt": -170, "approveCode": 0}]}, {"chargeID": 1354777583, "codeType": "AIR", "amt": 170, "curr": "AED", "originalAmt": 170, "originalCurr": "AED", "status": 0, "billDate": "2025-05-20T18:40:31", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354777583:*********", "paymentID": *********, "amt": 170, "approveCode": 0}]}, {"chargeID": 1354797580, "codeType": "PMNT", "amt": 13.07, "curr": "AED", "originalAmt": 13.07, "originalCurr": "AED", "status": 0, "billDate": "2025-05-20T18:56:07", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354797580:*********", "paymentID": *********, "amt": 13.07, "approveCode": 0}]}, {"chargeID": *********0, "codeType": "PNLT", "amt": 270, "curr": "AED", "originalAmt": 270, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "CancelNoRefund FZ 359 DXB - LHE 23.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********0:*********", "paymentID": *********, "amt": 270, "approveCode": 0}]}, {"chargeID": 1358103604, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1358103601, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T20:53:13", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MDFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1354777585, "paymentMap": []}, {"chargeID": 1354777585, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1354777583, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T18:40:31", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 2, "charges": [{"chargeID": 1358103620, "codeType": "NACT", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:50:37", "billDate": "2025-05-22T20:53:13", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "187837", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********", "isSSR": true, "parameter1Name": "BATCH_ID", "parameter1Value": "e23340603cc5468791c1d826ndmeues6x369n1ra437d", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-23T00:35:00\",\"fltNum\":\"359\",\"depDate\":\"2025-05-23T00:00:00\",\"board\":\"DXB\",\"off\":\"LHE\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1149415656_DXBLHE", "ChargeBookDate": "2025-05-22T20:53:13"}, {"chargeID": *********9, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:50:37", "billDate": "2025-05-22T20:53:13", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********9:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}"}, {"chargeID": *********2, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********1, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********2:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": *********3, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": *********1, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********3:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********4, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********1, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********4:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********5, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": *********1, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********5:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": *********6, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********1, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********6:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": *********1, "codeType": "AIR", "amt": 440, "curr": "AED", "originalAmt": 440, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "FZ 359 DXB-LHE 24May2025 Sat 00:35 04:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 200, "tierPoints": 200, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "*********1:*********", "paymentID": *********, "amt": 274.3, "approveCode": 0}, {"key": "*********1:*********", "paymentID": *********, "amt": 165.7, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1358110346, "codeType": "PMNT", "amt": 18, "curr": "AED", "originalAmt": 18, "originalCurr": "AED", "status": 1, "billDate": "2025-05-22T21:05:35", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358110346:*********", "paymentID": *********, "amt": 18, "approveCode": 0}]}, {"chargeID": *********8, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********8:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********7, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********1, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-22T20:53:13", "billDate": "2025-05-22T20:53:13", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}