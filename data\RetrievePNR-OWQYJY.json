{"seriesNum": "299", "PNR": "OWQYJY", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "83179008", "bookDate": "2025-05-27T14:03:20", "modifyDate": "2025-05-28T05:38:12", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 5, "activeSegCount": 2, "webBookingID": "eebc74df768b5ke4b9w143990et3oft3uef53aa8cd44", "securityGUID": "eebc74df768b5ke4b9w143990et3oft3uef53aa8cd44", "lastLoadGUID": "b419776e-0214-4ef5-8e68-356d38a3ea4f", "isAsyncPNR": false, "MasterPNR": "OWQYJY", "segments": [{"segKey": "16087926:16087926:7/4/2025 3:25:00 PM", "LFID": 16087926, "depDate": "2025-07-04T00:00:00", "flightGroupId": "16087926", "org": "SOF", "dest": "DXB", "depTime": "2025-07-04T15:25:00", "depTimeGMT": "2025-07-04T12:25:00", "arrTime": "2025-07-04T21:25:00", "operCarrier": "FZ", "operFlightNum": "1758", "mrktCarrier": "FZ ", "mrktFlightNum": "1758", "persons": [{"recNum": 7, "status": 1}, {"recNum": 10, "status": 1}, {"recNum": 6, "status": 1}, {"recNum": 8, "status": 1}, {"recNum": 9, "status": 1}], "legDetails": [{"PFID": 181676, "depDate": "2025-07-04T15:25:00", "legKey": "16087926:181676:7/4/2025 3:25:00 PM", "customerKey": "CCDB593DAB6E8166F4A4183C53F1D68BDA5715CA260EA70BAC5E1E04E76C2CD7"}], "active": true}, {"segKey": "16087936:16087936:6/29/2025 10:00:00 AM", "LFID": 16087936, "depDate": "2025-06-29T00:00:00", "flightGroupId": "16087936", "org": "DXB", "dest": "SOF", "depTime": "2025-06-29T10:00:00", "depTimeGMT": "2025-06-29T06:00:00", "arrTime": "2025-06-29T14:25:00", "operCarrier": "FZ", "operFlightNum": "1757", "mrktCarrier": "FZ ", "mrktFlightNum": "1757", "persons": [{"recNum": 1, "status": 1}, {"recNum": 4, "status": 1}, {"recNum": 2, "status": 1}, {"recNum": 5, "status": 1}, {"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181686, "depDate": "2025-06-29T10:00:00", "legKey": "16087936:181686:6/29/2025 10:00:00 AM", "customerKey": "454E6ED00DD07BB5FB24100F49ACE7D9EC6B3C6E834E7D4DA31B89490E881ADD"}], "active": true}], "persons": [{"paxID": 270335672, "fName": "KHALED", "lName": "OTHMAN", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "2011-09-03T00:00:00", "FFNum": "570882443", "FFTier": "SSURFER", "TierID": "8", "recNum": [1, 7]}, {"paxID": 270335671, "fName": "YARA", "lName": "OTHMAN", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "2007-05-01T00:00:00", "FFNum": "570882421", "FFTier": "BLUE", "TierID": "3", "recNum": [4, 10]}, {"paxID": 270335669, "fName": "JAMAL", "lName": "OTHMAN", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1975-01-05T00:00:00", "nationality": "275", "FFNum": "00419382913", "FFTier": "BLUE", "TierID": "3", "recNum": [2, 6]}, {"paxID": 270335670, "fName": "HEBA", "lName": "OMAR", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [5, 8]}, {"paxID": 270335673, "fName": "OMAR", "lName": "OTHMAN", "title": "MSTR", "PTCID": 6, "gender": "M", "DOB": "2014-03-26T00:00:00", "FFNum": "570882491", "FFTier": "SSURFER", "TierID": "8", "recNum": [3, 9]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#4#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835c3b30007770000003e15#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#5#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#3#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#2#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835c3b30007770000003e15#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#4#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#2#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#5#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 2:03:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "P6A9H-VYLMH-INS/83cfd6d6-ba15-495e-9d2a-ee1e893fdbb7", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835c3b30007770000003e15#3#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T14:03:20"}]}], "payments": [{"paymentID": 210469601, "paxID": 270340841, "method": "VISA", "status": "2", "paidDate": "2025-05-27T14:03:26", "cardNum": "************5686", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 11055.5, "baseCurr": "AED", "baseAmt": 11055.5, "userID": "smartrez", "channelID": 5, "cardHolderName": "<PERSON>", "reference": "23282787", "externalReference": "23282787", "tranId": "21663091", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21663091}, {"paymentID": *********, "paxID": 270390662, "method": "VISA", "status": "1", "paidDate": "2025-05-28T05:38:09", "cardNum": "************5686", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 11055.5, "baseCurr": "AED", "baseAmt": 11055.5, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "038277", "reference": "23293280", "externalReference": "23293280", "tranId": "21673496", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21673496}], "OAFlights": null, "physicalFlights": [{"key": "16087936:181686:2025-06-29T10:00:00 AM", "LFID": 16087936, "PFID": 181686, "org": "DXB", "dest": "SOF", "depDate": "2025-06-29T10:00:00", "depTime": "2025-06-29T10:00:00", "arrTime": "2025-06-29T14:25:00", "carrier": "FZ", "flightNum": "1757", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1757", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "SOF", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Sofia", "isActive": true}, {"key": "16087926:181676:2025-07-04T03:25:00 PM", "LFID": 16087926, "PFID": 181676, "org": "SOF", "dest": "DXB", "depDate": "2025-07-04T15:25:00", "depTime": "2025-07-04T15:25:00", "arrTime": "2025-07-04T21:25:00", "carrier": "FZ", "flightNum": "1758", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1758", "flightStatus": "OPEN", "originMetroGroup": "SOF", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 18000, "reaccomChangeAlert": false, "originName": "Sofia", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1364305074, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305074:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305032, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364305031, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305032:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364305035, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305031, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305035:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305036, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305031, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305036:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305033, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364305031, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305033:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364305034, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364305031, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305034:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305031, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305031:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305037, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305031, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305087, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 7, "charges": [{"chargeID": 1364305076, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305076:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305043, "codeType": "TAX", "taxID": 12652, "taxCode": "BG", "taxChargeID": 1364305039, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Charge (International)", "comment": "Passenger Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305043:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364305042, "codeType": "TAX", "taxID": 12650, "taxCode": "ZF", "taxChargeID": 1364305039, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305042:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364305040, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305039, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305040:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305041, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305039, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305041:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305039, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305039:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305044, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305039, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305088, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1364305070, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305070:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305020, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305016, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305020:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305018, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364305016, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305018:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364305021, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305016, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305021:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305017, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364305016, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305017:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364305019, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364305016, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305019:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305016, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305016:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305022, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305016, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305085, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 10, "charges": [{"chargeID": 1364305072, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305072:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305028, "codeType": "TAX", "taxID": 12652, "taxCode": "BG", "taxChargeID": 1364305024, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Charge (International)", "comment": "Passenger Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305028:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364305025, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305024, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305025:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305026, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305024, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305026:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305027, "codeType": "TAX", "taxID": 12650, "taxCode": "ZF", "taxChargeID": 1364305024, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305027:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364305024, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305024:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305029, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305024, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305086, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1364305062, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305062:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364304967, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364304965, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304967:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364304990, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364304965, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304990:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364304989, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364304965, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304989:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364304968, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364304965, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304968:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364304966, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364304965, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304966:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364304965, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364304965:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1365069326, "codeType": "PMNT", "amt": 322, "curr": "AED", "originalAmt": 322, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T05:38:11", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365069326:*********", "paymentID": *********, "amt": 322, "approveCode": 0}]}, {"chargeID": 1364304991, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364304965, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305081, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 6, "charges": [{"chargeID": 1364305064, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305064:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364304995, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364304993, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304995:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364304996, "codeType": "TAX", "taxID": 12650, "taxCode": "ZF", "taxChargeID": 1364304993, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304996:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364304997, "codeType": "TAX", "taxID": 12652, "taxCode": "BG", "taxChargeID": 1364304993, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Charge (International)", "comment": "Passenger Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304997:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364304994, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364304993, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364304994:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364304993, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364304993:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364304998, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364304993, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305082, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 5, "charges": [{"chargeID": 1364305066, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305066:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305004, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364305001, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305004:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305002, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364305001, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305002:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364305005, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305001, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305005:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305006, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305001, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305006:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305003, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364305001, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305003:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364305001, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305001:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305007, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305001, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305083, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 8, "charges": [{"chargeID": 1364305068, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305068:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305010, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305009, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305010:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305012, "codeType": "TAX", "taxID": 12650, "taxCode": "ZF", "taxChargeID": 1364305009, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305012:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364305011, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305009, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305011:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305013, "codeType": "TAX", "taxID": 12652, "taxCode": "BG", "taxChargeID": 1364305009, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Charge (International)", "comment": "Passenger Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305013:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364305009, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305009:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305014, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305009, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305084, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1364305078, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305078:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305051, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305046, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305051:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305047, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364305046, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305047:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364305049, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364305046, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305049:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305048, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364305046, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305048:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364305050, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305046, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305050:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305046, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305046:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305052, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305046, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305089, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 9, "charges": [{"chargeID": 1364305080, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305080:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"48.6\",\r\n  \"Tax\": \"2.31\",\r\n  \"SegPaxCount\": \"10\"\r\n}", "ChargeBookDate": "2025-05-27T14:03:20"}, {"chargeID": 1364305057, "codeType": "TAX", "taxID": 12650, "taxCode": "ZF", "taxChargeID": 1364305054, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305057:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364305058, "codeType": "TAX", "taxID": 12652, "taxCode": "BG", "taxChargeID": 1364305054, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Passenger Charge (International)", "comment": "Passenger Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305058:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364305055, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364305054, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305055:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364305056, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364305054, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364305056:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364305054, "codeType": "AIR", "amt": 670, "curr": "AED", "originalAmt": 670, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T14:03:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364305054:*********", "paymentID": *********, "amt": 670, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305059, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364305054, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364305090, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T14:03:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}