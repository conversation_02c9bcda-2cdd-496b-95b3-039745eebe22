{"seriesNum": "299", "PNR": "IRUDNM", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82747168", "bookDate": "2025-05-12T08:48:58", "modifyDate": "2025-05-16T13:40:58", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "37c4d4u74z25t464sat5z504j7829575bfe66ce9e3a7", "securityGUID": "37c4d4u74z25t464sat5z504j7829575bfe66ce9e3a7", "lastLoadGUID": "549dd287-f05f-44c5-80dd-706a4e91f9d4", "isAsyncPNR": false, "MasterPNR": "IRUDNM", "segments": [{"segKey": "16087749:16087749:5/15/2025 10:05:00 AM", "LFID": 16087749, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087749", "org": "DXB", "dest": "ZNZ", "depTime": "2025-05-15T10:05:00", "depTimeGMT": "2025-05-15T06:05:00", "arrTime": "2025-05-15T14:30:00", "operCarrier": "FZ", "operFlightNum": "1687", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "persons": [{"recNum": 3, "status": 5}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181499, "depDate": "2025-05-15T10:05:00", "legKey": "16087749:181499:5/15/2025 10:05:00 AM", "customerKey": "96A9719074D92A4D6F2A3C10ADD6BA3B1A23126190028A0004CB46F6337E4A96"}], "active": true, "changeType": "TK"}, {"segKey": "16087777:16087777:5/18/2025 9:20:00 PM", "LFID": 16087777, "depDate": "2025-05-18T00:00:00", "flightGroupId": "16087777", "org": "ZNZ", "dest": "DXB", "depTime": "2025-05-18T21:20:00", "depTimeGMT": "2025-05-18T18:20:00", "arrTime": "2025-05-19T03:45:00", "operCarrier": "FZ", "operFlightNum": "1688", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "persons": [{"recNum": 4, "status": 0}, {"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181515, "depDate": "2025-05-18T21:20:00", "legKey": "16087777:181515:5/18/2025 9:20:00 PM", "customerKey": "09210BCABBD4B69A04E53A1E7364EB4F9868BF1AE00B8C64E642F5C46EC675F7"}], "active": true, "changeType": "TK"}, {"segKey": "16087777:16087777:5/17/2025 9:20:00 PM", "LFID": 16087777, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087777", "org": "ZNZ", "dest": "DXB", "depTime": "2025-05-17T21:20:00", "depTimeGMT": "2025-05-17T18:20:00", "arrTime": "2025-05-18T03:45:00", "operCarrier": "FZ", "operFlightNum": "1688", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "persons": [{"recNum": 5, "status": 5}], "legDetails": [{"PFID": 181515, "depDate": "2025-05-17T21:20:00", "legKey": "16087777:181515:5/17/2025 9:20:00 PM", "customerKey": "57F818E21D85ED28A4BECE8CBEC3DE5C8EDBB0470801D0AB61C3485AB6D56D41"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268674533, "fName": "HOUIDA", "lName": "ALBARIDI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3, 4, 5]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/12/2025 8:48:59 AM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRX8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "ZB3DA-2SWZC-INS", "insuTransID": "ZB3DA-2SWZC-INS/9d88dd34-352b-45c8-bbe7-35ae47dfbadd", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821b54f000777000000099d#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-12T08:48:58"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/12/2025 8:48:59 AM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRX8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "ZB3DA-2SWZC-INS", "insuTransID": "ZB3DA-2SWZC-INS/9d88dd34-352b-45c8-bbe7-35ae47dfbadd", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821b54f000777000000099d#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-12T08:48:58"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ran<PERSON>.al<PERSON>i", "statusReasonID": 0, "markFareClass": "Z", "insuPurchasedate": "5/12/2025 8:48:59 AM", "provider": "<PERSON>", "status": 5, "fareClass": "Z", "operFareClass": "Z", "FBC": "ZR9AE2", "fareBrand": "Business", "cabin": "BUSINESS", "insuTransID": "ZB3DA-2SWZC-INS/9d88dd34-352b-45c8-bbe7-35ae47dfbadd", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6822eaa4000778000000597b#268674533#1#ENT#SFQE#CHANGE", "fareTypeID": 25, "channelID": 1, "bookDate": "2025-05-13T06:51:49"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "ran<PERSON>.al<PERSON>i", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "Z", "insuPurchasedate": "5/12/2025 8:48:59 AM", "provider": "<PERSON>", "status": 0, "fareClass": "Z", "operFareClass": "Z", "FBC": "ZR9AE2", "fareBrand": "Business", "cabin": "BUSINESS", "changeConsent": 0, "insuTransID": "ZB3DA-2SWZC-INS/9d88dd34-352b-45c8-bbe7-35ae47dfbadd", "toRecNum": 5, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6822eaa4000778000000597b#268674533#2#ENT#SFQE#CHANGE", "fareTypeID": 25, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-13T06:51:49"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "mahm<PERSON>.gouda", "statusReasonID": 0, "markFareClass": "Z", "status": 5, "fareClass": "Z", "operFareClass": "Z", "FBC": "ZR9AE2", "fareBrand": "Business", "cabin": "BUSINESS", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682738130007780000004bfa#268674533#2#ENT#VAYANT#CHANGE", "fareTypeID": 15, "channelID": 1, "bookDate": "2025-05-16T13:07:31"}]}], "payments": [{"paymentID": *********, "paxID": 268675535, "method": "VISA", "status": "1", "paidDate": "2025-05-12T08:53:10", "cardNum": "************1704", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1666.23, "baseCurr": "AED", "baseAmt": 1666.23, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "Hwayda Design", "authCode": "940466", "reference": "22987115", "externalReference": "22987115", "tranId": "21354558", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallmotoaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21354558}, {"paymentID": *********, "paxID": 268783312, "method": "IPAY", "status": "1", "paidDate": "2025-05-13T06:53:10", "cardNum": "************7867", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 6125.41, "baseCurr": "AED", "baseAmt": 6125.41, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "350665", "reference": "23004194", "externalReference": "23004194", "tranId": "21374215", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21374215}, {"paymentID": 209268478, "paxID": 269198105, "method": "VISA", "status": "2", "paidDate": "2025-05-16T13:16:13", "cardNum": "************4419", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 113.3, "baseCurr": "AED", "baseAmt": 113.3, "userID": "mahm<PERSON>.gouda", "channelID": 1, "cardHolderName": "<PERSON><PERSON><PERSON>", "reference": "23070224", "externalReference": "23070224", "tranId": "21447764", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21447764}, {"paymentID": *********, "paxID": 269199563, "method": "IPAY", "status": "1", "paidDate": "2025-05-16T13:40:49", "cardNum": "************9313", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 113.3, "baseCurr": "AED", "baseAmt": 113.3, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON> <PERSON>", "authCode": "291887", "reference": "23070524", "externalReference": "23070524", "tranId": "21448410", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21448410}], "OAFlights": null, "physicalFlights": [{"key": "16087749:181499:2025-05-15T10:05:00 AM", "LFID": 16087749, "PFID": 181499, "org": "DXB", "dest": "ZNZ", "depDate": "2025-05-15T10:05:00", "depTime": "2025-05-15T10:05:00", "arrTime": "2025-05-15T14:30:00", "carrier": "FZ", "flightNum": "1687", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": false, "changeType": "TK", "flightChangeTime": "3/13/2025 11:31:04 AM"}, {"key": "16087777:181515:2025-05-17T09:20:00 PM", "LFID": 16087777, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-05-17T21:20:00", "depTime": "2025-05-17T21:20:00", "arrTime": "2025-05-18T03:45:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "CLOSED", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/2/2025 7:00:49 AM"}, {"key": "16087777:181515:2025-05-18T09:20:00 PM", "LFID": 16087777, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-05-18T21:20:00", "depTime": "2025-05-18T21:20:00", "arrTime": "2025-05-19T03:45:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "CLOSED", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/2/2025 7:00:49 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1342235106, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235106:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-12T08:48:58"}, {"chargeID": 1343666874, "codeType": "INSU", "taxChargeID": 1343666872, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235106, "paymentMap": [{"key": "1343666874:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1342235093, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342234927, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235093:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342234928, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342234927, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342234928:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342235089, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342234927, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235089:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1342235091, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342234927, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235091:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342235092, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342234927, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235092:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1343666875, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343666872, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235092, "paymentMap": [{"key": "1343666875:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1343666876, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343666872, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235091, "paymentMap": [{"key": "1343666876:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1343666884, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343666872, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235089, "paymentMap": [{"key": "1343666884:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1343666888, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343666872, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342234928, "paymentMap": [{"key": "1343666888:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343666897, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343666872, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235093, "paymentMap": [{"key": "1343666897:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1342234927, "codeType": "AIR", "amt": 315, "curr": "AED", "originalAmt": 315, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T08:48:59", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342234927:*********", "paymentID": *********, "amt": 315, "approveCode": 0}]}, {"chargeID": 1343666872, "codeType": "AIR", "amt": -315, "curr": "AED", "originalAmt": -315, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T06:51:49", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342234927, "paymentMap": [{"key": "1343666872:*********", "paymentID": *********, "amt": -315, "approveCode": 0}]}, {"chargeID": 1342252375, "codeType": "PMNT", "amt": 48.53, "curr": "AED", "originalAmt": 48.53, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T08:53:15", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342252375:*********", "paymentID": *********, "amt": 48.53, "approveCode": 0}]}, {"chargeID": 1342235107, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181499", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}, {"chargeID": 1343666885, "codeType": "FRST", "taxChargeID": 1343666872, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235107, "paymentMap": [], "PFID": "181499"}, {"chargeID": 1342235094, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342234927, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343666886, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1343666872, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235094, "paymentMap": []}, {"chargeID": 1342235090, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1342234927, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343666873, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1343666872, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235090, "paymentMap": []}, {"chargeID": 1342235110, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}, {"chargeID": 1343666904, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343666872, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235110, "paymentMap": [], "PFID": "181499"}]}, {"recNum": 2, "charges": [{"chargeID": 1342235109, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235109:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-12T08:48:58"}, {"chargeID": 1343666917, "codeType": "INSU", "taxChargeID": 1343666907, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:51", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235109, "paymentMap": [{"key": "1343666917:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1342235098, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1342235096, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Aviation Safety Fee", "comment": "Aviation Safety Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235098:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1342235099, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342235096, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235099:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1342235097, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342235096, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235097:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342235101, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1342235096, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235101:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1342235102, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1342235096, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Airport Security Fee (International)", "comment": "Airport Security Fee (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235102:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1343666908, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343666907, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235097, "paymentMap": [{"key": "1343666908:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343666912, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343666907, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235099, "paymentMap": [{"key": "1343666912:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1343666913, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1343666907, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Airport Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235102, "paymentMap": [{"key": "1343666913:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1343666915, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1343666907, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:51", "desc": "Aviation Safety Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235098, "paymentMap": [{"key": "1343666915:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1343666916, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1343666907, "amt": -150, "curr": "AED", "originalAmt": -150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:51", "desc": "Airport Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235101, "paymentMap": [{"key": "1343666916:*********", "paymentID": *********, "amt": -150, "approveCode": 0}]}, {"chargeID": 1342235096, "codeType": "AIR", "amt": 362, "curr": "AED", "originalAmt": 362, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T08:48:59", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342235096:*********", "paymentID": *********, "amt": 362, "approveCode": 0}]}, {"chargeID": 1343666907, "codeType": "AIR", "amt": -362, "curr": "AED", "originalAmt": -362, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T06:51:50", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235096, "paymentMap": [{"key": "1343666907:*********", "paymentID": *********, "amt": -362, "approveCode": 0}]}, {"chargeID": 1342235103, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342235096, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343666914, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1343666907, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:51", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235103, "paymentMap": []}, {"chargeID": 1342235100, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1342235096, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343666910, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1343666907, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235100, "paymentMap": []}, {"chargeID": 1342235111, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T08:48:59", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181515"}, {"chargeID": 1343666909, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343666907, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T06:51:50", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342235111, "paymentMap": [], "PFID": "181515"}]}, {"recNum": 3, "charges": [{"chargeID": 1343666970, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:46:07", "billDate": "2025-05-13T06:51:51", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666970:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "PFID": "181499", "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-13T06:51:51"}, {"chargeID": 1343666919, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343666918, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666919:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1343666920, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343666918, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666920:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343666921, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343666918, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666921:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343666922, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343666918, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666922:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1343666952, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343666918, "amt": 890, "curr": "AED", "originalAmt": 890, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666952:*********", "paymentID": *********, "amt": 890, "approveCode": 0}]}, {"chargeID": 1343666918, "codeType": "AIR", "amt": 2642, "curr": "AED", "originalAmt": 2642, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "FZ 1687 DXB-ZNZ 15May2025 Thu 10:05 14:30\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666918:*********", "paymentID": *********, "amt": 1617.7, "approveCode": 0}, {"key": "1343666918:*********", "paymentID": *********, "amt": 1024.3, "approveCode": 0}]}, {"chargeID": 1343669298, "codeType": "PMNT", "amt": 178.41, "curr": "AED", "originalAmt": 178.41, "originalCurr": "AED", "status": 1, "billDate": "2025-05-13T06:53:15", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343669298:*********", "paymentID": *********, "amt": 178.41, "approveCode": 0}]}, {"chargeID": 1343666969, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666969:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343666967, "codeType": "JBAG", "taxID": 6544, "taxCode": "JBAG", "taxChargeID": 1343666918, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "JBAG: 40kg Baggage allowance Business", "comment": "JBAG: 40kg Baggage allowance Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343666968, "codeType": "IFPJ", "taxID": 6545, "taxCode": "IFPJ", "taxChargeID": 1343666918, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "IFPJ: In flight entertainment Business", "comment": "IFPJ: In flight entertainment Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346804327, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-15T04:17:00", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499", "ssrCommentId": "*********"}]}, {"recNum": 4, "charges": [{"chargeID": 1343667091, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:46:07", "billDate": "2025-05-13T06:51:52", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343667091:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "PFID": "181515", "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-13T06:51:52"}, {"chargeID": 1343666984, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1343666983, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "HY: Aviation Safety Fee", "comment": "HY: Aviation Safety Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666984:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1343666986, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343666983, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666986:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343666987, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1343666983, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "P9: Airport Security Fee (International)", "comment": "P9: Airport Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666987:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1343666988, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1343666983, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "NN: Airport Service Charge", "comment": "NN: Airport Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666988:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343667006, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343666983, "amt": 890, "curr": "AED", "originalAmt": 890, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:52", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343667006:*********", "paymentID": *********, "amt": 890, "approveCode": 0}]}, {"chargeID": 1349106629, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1349106608, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-16T13:07:31", "desc": "HY: Aviation Safety Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343666984, "paymentMap": [{"key": "1349106629:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1349106630, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349106608, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-16T13:07:31", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343666986, "paymentMap": [{"key": "1349106630:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1349106631, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1349106608, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-16T13:07:32", "desc": "P9: Airport Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343666987, "paymentMap": [{"key": "1349106631:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1349106632, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349106608, "amt": -890, "curr": "AED", "originalAmt": -890, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-16T13:07:32", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343667006, "paymentMap": [{"key": "1349106632:*********", "paymentID": *********, "amt": -890, "approveCode": 0}]}, {"chargeID": 1349106635, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1349106608, "amt": -150, "curr": "AED", "originalAmt": -150, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-16T13:07:32", "desc": "NN: Airport Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343666988, "paymentMap": [{"key": "1349106635:*********", "paymentID": *********, "amt": -150, "approveCode": 0}]}, {"chargeID": 1343666983, "codeType": "AIR", "amt": 2642, "curr": "AED", "originalAmt": 2642, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-13T06:51:51", "desc": "FZ 1688 ZNZ-DXB 18May2025 Sun 21:20 03:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343666983:*********", "paymentID": *********, "amt": 2642, "approveCode": 0}]}, {"chargeID": 1349106608, "codeType": "AIR", "amt": -2642, "curr": "AED", "originalAmt": -2642, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:51", "billDate": "2025-05-16T13:07:31", "desc": "FZ 1688 ZNZ-DXB 18May2025 Sun 21:20 03:45\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343666983, "paymentMap": [{"key": "1349106608:*********", "paymentID": *********, "amt": -2642, "approveCode": 0}]}, {"chargeID": 1343667066, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:52", "billDate": "2025-05-13T06:51:52", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343667066:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343667023, "codeType": "JBAG", "taxID": 6544, "taxCode": "JBAG", "taxChargeID": 1343666983, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:52", "billDate": "2025-05-13T06:51:52", "desc": "JBAG: 40kg Baggage allowance Business", "comment": "JBAG: 40kg Baggage allowance Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349106633, "codeType": "JBAG", "taxID": 6544, "taxCode": "JBAG", "taxChargeID": 1349106608, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:52", "billDate": "2025-05-16T13:07:32", "desc": "JBAG: 40kg Baggage allowance Business", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343667023, "paymentMap": []}, {"chargeID": 1343667048, "codeType": "IFPJ", "taxID": 6545, "taxCode": "IFPJ", "taxChargeID": 1343666983, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:52", "billDate": "2025-05-13T06:51:52", "desc": "IFPJ: In flight entertainment Business", "comment": "IFPJ: In flight entertainment Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349106634, "codeType": "IFPJ", "taxID": 6545, "taxCode": "IFPJ", "taxChargeID": 1349106608, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-13T06:51:52", "billDate": "2025-05-16T13:07:32", "desc": "IFPJ: In flight entertainment Business", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343667048, "paymentMap": []}]}, {"recNum": 5, "charges": [{"chargeID": 1349106637, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1349106636, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "NN: Airport Service Charge", "comment": "NN: Airport Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106637:*********", "paymentID": *********, "amt": 150, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-16T13:07:32"}, {"chargeID": 1349106638, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1349106636, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "HY: Aviation Safety Fee", "comment": "HY: Aviation Safety Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106638:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1349106639, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349106636, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106639:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1349106640, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349106636, "amt": 890, "curr": "AED", "originalAmt": 890, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106640:*********", "paymentID": *********, "amt": 30, "approveCode": 0}, {"key": "1349106640:*********", "paymentID": *********, "amt": 860, "approveCode": 0}]}, {"chargeID": 1349106641, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1349106636, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "P9: Airport Security Fee (International)", "comment": "P9: Airport Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106641:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1349106636, "codeType": "AIR", "amt": 2692, "curr": "AED", "originalAmt": 2692, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "FZ 1688 ZNZ-DXB 17May2025 Sat 21:20 03:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106636:*********", "paymentID": *********, "amt": 2692, "approveCode": 0}]}, {"chargeID": 1349153642, "codeType": "PMNT", "amt": 3.3, "curr": "AED", "originalAmt": 3.3, "originalCurr": "AED", "status": 1, "billDate": "2025-05-16T13:40:58", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349153642:*********", "paymentID": *********, "amt": 3.3, "approveCode": 0}]}, {"chargeID": 1349106644, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349106644:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1349106642, "codeType": "JBAG", "taxID": 6544, "taxCode": "JBAG", "taxChargeID": 1349106636, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "JBAG: 40kg Baggage allowance Business", "comment": "JBAG: 40kg Baggage allowance Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349106643, "codeType": "IFPJ", "taxID": 6545, "taxCode": "IFPJ", "taxChargeID": 1349106636, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-16T13:07:32", "billDate": "2025-05-16T13:07:32", "desc": "IFPJ: In flight entertainment Business", "comment": "IFPJ: In flight entertainment Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}], "parentPNRs": [], "childPNRs": []}