{"seriesNum": "299", "PNR": "TB24P4", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "81399524", "bookDate": "2025-03-22T20:40:08", "modifyDate": "2025-05-18T06:39:07", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "2bcb2a203cybues8u40b3960ybe9i9c0ea7a63fd5fb4", "securityGUID": "2bcb2a203cybues8u40b3960ybe9i9c0ea7a63fd5fb4", "lastLoadGUID": "3562EC703D0B4FE7E0631E206F0A120C", "isAsyncPNR": false, "MasterPNR": "TB24P4", "segments": [{"segKey": "16087456:16087456:5/19/2025 1:40:00 AM", "LFID": 16087456, "depDate": "2025-05-19T00:00:00", "flightGroupId": "16087456", "org": "DXB", "dest": "TBS", "depTime": "2025-05-19T01:40:00", "depTimeGMT": "2025-05-18T21:40:00", "arrTime": "2025-05-19T05:05:00", "operCarrier": "FZ", "operFlightNum": "713", "mrktCarrier": "FZ ", "mrktFlightNum": "713", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181207, "depDate": "2025-05-19T01:40:00", "legKey": "16087456:181207:5/19/2025 1:40:00 AM", "customerKey": "3B9EFD5EBF6F4AEF0B9403B4DF3144ABF3A58200CC894E622E49AB4FE2341305"}], "active": true, "changeType": "TK"}, {"segKey": "16087476:16087476:5/21/2025 4:35:00 PM", "LFID": 16087476, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087476", "org": "TBS", "dest": "DXB", "depTime": "2025-05-21T16:35:00", "depTimeGMT": "2025-05-21T12:35:00", "arrTime": "2025-05-21T19:50:00", "operCarrier": "FZ", "operFlightNum": "712", "mrktCarrier": "FZ ", "mrktFlightNum": "712", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181237, "depDate": "2025-05-21T16:35:00", "legKey": "16087476:181237:5/21/2025 4:35:00 PM", "customerKey": "64B7C6FD95123361A8092F93E4273A110AFF21D858A2FF7EB00BF9743559E4E5"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 263402613, "fName": "FARAH", "lName": "MOUED", "title": "MS", "PTCID": 1, "gender": "F", "FFNum": "715970780", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/18/2025 6:36:08 AM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "emergencyContactID": 269241017, "discloseEmergencyContact": 1, "insuConfNum": "FJDWW-F9M39-INS", "insuTransID": "FJDWW-F9M39-INS/04ac4492-cd28-4a3a-86a0-236cb0d2f3e9", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "67df1ec60007780000031189#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "bookDate": "2025-03-22T20:40:08"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/18/2025 6:36:08 AM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "emergencyContactID": 269493174, "discloseEmergencyContact": 1, "insuConfNum": "FJDWW-F9M39-INS", "insuTransID": "FJDWW-F9M39-INS/04ac4492-cd28-4a3a-86a0-236cb0d2f3e9", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "67df1ec60007780000031189#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "bookDate": "2025-03-22T20:40:08"}]}], "payments": [{"paymentID": *********, "paxID": 269329188, "method": "IPAY", "status": "1", "paidDate": "2025-05-18T06:37:02", "cardNum": "************3094", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 36.77, "baseCurr": "AED", "baseAmt": 36.77, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "149089", "reference": "23100837", "externalReference": "23100837", "tranId": "21474736", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21474736}, {"paymentID": *********, "paxID": 263402621, "method": "VISA", "status": "1", "paidDate": "2025-03-22T20:40:27", "cardNum": "************5171", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1686.11, "baseCurr": "AED", "baseAmt": 1686.11, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "236668", "reference": "21936064", "externalReference": "21936064", "tranId": "20330223", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 20330223}], "OAFlights": null, "physicalFlights": [{"key": "16087456:181207:2025-05-19T01:40:00 AM", "LFID": 16087456, "PFID": 181207, "org": "DXB", "dest": "TBS", "depDate": "2025-05-19T01:40:00", "depTime": "2025-05-19T01:40:00", "arrTime": "2025-05-19T05:05:00", "carrier": "FZ", "flightNum": "713", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "713", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:49 PM"}, {"key": "16087476:181237:2025-05-21T04:35:00 PM", "LFID": 16087476, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-05-21T16:35:00", "depTime": "2025-05-21T16:35:00", "arrTime": "2025-05-21T19:50:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/16/2025 12:53:38 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1350870508, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-18T06:36:00", "billDate": "2025-05-18T06:36:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350870508:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-18T06:36:06"}, {"chargeID": 1274727724, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1274727699, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727724:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1274727725, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1274727699, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727725:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1274727703, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1274727699, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727703:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1274727701, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1274727699, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727701:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1274727702, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1274727699, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727702:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1274727699, "codeType": "AIR", "amt": 455, "curr": "AED", "originalAmt": 455, "originalCurr": "AED", "status": 1, "billDate": "2025-03-22T20:40:08", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1274727699:*********", "paymentID": *********, "amt": 455, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1350871574, "codeType": "PMNT", "amt": 1.07, "curr": "AED", "originalAmt": 1.07, "originalCurr": "AED", "status": 1, "billDate": "2025-05-18T06:37:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350871574:*********", "paymentID": *********, "amt": 1.07, "approveCode": 0}]}, {"chargeID": 1274728662, "codeType": "PMNT", "amt": 49.11, "curr": "AED", "originalAmt": 49.11, "originalCurr": "AED", "status": 1, "billDate": "2025-03-22T20:40:32", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274728662:*********", "paymentID": *********, "amt": 49.11, "approveCode": 0}]}, {"chargeID": 1350869696, "codeType": "NSST", "amt": 32, "curr": "AED", "originalAmt": 32, "originalCurr": "AED", "status": 1, "billDate": "2025-05-18T06:34:41", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1350869696:*********", "paymentID": *********, "amt": 32, "approveCode": 0}], "PFID": "181207", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1350869695, "codeType": "NSST", "amt": -32, "curr": "AED", "originalAmt": -32, "originalCurr": "AED", "status": 0, "billDate": "2025-05-18T06:34:41", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1274727735, "paymentMap": [{"key": "1350869695:*********", "paymentID": *********, "amt": -32, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1274727735, "codeType": "NSST", "amt": 32, "curr": "AED", "originalAmt": 32, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "NSST", "comment": "FLXID:NSST_ZONE3_WIN_AIS::181207", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727735:*********", "paymentID": *********, "amt": 32, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1274727700, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1274727699, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1274727738, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181207", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1350870569, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-18T06:36:00", "billDate": "2025-05-18T06:36:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350870569:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-18T06:36:06"}, {"chargeID": 1274727730, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1274727727, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727730:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1274727732, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1274727727, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727732:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1274727729, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1274727727, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727729:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1274727731, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1274727727, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1274727731:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1274727727, "codeType": "AIR", "amt": 455, "curr": "AED", "originalAmt": 455, "originalCurr": "AED", "status": 1, "billDate": "2025-03-22T20:40:08", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1274727727:*********", "paymentID": *********, "amt": 455, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1274727737, "codeType": "SPST", "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181237", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1274727737:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181237", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1274727728, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1274727727, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1274727739, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-03-22T20:40:08", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181237", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}