{"seriesNum": "299", "PNR": "A9G61N", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "83078153", "bookDate": "2025-05-23T14:52:56", "modifyDate": "2025-05-29T16:50:30", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "f881c5ic9bdd60u2e8bf64y9nf4288987f97cc83564c", "securityGUID": "f881c5ic9bdd60u2e8bf64y9nf4288987f97cc83564c", "lastLoadGUID": "845dbadc-a25f-428a-bf97-4c84f8ff3a81", "isAsyncPNR": false, "MasterPNR": "A9G61N", "segments": [{"segKey": "16087829:16087829:5/24/2025 10:20:00 AM", "LFID": 16087829, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16087829", "org": "DXB", "dest": "OTP", "depTime": "2025-05-24T10:20:00", "depTimeGMT": "2025-05-24T06:20:00", "arrTime": "2025-05-24T14:45:00", "operCarrier": "FZ", "operFlightNum": "1797", "mrktCarrier": "FZ", "mrktFlightNum": "1797", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181559, "depDate": "2025-05-24T10:20:00", "legKey": "16087829:181559:5/24/2025 10:20:00 AM", "customerKey": "526567993EADF8D224AA0A1DBF8E4407AECBC2944C063D095BA3DFF3DB9FB9F7"}], "active": true}, {"segKey": "16087845:16087845:8/18/2025 3:40:00 PM", "LFID": 16087845, "depDate": "2025-08-18T00:00:00", "flightGroupId": "16087845", "org": "OTP", "dest": "DXB", "depTime": "2025-08-18T15:40:00", "depTimeGMT": "2025-08-18T12:40:00", "arrTime": "2025-08-18T21:35:00", "operCarrier": "FZ", "operFlightNum": "1798", "mrktCarrier": "FZ ", "mrktFlightNum": "1798", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181575, "depDate": "2025-08-18T15:40:00", "legKey": "16087845:181575:8/18/2025 3:40:00 PM", "customerKey": "B6150C688AFEB7D68F4A77698000770CF71329F960CF2E6505B9A58906FAC455"}], "active": true, "changeType": "AC"}, {"segKey": "16087944:16087944:5/23/2025 8:55:00 PM", "LFID": 16087944, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087944", "org": "DXB", "dest": "OTP", "depTime": "2025-05-23T20:55:00", "depTimeGMT": "2025-05-23T16:55:00", "arrTime": "2025-05-24T01:20:00", "operCarrier": "FZ", "operFlightNum": "1795", "mrktCarrier": "FZ ", "mrktFlightNum": "1795", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181694, "depDate": "2025-05-23T20:55:00", "legKey": "16087944:181694:5/23/2025 8:55:00 PM", "customerKey": "CAF5011F542C1CF22CF8D5748EC65399431AA264BB26E1013EC80167D2B57A44"}], "active": true, "changeType": "TK"}, {"segKey": "16087845:16087845:5/30/2025 3:40:00 PM", "LFID": 16087845, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087845", "org": "OTP", "dest": "DXB", "depTime": "2025-05-30T15:40:00", "depTimeGMT": "2025-05-30T12:40:00", "arrTime": "2025-05-30T21:35:00", "operCarrier": "FZ", "operFlightNum": "1798", "mrktCarrier": "FZ ", "mrktFlightNum": "1798", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181575, "depDate": "2025-05-30T15:40:00", "legKey": "16087845:181575:5/30/2025 3:40:00 PM", "customerKey": "3B43A5CC8BA1F3F0F08FE17F3A4E2CEEC422164C7EEB8E50F8398E4CBB3435C2"}], "active": true}], "persons": [{"paxID": 269943688, "fName": "MOHAMED", "lName": "ALMUHAIRI", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1990-07-12T00:00:00", "FFNum": "477582976", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3, 4], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/23/2025 2:52:57 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "7LFQ7-LT3Q9-INS", "insuTransID": "7LFQ7-LT3Q9-INS/0f5b9863-7bf9-4c37-b6cf-1bc0463dd452", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68308b0d00077700000139e2#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-23T14:52:56"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/23/2025 2:52:57 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "7LFQ7-LT3Q9-INS", "insuTransID": "7LFQ7-LT3Q9-INS/0f5b9863-7bf9-4c37-b6cf-1bc0463dd452", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6830aef60007770000014362#269943688#2#ENT#VAYANT#CHANGE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-23T14:52:56"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "<PERSON><PERSON><PERSON>a<PERSON><PERSON>", "statusReasonID": 0, "markFareClass": "W", "insuPurchasedate": "5/23/2025 2:52:57 PM", "provider": "<PERSON>", "status": 5, "fareClass": "W", "operFareClass": "W", "FBC": "WRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "7LFQ7-LT3Q9-INS/0f5b9863-7bf9-4c37-b6cf-1bc0463dd452", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6830aef60007770000014362#269943688#1#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-23T17:28:02"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/23/2025 2:52:57 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "7LFQ7-LT3Q9-INS", "insuTransID": "7LFQ7-LT3Q9-INS/0f5b9863-7bf9-4c37-b6cf-1bc0463dd452", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6837150f00077800000043f8#269943688#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-28T13:54:21"}]}], "payments": [{"paymentID": 210589536, "paxID": 270459385, "method": "IPAY", "status": "2", "paidDate": "2025-05-28T13:54:28", "cardNum": "************5985", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 43.96, "baseCurr": "AED", "baseAmt": 180.25, "userID": "smartrez", "channelID": 5, "cardHolderName": "<PERSON>", "reference": "23302280", "externalReference": "23302280", "tranId": "21684907", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "MCCMPGSPEEUR", "exchangeRate": "0.24386444", "resExternalPaymentID": 21684907}, {"paymentID": 210601984, "paxID": 270471343, "method": "IPAY", "status": "2", "paidDate": "2025-05-28T15:35:22", "cardNum": "************6474", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 180.25, "baseCurr": "AED", "baseAmt": 180.25, "userID": "smartrez", "channelID": 5, "cardHolderName": "<PERSON>", "reference": "23304663", "externalReference": "23304663", "tranId": "21687014", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21687014}, {"paymentID": *********, "paxID": 270598318, "method": "IPAY", "status": "1", "paidDate": "2025-05-29T16:50:27", "cardNum": "************6474", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 180.25, "baseCurr": "AED", "baseAmt": 180.25, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "942753", "reference": "23327402", "externalReference": "23327402", "tranId": "21712023", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21712023}, {"paymentID": 210067517, "paxID": 269957997, "method": "IPAY", "status": "2", "paidDate": "2025-05-23T17:31:35", "cardNum": "************6474", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1946.24, "baseCurr": "AED", "baseAmt": 1946.24, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "reference": "23209677", "externalReference": "23209677", "tranId": "21591316", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21591316}, {"paymentID": *********, "paxID": 269943706, "method": "IPAY", "status": "1", "paidDate": "2025-05-23T14:53:01", "cardNum": "************5985", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2723.47, "baseCurr": "AED", "baseAmt": 2723.47, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "518733", "reference": "23207838", "externalReference": "23207838", "tranId": "21588874", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21588874}, {"paymentID": 210067494, "paxID": 269957991, "method": "IPAY", "status": "2", "paidDate": "2025-05-23T17:31:03", "cardNum": "************6474", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1946.24, "baseCurr": "AED", "baseAmt": 1946.24, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "reference": "23209669", "externalReference": "23209669", "tranId": "21591316", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21591316}, {"paymentID": 210067520, "paxID": 269957998, "method": "IPAY", "status": "2", "paidDate": "2025-05-23T17:32:01", "cardNum": "************6474", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1946.24, "baseCurr": "AED", "baseAmt": 1946.24, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "reference": "23209688", "externalReference": "23209688", "tranId": "21591316", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21591316}, {"paymentID": *********, "paxID": 269958018, "method": "IPAY", "status": "1", "paidDate": "2025-05-23T17:42:01", "cardNum": "************6474", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1946.24, "baseCurr": "AED", "baseAmt": 1946.24, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "167533", "reference": "23209767", "externalReference": "23209767", "tranId": "21591316", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21591316}], "OAFlights": null, "physicalFlights": [{"key": "16087944:181694:2025-05-23T08:55:00 PM", "LFID": 16087944, "PFID": 181694, "org": "DXB", "dest": "OTP", "depDate": "2025-05-23T20:55:00", "depTime": "2025-05-23T20:55:00", "arrTime": "2025-05-24T01:20:00", "carrier": "FZ", "flightNum": "1795", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "1795", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "OTP", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bucharest Henri <PERSON>", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:14:25 AM"}, {"key": "16087829:181559:2025-05-24T10:20:00 AM", "LFID": 16087829, "PFID": 181559, "org": "DXB", "dest": "OTP", "depDate": "2025-05-24T10:20:00", "depTime": "2025-05-24T10:20:00", "arrTime": "2025-05-24T14:45:00", "carrier": "FZ", "flightNum": "1797", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1797", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "OTP", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bucharest Henri <PERSON>", "isActive": false}, {"key": "16087845:181575:2025-05-30T03:40:00 PM", "LFID": 16087845, "PFID": 181575, "org": "OTP", "dest": "DXB", "depDate": "2025-05-30T15:40:00", "depTime": "2025-05-30T15:40:00", "arrTime": "2025-05-30T21:35:00", "carrier": "FZ", "flightNum": "1798", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1798", "flightStatus": "CLOSED", "originMetroGroup": "OTP", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 17700, "reaccomChangeAlert": false, "originName": "Bucharest Henri <PERSON>", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087845:181575:2025-08-18T03:40:00 PM", "LFID": 16087845, "PFID": 181575, "org": "OTP", "dest": "DXB", "depDate": "2025-08-18T15:40:00", "depTime": "2025-08-18T15:40:00", "arrTime": "2025-08-18T21:35:00", "carrier": "FZ", "flightNum": "1798", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "1798", "flightStatus": "OPEN", "originMetroGroup": "OTP", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 17700, "reaccomChangeAlert": false, "originName": "Bucharest Henri <PERSON>", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/26/2025 6:04:36 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1359062967, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062967:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-23T14:52:56"}, {"chargeID": 1359235398, "codeType": "INSU", "taxChargeID": 1359235394, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "INSU", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062967, "paymentMap": [{"key": "1359235398:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1359062954, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1359062951, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062954:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1359062955, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1359062951, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062955:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1359062957, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1359062951, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062957:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1359062953, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1359062951, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062953:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1359062956, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1359062951, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062956:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1359235395, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1359235394, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "YQ - DUMMY", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062954, "paymentMap": [{"key": "1359235395:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1359235396, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1359235394, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "Passenger Service Charge (Intl)", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062955, "paymentMap": [{"key": "1359235396:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1359235400, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1359235394, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "Passenger Facilities Charge.", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062956, "paymentMap": [{"key": "1359235400:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1359235401, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1359235394, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "Advanced passenger information fee", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062953, "paymentMap": [{"key": "1359235401:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1359235402, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1359235394, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "Passengers Security & Safety Service Fees", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062957, "paymentMap": [{"key": "1359235402:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1359062951, "codeType": "AIR", "amt": 892, "curr": "AED", "originalAmt": 892, "originalCurr": "AED", "status": 0, "billDate": "2025-05-23T14:52:56", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062951:*********", "paymentID": *********, "amt": 892, "approveCode": 0}]}, {"chargeID": 1359235394, "codeType": "AIR", "amt": -892, "curr": "AED", "originalAmt": -892, "originalCurr": "AED", "status": 0, "billDate": "2025-05-23T17:28:02", "desc": "WEB:AIR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062951, "paymentMap": [{"key": "1359235394:*********", "paymentID": *********, "amt": -892, "approveCode": 0}]}, {"chargeID": 1359064576, "codeType": "PMNT", "amt": 79.32, "curr": "AED", "originalAmt": 79.32, "originalCurr": "AED", "status": 0, "billDate": "2025-05-23T14:53:05", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359064576:*********", "paymentID": *********, "amt": 79.32, "approveCode": 0}]}, {"chargeID": 1359062968, "codeType": "XLGR", "amt": 156, "curr": "AED", "originalAmt": 156, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "XLGR", "comment": "FLXID:XLGR_EMER_ZONE3_MID::181694", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062968:*********", "paymentID": *********, "amt": 156, "approveCode": 0}], "PFID": "181694"}, {"chargeID": 1359235403, "codeType": "PNLT", "amt": 1172, "curr": "AED", "originalAmt": 1172, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:02", "billDate": "2025-05-23T17:28:02", "desc": "CancelNoRefund FZ 1795 DXB - OTP 23.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235403:*********", "paymentID": *********, "amt": 1172, "approveCode": 0}]}, {"chargeID": 1359062952, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1359062951, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1359235397, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1359235394, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "20kg BAG INCLUDED IN FARE", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062952, "paymentMap": []}, {"chargeID": 1359062992, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181694"}, {"chargeID": 1359235399, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1359235394, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "Standard meal", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062992, "paymentMap": [], "PFID": "181694"}]}, {"recNum": 2, "charges": [{"chargeID": 1359062990, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062990:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-23T14:52:56"}, {"chargeID": 1359235404, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-23T17:24:12", "billDate": "2025-05-23T17:28:02", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235404:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1359235405, "codeType": "INSU", "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T17:28:02", "desc": "INSU", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062990, "paymentMap": [{"key": "1359235405:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1359062962, "codeType": "TAX", "taxID": 12770, "taxCode": "RO", "taxChargeID": 1359062959, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Airport Departure Tax (International)", "comment": "Airport Departure Tax (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062962:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1359062964, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1359062959, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062964:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1359062961, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1359062959, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062961:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1359062963, "codeType": "TAX", "taxID": 12772, "taxCode": "DC", "taxChargeID": 1359062959, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062963:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1365964903, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1359062959, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062964, "paymentMap": [{"key": "1365964903:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1365964905, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1359062959, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062961, "paymentMap": [{"key": "1365964905:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1365964900, "codeType": "TAX", "taxID": 12770, "taxCode": "RO", "taxChargeID": 1359062959, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Airport Departure Tax (International)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062962, "paymentMap": [{"key": "1365964900:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1365964909, "codeType": "TAX", "taxID": 12772, "taxCode": "DC", "taxChargeID": 1359062959, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Security Charge (International)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062963, "paymentMap": [{"key": "1365964909:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1359062959, "codeType": "AIR", "amt": 590, "curr": "AED", "originalAmt": 590, "originalCurr": "AED", "status": 0, "billDate": "2025-05-23T14:52:56", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062959:*********", "paymentID": *********, "amt": 590, "approveCode": 0}]}, {"chargeID": 1365964906, "codeType": "AIR", "amt": -590, "curr": "AED", "originalAmt": -590, "originalCurr": "AED", "status": 0, "billDate": "2025-05-28T13:54:21", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062959, "paymentMap": [{"key": "1365964906:*********", "paymentID": *********, "amt": -590, "approveCode": 0}]}, {"chargeID": 1359062991, "codeType": "XLGR", "amt": 166, "curr": "AED", "originalAmt": 166, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_MID::181575", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359062991:*********", "paymentID": *********, "amt": 166, "approveCode": 0}], "PFID": "181575"}, {"chargeID": 1365964897, "codeType": "XLGR", "amt": -166, "curr": "AED", "originalAmt": -166, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1359062991, "paymentMap": [{"key": "1365964897:*********", "paymentID": *********, "amt": -166, "approveCode": 0}], "PFID": "181575"}, {"chargeID": 1365964910, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T13:54:21", "desc": "Penalty AddedDueToModify FZ  1798 OTP  - DXB  30-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365964910:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1359062960, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1359062959, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1359062993, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:52:56", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181575"}]}, {"recNum": 3, "charges": [{"chargeID": 1359235416, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:24:12", "billDate": "2025-05-23T17:28:03", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235416:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-23T17:28:03"}, {"chargeID": 1359235407, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1359235406, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235407:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1359235408, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1359235406, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235408:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1359235409, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1359235406, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235409:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1359235410, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1359235406, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235410:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1359235411, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1359235406, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235411:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1359235406, "codeType": "AIR", "amt": 1382, "curr": "AED", "originalAmt": 1382, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:02", "billDate": "2025-05-23T17:28:03", "desc": "FZ 1797 DXB-OTP 24May2025 Sat 10:20 14:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1359235406:*********", "paymentID": *********, "amt": 1206.85, "approveCode": 0}, {"key": "1359235406:*********", "paymentID": *********, "amt": 175.15, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1359248224, "codeType": "PMNT", "amt": 56.69, "curr": "AED", "originalAmt": 56.69, "originalCurr": "AED", "status": 1, "billDate": "2025-05-23T17:42:20", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359248224:*********", "paymentID": *********, "amt": 56.69, "approveCode": 0}]}, {"chargeID": 1359235414, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359235414:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1359235415, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:24:09", "billDate": "2025-05-23T17:28:03", "desc": "Special Service Request:XLGR-16D", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1359235415:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181559", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1359235412, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1359235406, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1359235413, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1359235406, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T17:28:03", "billDate": "2025-05-23T17:28:03", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181559", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1359532221, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-24T04:53:16", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181559", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}]}, {"recNum": 4, "charges": [{"chargeID": 1365964951, "codeType": "TAX", "taxID": 12770, "taxCode": "RO", "taxChargeID": 1365964948, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Airport Departure Tax (International)", "comment": "Airport Departure Tax (International)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365964951:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-28T13:54:21"}, {"chargeID": 1365964950, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1365964948, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365964950:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1365964952, "codeType": "TAX", "taxID": 12772, "taxCode": "DC", "taxChargeID": 1365964948, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365964952:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1365964949, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1365964948, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365964949:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1365964948, "codeType": "AIR", "amt": 615, "curr": "AED", "originalAmt": 615, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T13:54:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1365964948:*********", "paymentID": *********, "amt": 606, "approveCode": 0}, {"key": "1365964948:*********", "paymentID": *********, "amt": 9, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1367918619, "codeType": "PMNT", "amt": 5.25, "curr": "AED", "originalAmt": 5.25, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T16:50:30", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367918619:*********", "paymentID": *********, "amt": 5.25, "approveCode": 0}]}, {"chargeID": 1365965337, "codeType": "XLGR", "amt": 166, "curr": "AED", "originalAmt": 166, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-28T13:54:21", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_MID::181575", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1365965337:*********", "paymentID": *********, "amt": 166, "approveCode": 0}], "PFID": "181575", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1365964953, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1365964948, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1365964971, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-28T13:54:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181575", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}