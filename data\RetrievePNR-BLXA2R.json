{"seriesNum": "299", "PNR": "BLXA2R", "bookAgent": "ANDROID_APP", "resCurrency": "AED", "PNRPin": "83044751", "bookDate": "2025-05-22T11:04:58", "modifyDate": "2025-05-28T16:11:02", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "21edeca21cybb4i0l44b02bbt8yde9u3b68494be078c", "securityGUID": "21edeca21cybb4i0l44b02bbt8yde9u3b68494be078c", "lastLoadGUID": "363468F1808E7494E0630A57380A5246", "isAsyncPNR": false, "MasterPNR": "BLXA2R", "segments": [{"segKey": "17127781:17127781:5/29/2025 8:55:00 PM", "LFID": 17127781, "depDate": "2025-05-29T00:00:00", "flightGroupId": "17127781", "org": "DXB", "dest": "PEW", "depTime": "2025-05-29T20:55:00", "depTimeGMT": "2025-05-29T16:55:00", "arrTime": "2025-05-30T01:05:00", "operCarrier": "FZ", "operFlightNum": "375", "mrktCarrier": "FZ", "mrktFlightNum": "375", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 188022, "depDate": "2025-05-29T20:55:00", "legKey": "17127781:188022:5/29/2025 8:55:00 PM", "customerKey": "727EAA8AACB23A76AB9EB7C14D0FE23FDD0BDD26DAEB1FA18184ECE7EB79B162"}], "active": true, "changeType": "AC"}, {"segKey": "17127781:17127781:5/28/2025 8:55:00 PM", "LFID": 17127781, "depDate": "2025-05-28T00:00:00", "flightGroupId": "17127781", "org": "DXB", "dest": "PEW", "depTime": "2025-05-28T20:55:00", "depTimeGMT": "2025-05-28T16:55:00", "arrTime": "2025-05-29T01:05:00", "operCarrier": "FZ", "operFlightNum": "375", "mrktCarrier": "FZ ", "mrktFlightNum": "375", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 188022, "depDate": "2025-05-28T20:55:00", "legKey": "17127781:188022:5/28/2025 8:55:00 PM", "customerKey": "90715F28F260C57E955FE9AD19D56BA39267334E2165B9CFE6C4CDFA16DA1B5E"}], "active": true, "changeType": "AC"}, {"segKey": "17127584:17127584:6/10/2025 2:05:00 AM", "LFID": 17127584, "depDate": "2025-06-10T00:00:00", "flightGroupId": "17127584", "org": "PEW", "dest": "DXB", "depTime": "2025-06-10T02:05:00", "depTimeGMT": "2025-06-09T21:05:00", "arrTime": "2025-06-10T04:15:00", "operCarrier": "FZ", "operFlightNum": "376", "mrktCarrier": "FZ ", "mrktFlightNum": "376", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 188040, "depDate": "2025-06-10T02:05:00", "legKey": "17127584:188040:6/10/2025 2:05:00 AM", "customerKey": "8DC4BB008A438D161DA57995BA24FDC8BBE21125B2ED7893D89A3FD24083AAD1"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 269812762, "fName": "KHAN", "lName": "ZUBAIR", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/22/2025 11:04:58 AM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "XUDMG-74R2H-INS/967a14d6-f483-46e1-8e8b-2ad05fe10c02", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682f02d4000778000000e298#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-22T11:04:58"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/28/2025 4:07:24 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "VFUL9-DBUPX-INS", "insuTransID": "VFUL9-DBUPX-INS/adef207b-0b89-4a94-a391-a49e371a1942", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683734810007780000005a31#269812762#2#ENT#SFQE#CHANGE", "fareTypeID": 21, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-22T11:04:58"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "mohammed.aldand", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/28/2025 4:07:24 PM", "provider": "<PERSON>", "status": 5, "fareClass": "N", "operFareClass": "N", "FBC": "NRB6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "insuConfNum": "VFUL9-DBUPX-INS", "insuTransID": "VFUL9-DBUPX-INS/adef207b-0b89-4a94-a391-a49e371a1942", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683734810007780000005a31#269812762#1#ENT#SFQE#CHANGE", "fareTypeID": 21, "channelID": 1, "bookDate": "2025-05-28T16:07:18"}]}], "payments": [{"paymentID": *********, "paxID": 269812861, "method": "VISA", "status": "1", "paidDate": "2025-05-22T11:05:32", "cardNum": "************7832", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1740.85, "baseCurr": "AED", "baseAmt": 1740.85, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "ZUBAIR KHAN", "authCode": "551832", "reference": "23182834", "externalReference": "23182834", "tranId": "21564414", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21564414}, {"paymentID": *********, "paxID": 269812762, "method": "APOS", "status": "1", "paidDate": "2025-05-28T16:11:02", "paidCurr": "AED", "paidAmt": 966, "baseCurr": "AED", "baseAmt": 966, "userID": "cashier.nib4", "channelID": 1, "paymentComment": "21101", "authCode": "536062", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "17127781:188022:2025-05-28T08:55:00 PM", "LFID": 17127781, "PFID": 188022, "org": "DXB", "dest": "PEW", "depDate": "2025-05-28T20:55:00", "depTime": "2025-05-28T20:55:00", "arrTime": "2025-05-29T01:05:00", "carrier": "FZ", "flightNum": "375", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "375", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "PEW", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bacha Khan International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "5/13/2025 7:36:27 AM"}, {"key": "17127781:188022:2025-05-29T08:55:00 PM", "LFID": 17127781, "PFID": 188022, "org": "DXB", "dest": "PEW", "depDate": "2025-05-29T20:55:00", "depTime": "2025-05-29T20:55:00", "arrTime": "2025-05-30T01:05:00", "carrier": "FZ", "flightNum": "375", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73H", "mrktCarrier": "FZ", "mrktFlightNum": "375", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "PEW", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bacha Khan International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "5/14/2025 10:05:09 AM"}, {"key": "17127584:188040:2025-06-10T02:05:00 AM", "LFID": 17127584, "PFID": 188040, "org": "PEW", "dest": "DXB", "depDate": "2025-06-10T02:05:00", "depTime": "2025-06-10T02:05:00", "arrTime": "2025-06-10T04:15:00", "carrier": "FZ", "flightNum": "376", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "376", "flightStatus": "OPEN", "originMetroGroup": "PEW", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Bacha Khan International Airport", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/15/2025 6:41:58 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1357315350, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315350:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-22T11:04:58"}, {"chargeID": 1366188060, "codeType": "INSU", "taxChargeID": 1366188057, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "INSU", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315350, "paymentMap": [{"key": "1366188060:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1357315321, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1357315316, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315321:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1357315322, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1357315316, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315322:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357315318, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1357315316, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315318:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357315320, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1357315316, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315320:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1357315319, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1357315316, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315319:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1366188059, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366188057, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "YQ - DUMMY", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315319, "paymentMap": [{"key": "1366188059:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1366188061, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1366188057, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "Passenger Service Charge (Intl)", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315320, "paymentMap": [{"key": "1366188061:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1366188062, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366188057, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "Advanced passenger information fee", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315318, "paymentMap": [{"key": "1366188062:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1366188063, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1366188057, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "Passengers Security & Safety Service Fees", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315322, "paymentMap": [{"key": "1366188063:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1366188064, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366188057, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "Passenger Facilities Charge.", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315321, "paymentMap": [{"key": "1366188064:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1357315316, "codeType": "AIR", "amt": 535, "curr": "AED", "originalAmt": 535, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T11:04:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315316:*********", "paymentID": *********, "amt": 535, "approveCode": 0}]}, {"chargeID": 1366188057, "codeType": "AIR", "amt": -535, "curr": "AED", "originalAmt": -535, "originalCurr": "AED", "status": 0, "billDate": "2025-05-28T16:07:18", "desc": "WEB:AIR", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315316, "paymentMap": [{"key": "1366188057:*********", "paymentID": *********, "amt": -535, "approveCode": 0}]}, {"chargeID": 1357325261, "codeType": "PMNT", "amt": 50.7, "curr": "AED", "originalAmt": 50.7, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T11:05:36", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357325261:*********", "paymentID": *********, "amt": 50.7, "approveCode": 0}]}, {"chargeID": 1366188065, "codeType": "PNLT", "amt": 635, "curr": "AED", "originalAmt": 635, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "CancelNoRefund FZ 375 DXB - PEW 28.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188065:*********", "paymentID": *********, "amt": 635, "approveCode": 0}]}, {"chargeID": 1357315317, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1357315316, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1366188058, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366188057, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "20kg BAG INCLUDED IN FARE", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315317, "paymentMap": []}]}, {"recNum": 2, "charges": [{"chargeID": 1357315351, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315351:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-22T11:04:58"}, {"chargeID": 1366188066, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:06:28", "billDate": "2025-05-28T16:07:19", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188066:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1366188067, "codeType": "INSU", "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T16:07:19", "desc": "INSU", "comment": "TAX ADJUST No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357315351, "paymentMap": [{"key": "1366188067:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1357315325, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1357315323, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315325:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357315349, "codeType": "TAX", "taxID": 11066, "taxCode": "SP", "taxChargeID": 1357315323, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Embarkation fee", "comment": "Embarkation fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315349:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1357315327, "codeType": "TAX", "taxID": 7364, "taxCode": "RG", "taxChargeID": 1357315323, "amt": 170, "curr": "AED", "originalAmt": 170, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Federal Excise Duty", "comment": "Federal Excise Duty", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315327:*********", "paymentID": *********, "amt": 170, "approveCode": 0}]}, {"chargeID": 1357315328, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1357315323, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315328:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1357315326, "codeType": "TAX", "taxID": 11107, "taxCode": "YD", "taxChargeID": 1357315323, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "Infrastructure Development Charges", "comment": "Infrastructure Development Charges", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315326:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1357315323, "codeType": "AIR", "amt": 535, "curr": "AED", "originalAmt": 535, "originalCurr": "AED", "status": 1, "billDate": "2025-05-22T11:04:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357315323:*********", "paymentID": *********, "amt": 535, "approveCode": 0}]}, {"chargeID": 1357315324, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1357315323, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-22T11:04:58", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 3, "charges": [{"chargeID": 1366188075, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:06:28", "billDate": "2025-05-28T16:07:19", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188075:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-28T16:07:19"}, {"chargeID": 1366188069, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366188068, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188069:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1366188070, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1366188068, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188070:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1366188071, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366188068, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188071:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1366188072, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1366188068, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188072:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1366188073, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366188068, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188073:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1366188076, "codeType": "AFEE", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:09", "billDate": "2025-05-28T16:07:19", "desc": "Special Service Request", "comment": "AIRPORT ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188076:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1366188068, "codeType": "AIR", "amt": 786, "curr": "AED", "originalAmt": 786, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "FZ 375 DXB-PEW 29May2025 Thu 20:55 01:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366188068:*********", "paymentID": *********, "amt": 610.85, "approveCode": 0}, {"key": "1366188068:*********", "paymentID": *********, "amt": 175.15, "approveCode": 0}]}, {"chargeID": 1366188074, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366188068, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T16:07:19", "billDate": "2025-05-28T16:07:19", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}], "parentPNRs": [], "childPNRs": []}