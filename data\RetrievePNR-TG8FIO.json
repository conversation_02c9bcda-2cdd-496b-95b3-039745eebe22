{"seriesNum": "299", "PNR": "TG8FIO", "bookAgent": "WEB_MOBILE", "resCurrency": "AED", "PNRPin": "82914530", "bookDate": "2025-05-18T02:11:40", "modifyDate": "2025-05-23T11:09:31", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "a7ct01ga8670y0u4oc0564hfhe43838768e432981858", "securityGUID": "a7ct01ga8670y0u4oc0564hfhe43838768e432981858", "lastLoadGUID": "945e520f-569b-4339-8bc0-fb418c3b95fa", "isAsyncPNR": false, "MasterPNR": "TG8FIO", "segments": [{"segKey": "16087372:16087372:5/20/2025 8:45:00 PM", "LFID": 16087372, "depDate": "2025-05-20T00:00:00", "flightGroupId": "16087372", "org": "DXB", "dest": "HYD", "depTime": "2025-05-20T20:45:00", "depTimeGMT": "2025-05-20T16:45:00", "arrTime": "2025-05-21T01:55:00", "operCarrier": "FZ", "operFlightNum": "435", "mrktCarrier": "FZ ", "mrktFlightNum": "435", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181133, "depDate": "2025-05-20T20:45:00", "legKey": "16087372:181133:5/20/2025 8:45:00 PM", "customerKey": "4CD212965F9F3527B3B4D9BA2921A56A87C36BBF5DDB53D33B03AF8412DBF855"}], "active": true, "changeType": "TK"}, {"segKey": "16087372:16087372:5/27/2025 8:45:00 PM", "LFID": 16087372, "depDate": "2025-05-27T00:00:00", "flightGroupId": "16087372", "org": "DXB", "dest": "HYD", "depTime": "2025-05-27T20:45:00", "depTimeGMT": "2025-05-27T16:45:00", "arrTime": "2025-05-28T01:55:00", "operCarrier": "FZ", "operFlightNum": "435", "mrktCarrier": "FZ", "mrktFlightNum": "435", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181133, "depDate": "2025-05-27T20:45:00", "legKey": "16087372:181133:5/27/2025 8:45:00 PM", "customerKey": "CC018F1F8CB9A7A720191D6C7D437799C6F0C296FC81D4A0A3AEDB6E80FB98DB"}], "active": true, "changeType": "TK"}, {"segKey": "16225499:16225499:5/24/2025 8:45:00 PM", "LFID": 16225499, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16225499", "org": "DXB", "dest": "HYD", "depTime": "2025-05-24T20:45:00", "depTimeGMT": "2025-05-24T16:45:00", "arrTime": "2025-05-25T01:55:00", "operCarrier": "FZ", "operFlightNum": "435", "mrktCarrier": "FZ", "mrktFlightNum": "435", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 182413, "depDate": "2025-05-24T20:45:00", "legKey": "16225499:182413:5/24/2025 8:45:00 PM", "customerKey": "9871219B35ABABC401E8EBD4F514B915450D49B829BBC364326E397C08BB6B8D"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 269321048, "fName": "HUMAID KHALIFA", "lName": "ALI OBAID", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "L", "insuPurchasedate": "5/18/2025 2:11:41 AM", "provider": "<PERSON>", "status": 0, "fareClass": "L", "operFareClass": "L", "FBC": "LOMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "AF7FX-QA4YX-INS", "insuTransID": "AF7FX-QA4YX-INS/0f4fffbb-ff51-4fd8-a449-b41ee8f4e87c", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682940ee000777000000cfee#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-18T02:11:40"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "a<PERSON><PERSON><PERSON>karimi", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "H", "insuPurchasedate": "5/18/2025 2:11:41 AM", "provider": "<PERSON>", "status": 0, "fareClass": "H", "operFareClass": "H", "FBC": "HOMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "AF7FX-QA4YX-INS", "insuTransID": "AF7FX-QA4YX-INS/0f4fffbb-ff51-4fd8-a449-b41ee8f4e87c", "toRecNum": 3, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682ad8b7000778000000109d#269321048#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-19T07:11:40"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ammar.i", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/18/2025 2:11:41 AM", "provider": "<PERSON>", "status": 5, "fareClass": "Q", "operFareClass": "Q", "FBC": "QOMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "AF7FX-QA4YX-INS/0f4fffbb-ff51-4fd8-a449-b41ee8f4e87c", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6830562e000778000001178c#269321048#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-23T11:07:58"}]}], "payments": [{"paymentID": *********, "paxID": 269321050, "method": "SPAY", "status": "1", "paidDate": "2025-05-18T02:11:46", "cardNum": "************0687", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 654.77, "baseCurr": "AED", "baseAmt": 654.77, "userID": "WEB_MOBILE", "channelID": 12, "authCode": "T02888", "reference": "23098457", "externalReference": "23098457", "tranId": "21473051", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21473051}, {"paymentID": *********, "paxID": 269420327, "method": "SPAY", "status": "1", "paidDate": "2025-05-19T07:13:05", "cardNum": "************0687", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 214.24, "baseCurr": "AED", "baseAmt": 214.24, "userID": "paybylink", "channelID": 2, "authCode": "T85642", "reference": "23119844", "externalReference": "23119844", "tranId": "21492143", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21492143}, {"paymentID": *********, "paxID": 269919354, "method": "SPAY", "status": "1", "paidDate": "2025-05-23T11:09:26", "cardNum": "************0687", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 265.74, "baseCurr": "AED", "baseAmt": 265.74, "userID": "paybylink", "channelID": 2, "authCode": "T46186", "reference": "23203542", "externalReference": "23203542", "tranId": "21584446", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21584446}], "OAFlights": null, "physicalFlights": [{"key": "16087372:181133:2025-05-20T08:45:00 PM", "LFID": 16087372, "PFID": 181133, "org": "DXB", "dest": "HYD", "depDate": "2025-05-20T20:45:00", "depTime": "2025-05-20T20:45:00", "arrTime": "2025-05-21T01:55:00", "carrier": "FZ", "flightNum": "435", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "435", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "HYD", "operatingCarrier": "FZ", "flightDuration": 13200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Hyderabad", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:32:53 AM"}, {"key": "16225499:182413:2025-05-24T08:45:00 PM", "LFID": 16225499, "PFID": 182413, "org": "DXB", "dest": "HYD", "depDate": "2025-05-24T20:45:00", "depTime": "2025-05-24T20:45:00", "arrTime": "2025-05-25T01:55:00", "carrier": "FZ", "flightNum": "435", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "435", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "HYD", "operatingCarrier": "FZ", "flightDuration": 13200, "reaccomChangeAlert": true, "originName": "Dubai International Airport", "destinationName": "Hyderabad", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:36:52 AM"}, {"key": "16087372:181133:2025-05-27T08:45:00 PM", "LFID": 16087372, "PFID": 181133, "org": "DXB", "dest": "HYD", "depDate": "2025-05-27T20:45:00", "depTime": "2025-05-27T20:45:00", "arrTime": "2025-05-28T01:55:00", "carrier": "FZ", "flightNum": "435", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "435", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "HYD", "operatingCarrier": "FZ", "flightDuration": 13200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Hyderabad", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:32:53 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1352063637, "codeType": "INSU", "taxChargeID": 1352063630, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769684, "paymentMap": [{"key": "1352063637:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-19T07:11:41"}, {"chargeID": 1350769684, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769684:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1352063638, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352063630, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769678, "paymentMap": [{"key": "1352063638:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352063639, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352063630, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769677, "paymentMap": [{"key": "1352063639:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352063634, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352063630, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769675, "paymentMap": [{"key": "1352063634:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1352063635, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352063630, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769679, "paymentMap": [{"key": "1352063635:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1352063636, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352063630, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769676, "paymentMap": [{"key": "1352063636:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1350769675, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1350769674, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769675:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1350769676, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1350769674, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769676:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1350769678, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1350769674, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769678:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1350769677, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1350769674, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769677:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1350769679, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1350769674, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769679:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1352063630, "codeType": "AIR", "amt": -380, "curr": "AED", "originalAmt": -380, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T07:11:41", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769674, "paymentMap": [{"key": "1352063630:*********", "paymentID": *********, "amt": -380, "approveCode": 0}]}, {"chargeID": 1350769674, "codeType": "AIR", "amt": 380, "curr": "AED", "originalAmt": 380, "originalCurr": "AED", "status": 0, "billDate": "2025-05-18T02:11:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769674:*********", "paymentID": *********, "amt": 380, "approveCode": 0}]}, {"chargeID": 1350769985, "codeType": "PMNT", "amt": 19.07, "curr": "AED", "originalAmt": 19.07, "originalCurr": "AED", "status": 0, "billDate": "2025-05-18T02:11:49", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350769985:*********", "paymentID": *********, "amt": 19.07, "approveCode": 0}]}, {"chargeID": 1352063632, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352063630, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769681, "paymentMap": []}, {"chargeID": 1350769681, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1350769674, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352063631, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1352063630, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769680, "paymentMap": []}, {"chargeID": 1350769680, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1350769674, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352063640, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352063630, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T07:11:41", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1350769685, "paymentMap": [], "PFID": "181133"}, {"chargeID": 1350769685, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T02:11:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181133"}]}, {"recNum": 2, "charges": [{"chargeID": 1352063654, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:07:38", "billDate": "2025-05-19T07:11:41", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063654:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-19T07:11:41"}, {"chargeID": 1358744960, "codeType": "INSU", "taxChargeID": 1358744950, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:07:38", "billDate": "2025-05-23T11:07:59", "desc": "Special Service Request", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063654, "paymentMap": [{"key": "1358744960:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}"}, {"chargeID": 1352063642, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352063641, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063642:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352063643, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352063641, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063643:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}, {"key": "1352063643:*********", "paymentID": *********, "amt": 32.7, "approveCode": 0}]}, {"chargeID": 1352063644, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352063641, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063644:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352063645, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352063641, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063645:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352063646, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352063641, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063646:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1358744951, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1358744950, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "ZR: Advanced passenger information fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063645, "paymentMap": [{"key": "1358744951:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1358744952, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1358744950, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063644, "paymentMap": [{"key": "1358744952:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1358744953, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1358744950, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "F6: Passenger Facilities Charge.", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063643, "paymentMap": [{"key": "1358744953:*********", "paymentID": *********, "amt": -32.7, "approveCode": 0}, {"key": "1358744953:*********", "paymentID": *********, "amt": -12.3, "approveCode": 0}]}, {"chargeID": 1358744954, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1358744950, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "AE: Passenger Service Charge (Intl)", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063642, "paymentMap": [{"key": "1358744954:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1358744959, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1358744950, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:59", "desc": "YQ: YQ - DUMMY", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063646, "paymentMap": [{"key": "1358744959:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1352063641, "codeType": "AIR", "amt": 528, "curr": "AED", "originalAmt": 528, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "FZ 435 DXB-HYD 24May2025 Sat 20:45 01:55\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063641:*********", "paymentID": *********, "amt": 528, "approveCode": 0}]}, {"chargeID": 1358744950, "codeType": "AIR", "amt": -528, "curr": "AED", "originalAmt": -528, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "FZ 435 DXB-HYD 24May2025 Sat 20:45 01:55\r\n", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063641, "paymentMap": [{"key": "1358744950:*********", "paymentID": *********, "amt": -528, "approveCode": 0}]}, {"chargeID": 1352066130, "codeType": "PMNT", "amt": 6.24, "curr": "AED", "originalAmt": 6.24, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T07:13:10", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352066130:*********", "paymentID": *********, "amt": 6.24, "approveCode": 0}]}, {"chargeID": 1352063650, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352063650:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352063659, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:09:59", "billDate": "2025-05-19T07:11:41", "desc": "Special Service Request:FRST-7F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "182413"}, {"chargeID": 1358744955, "codeType": "FRST", "taxChargeID": 1358744950, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:09:59", "billDate": "2025-05-23T11:07:58", "desc": "Special Service Request:FRST-7F", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063659, "paymentMap": [], "PFID": "182413"}, {"chargeID": 1352063649, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352063641, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1358744956, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1358744950, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "INST: Included seat", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063649, "paymentMap": []}, {"chargeID": 1352063647, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1352063641, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1358744958, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1358744950, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:59", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063647, "paymentMap": []}, {"chargeID": 1352063648, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352063641, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-19T07:11:41", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "182413"}, {"chargeID": 1358744957, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1358744950, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T07:11:41", "billDate": "2025-05-23T11:07:58", "desc": "MLIN: Standard meal", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352063648, "paymentMap": [], "PFID": "182413"}]}, {"recNum": 3, "charges": [{"chargeID": 1358745124, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:04:23", "billDate": "2025-05-23T11:07:59", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358745124:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "1358745124:*********", "paymentID": *********, "amt": 25.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-23T11:07:59"}, {"chargeID": 1358744962, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1358744961, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358744962:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1358744963, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1358744961, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358744963:*********", "paymentID": *********, "amt": 22.7, "approveCode": 0}, {"key": "1358744963:*********", "paymentID": *********, "amt": 22.3, "approveCode": 0}]}, {"chargeID": 1358744964, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1358744961, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358744964:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1358744965, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1358744961, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358744965:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1358744966, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1358744961, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358744966:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1358744961, "codeType": "AIR", "amt": 538, "curr": "AED", "originalAmt": 538, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "FZ 435 DXB-HYD 27May2025 Tue 20:45 01:55\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358744961:*********", "paymentID": *********, "amt": 538, "approveCode": 0}]}, {"chargeID": 1358747691, "codeType": "PMNT", "amt": 7.74, "curr": "AED", "originalAmt": 7.74, "originalCurr": "AED", "status": 1, "billDate": "2025-05-23T11:09:31", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358747691:*********", "paymentID": *********, "amt": 7.74, "approveCode": 0}]}, {"chargeID": 1358745110, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358745110:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1358745125, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:05:57", "billDate": "2025-05-23T11:07:59", "desc": "Special Service Request:XLGR-6A", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358745125:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "181133"}, {"chargeID": 1358745109, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1358744961, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1358744967, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1358744961, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1358744968, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1358744961, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-23T11:07:59", "billDate": "2025-05-23T11:07:59", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181133"}]}], "parentPNRs": [], "childPNRs": []}