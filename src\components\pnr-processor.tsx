'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Play, RotateCcw, Square } from 'lucide-react';
import { PNRRecord, BatchProcessingState } from '@/types';

interface PNRProcessorProps {
  pnrs: string[];
  onProcessingComplete: (results: PNRRecord[]) => void;
  onReset?: () => void;
  onStop?: () => void;
}

export function PNRProcessor({ pnrs, onProcessingComplete, onReset, onStop }: PNRProcessorProps) {
  const [state, setState] = useState<BatchProcessingState>({
    pnrs: pnrs.map(pnr => ({ pnr, status: 'pending', progress: 0 })),
    isProcessing: false,
    completedCount: 0,
    totalCount: pnrs.length
  });

  const [shouldStop, setShouldStop] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const shouldStopRef = useRef(false);

  const processPNR = useCallback(async (pnr: string): Promise<boolean> => {
    try {
      // Check if we should stop before starting
      if (shouldStopRef.current) {
        return false;
      }

      // Update status to processing
      setState(prev => ({
        ...prev,
        pnrs: prev.pnrs.map(p =>
          p.pnr === pnr ? { ...p, status: 'processing', progress: 10 } : p
        )
      }));

      // Simulate progress updates with stop checking
      for (let progress = 20; progress <= 90; progress += 20) {
        if (shouldStopRef.current) {
          // Mark as cancelled
          setState(prev => ({
            ...prev,
            pnrs: prev.pnrs.map(p =>
              p.pnr === pnr ? { ...p, status: 'error', progress: 0, error: 'Cancelled by user' } : p
            ),
            completedCount: prev.completedCount + 1
          }));
          return false;
        }

        await new Promise(resolve => setTimeout(resolve, 500));
        setState(prev => ({
          ...prev,
          pnrs: prev.pnrs.map(p =>
            p.pnr === pnr ? { ...p, progress } : p
          )
        }));
      }

      // Final stop check before API call
      if (shouldStopRef.current) {
        setState(prev => ({
          ...prev,
          pnrs: prev.pnrs.map(p =>
            p.pnr === pnr ? { ...p, status: 'error', progress: 0, error: 'Cancelled by user' } : p
          ),
          completedCount: prev.completedCount + 1
        }));
        return false;
      }

      // Call the API with abort controller
      const response = await fetch('/api/process-pnr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pnr }),
        signal: abortControllerRef.current?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Check if the result contains an error (Cover Genius API failure, etc.)
      if (result.error || result.errorType) {
        // Handle API-level errors (like Cover Genius API failures)
        setState(prev => ({
          ...prev,
          pnrs: prev.pnrs.map(p =>
            p.pnr === pnr
              ? {
                  ...p,
                  status: 'error',
                  progress: 0,
                  error: result.error || 'API processing error'
                }
              : p
          ),
          completedCount: prev.completedCount + 1
        }));
        return false;
      }

      // Update with completed status
      setState(prev => ({
        ...prev,
        pnrs: prev.pnrs.map(p =>
          p.pnr === pnr
            ? { ...p, status: 'completed', progress: 100, result }
            : p
        ),
        completedCount: prev.completedCount + 1
      }));

      return true;

    } catch (error) {
      // Check if it was an abort error
      if (error instanceof Error && error.name === 'AbortError') {
        setState(prev => ({
          ...prev,
          pnrs: prev.pnrs.map(p =>
            p.pnr === pnr
              ? { ...p, status: 'error', progress: 0, error: 'Cancelled by user' }
              : p
          ),
          completedCount: prev.completedCount + 1
        }));
        return false;
      }

      // Update with error status
      setState(prev => ({
        ...prev,
        pnrs: prev.pnrs.map(p =>
          p.pnr === pnr
            ? {
                ...p,
                status: 'error',
                progress: 0,
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : p
        ),
        completedCount: prev.completedCount + 1
      }));
      return false;
    }
  }, []); // No dependencies needed since we're using refs

  const startProcessing = useCallback(async () => {
    // Reset stop flags and create new abort controller
    setShouldStop(false);
    shouldStopRef.current = false;
    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, isProcessing: true, completedCount: 0 }));

    // Process PNRs sequentially to avoid overwhelming the APIs
    for (const pnrRecord of state.pnrs) {
      // Check if we should stop before processing each PNR
      if (shouldStopRef.current) {
        console.log('Processing stopped by user - breaking out of loop');
        break;
      }

      if (pnrRecord.status === 'pending') {
        await processPNR(pnrRecord.pnr);
        // Note: No need to check success here since processPNR handles stop internally
      }
    }

    // Get the final state after all processing is complete
    setState(prev => {
      const finalState = { ...prev, isProcessing: false };

      // Notify parent component with the completed results
      // Use setTimeout to ensure state update is complete
      setTimeout(() => {
        onProcessingComplete(finalState.pnrs);
      }, 0);

      return finalState;
    });
  }, [state.pnrs, processPNR, onProcessingComplete]);

  const resetProcessing = useCallback(() => {
    // Reset stop flags and abort any ongoing requests
    setShouldStop(false);
    shouldStopRef.current = false;
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setState({
      pnrs: pnrs.map(pnr => ({ pnr, status: 'pending', progress: 0 })),
      isProcessing: false,
      completedCount: 0,
      totalCount: pnrs.length
    });

    // Notify parent component to clear results
    if (onReset) {
      onReset();
    }
  }, [pnrs, onReset]);

  const stopProcessing = useCallback(() => {
    console.log('Stop button clicked - setting stop flags');
    setShouldStop(true);
    shouldStopRef.current = true;

    // Abort any ongoing API requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Immediately update state to show processing has stopped
    setState(prev => ({ ...prev, isProcessing: false }));

    // Notify parent component
    if (onStop) {
      onStop();
    }
  }, [onStop]);

  const getStatusBadgeVariant = (status: PNRRecord['status']) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'processing': return 'default';
      case 'completed': return 'default';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };



  const overallProgress = state.totalCount > 0
    ? (state.completedCount / state.totalCount) * 100
    : 0;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Process PNR Records</span>
          <div className="flex gap-2">
            <Button
              onClick={startProcessing}
              disabled={state.isProcessing}
              size="sm"
            >
              <Play className="h-4 w-4 mr-2" />
              {state.isProcessing ? 'Processing...' : 'Start Processing'}
            </Button>
            {state.isProcessing && (
              <Button
                onClick={stopProcessing}
                variant="destructive"
                size="sm"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            )}
            <Button
              onClick={resetProcessing}
              disabled={state.isProcessing}
              variant="outline"
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          Process {state.totalCount} PNR records to extract insurance data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{state.completedCount} / {state.totalCount}</span>
          </div>
          <Progress value={overallProgress} className="w-full" />
        </div>

        {/* Individual PNR Status */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {state.pnrs.map((pnrRecord) => (
            <div
              key={pnrRecord.pnr}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                <span className="font-mono font-medium">{pnrRecord.pnr}</span>
                <Badge variant={getStatusBadgeVariant(pnrRecord.status)}>
                  {pnrRecord.status}
                </Badge>
              </div>

              <div className="flex items-center gap-3">
                {pnrRecord.status === 'processing' && (
                  <div className="w-24">
                    <Progress value={pnrRecord.progress} className="h-2" />
                  </div>
                )}

                {pnrRecord.status === 'completed' && pnrRecord.result && !pnrRecord.result.error && (
                  <span className="text-sm text-green-600">
                    {pnrRecord.result.summary.missingConfirmation} missing
                  </span>
                )}

                {pnrRecord.status === 'error' && (
                  <span className="text-sm text-red-600 max-w-xs truncate" title={pnrRecord.error}>
                    {pnrRecord.error}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        {state.completedCount > 0 && (
          <Alert>
            <AlertDescription>
              Completed: {state.pnrs.filter(p => p.status === 'completed').length},
              Errors: {state.pnrs.filter(p => p.status === 'error').length},
              Remaining: {state.pnrs.filter(p => p.status === 'pending').length}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
