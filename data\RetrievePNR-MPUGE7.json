{"seriesNum": "299", "PNR": "MPUGE7", "bookAgent": "apikaspitravel", "IATA": "40007160", "resCurrency": "KZT", "PNRPin": "81518133", "bookDate": "2025-03-27T08:05:02", "modifyDate": "2025-05-31T22:05:40", "resType": "TPAPI", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "367274sfq2c2g7u45bq964t0pbid87483ced96d2954c", "securityGUID": "367274sfq2c2g7u45bq964t0pbid87483ced96d2954c", "lastLoadGUID": "19bae13d-a156-40ce-aee8-a59c3e0be485", "isAsyncPNR": false, "MasterPNR": "MPUGE7", "segments": [{"segKey": "16142953:16142953:4/9/2025 7:30:00 AM", "LFID": 16142953, "depDate": "2025-04-09T00:00:00", "flightGroupId": "16142953", "org": "NQZ", "dest": "BAH", "depTime": "2025-04-09T07:30:00", "depTimeGMT": "2025-04-09T02:30:00", "arrTime": "2025-04-09T14:50:00", "operCarrier": "FZ", "operFlightNum": "1308/021", "mrktCarrier": "FZ ", "mrktFlightNum": "1308/021", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181504, "depDate": "2025-04-09T07:30:00", "legKey": "16142953:181504:4/9/2025 7:30:00 AM", "customerKey": "FA6D0F2D0D63C94FC61A1F0850DC2899BF254B2800E846333CF2EE75994700AB"}, {"PFID": 181017, "depDate": "2025-04-09T14:35:00", "legKey": "16142953:181017:4/9/2025 2:35:00 PM", "customerKey": "69FE51544D507F4A696BD9D617E74064A77523FDD319D6C8209451B1EF05B04F"}], "active": true, "changeType": "AC"}, {"segKey": "16092398:16092398:5/7/2025 3:35:00 PM", "LFID": 16092398, "depDate": "2025-05-07T00:00:00", "flightGroupId": "16092398", "org": "BAH", "dest": "NQZ", "depTime": "2025-05-07T15:35:00", "depTimeGMT": "2025-05-07T12:35:00", "arrTime": "2025-05-08T03:25:00", "operCarrier": "FZ", "operFlightNum": "022/1303", "mrktCarrier": "FZ ", "mrktFlightNum": "022/1303", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181015, "depDate": "2025-05-07T15:35:00", "legKey": "16092398:181015:5/7/2025 3:35:00 PM", "customerKey": "AF18027CEED29E465FB97B9A76383207D93CF892A678B7F023915D2F21F9C19B"}, {"PFID": 181446, "depDate": "2025-05-07T21:50:00", "legKey": "16092398:181446:5/7/2025 9:50:00 PM", "customerKey": "D90429253469116D615F13D52D7722D4B2C632532B77426B09E073B0D3725223"}], "active": true, "changeType": "AC"}, {"segKey": "16092398:16092398:6/1/2025 3:35:00 PM", "LFID": 16092398, "depDate": "2025-06-01T00:00:00", "flightGroupId": "16092398", "org": "BAH", "dest": "NQZ", "depTime": "2025-06-01T15:35:00", "depTimeGMT": "2025-06-01T12:35:00", "arrTime": "2025-06-02T03:25:00", "operCarrier": "FZ", "operFlightNum": "022/1303", "mrktCarrier": "FZ ", "mrktFlightNum": "022/1303", "persons": [{"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181015, "depDate": "2025-06-01T15:35:00", "legKey": "16092398:181015:6/1/2025 3:35:00 PM", "customerKey": "AD45DF5501058EE1BB83A797DE90FCF43B746748B0810546C2B3C07A6333BFB1"}, {"PFID": 181446, "depDate": "2025-06-01T21:50:00", "legKey": "16092398:181446:6/1/2025 9:50:00 PM", "customerKey": "7804289E23F6513492E178A66F5B6160BEEE93328F32D5625E01CA309702D0FB"}], "active": true, "changeType": "AC"}, {"segKey": "16092398:16092398:6/10/2025 3:35:00 PM", "LFID": 16092398, "depDate": "2025-06-10T00:00:00", "flightGroupId": "16092398", "org": "BAH", "dest": "NQZ", "depTime": "2025-06-10T15:35:00", "depTimeGMT": "2025-06-10T12:35:00", "arrTime": "2025-06-11T03:25:00", "operCarrier": "FZ", "operFlightNum": "022/1303", "mrktCarrier": "FZ ", "mrktFlightNum": "022/1303", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181015, "depDate": "2025-06-10T15:35:00", "legKey": "16092398:181015:6/10/2025 3:35:00 PM", "customerKey": "8EDE8E27230B499A09E12E8631E89F0295EB922E885155142C14DB5B6B91C183"}, {"PFID": 181446, "depDate": "2025-06-10T21:50:00", "legKey": "16092398:181446:6/10/2025 9:50:00 PM", "customerKey": "C9650EBAEEE8E2B33CC6D4121838E1A12DD81D9937B33B7C87DE4559292CD050"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": *********, "fName": "REGINA", "lName": "GUMAROVA", "title": "MRS", "PTCID": 1, "gender": "F", "DOB": "1994-04-16T00:00:00", "nationality": "398", "recNum": [1, 2, 3, 4], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "apikaspitravel", "statusReasonID": 0, "markFareClass": "W", "status": 5, "fareClass": "W", "operFareClass": "W", "FBC": "WRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "67e506a900077800000004df#1#1#TPAPI#VAYANT#CREATE", "fareTypeID": 12, "channelID": 5, "bookDate": "2025-03-27T08:05:02"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "apikaspitravel", "cancelAgent": "40007160alexa1", "statusReasonID": 0, "markFareClass": "O", "status": 0, "fareClass": "O", "operFareClass": "O", "FBC": "ORB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67e506a900077800000004df#1#2#TPAPI#VAYANT#CREATE", "fareTypeID": 12, "channelID": 5, "cancelReasonID": 0, "bookDate": "2025-03-27T08:05:02"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "40007160alexa1", "cancelAgent": "40007160nurbol", "statusReasonID": 0, "markFareClass": "W", "insuPurchasedate": "4/25/2025 10:21:43 PM", "provider": "AIG", "status": 0, "fareClass": "W", "operFareClass": "W", "FBC": "WRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "52beede1-272d-40cc-9c02-64a53f8ba692", "toRecNum": 4, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680c0aef0007780000005f25#*********#2#TA#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 16, "cancelReasonID": 0, "bookDate": "2025-04-25T22:22:27"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "40007160nurbol", "statusReasonID": 0, "markFareClass": "O", "status": 1, "fareClass": "O", "operFareClass": "O", "FBC": "ORB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "63LCB-NH9AH-INS/db525334-3ee1-4b9e-a829-bc92b3e486ad", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b7cd400077700000185b4#*********#2#TA#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 16, "bookDate": "2025-05-31T22:04:51"}]}], "payments": [{"paymentID": *********, "paxID": *********, "method": "INVC", "status": "1", "paidDate": "2025-05-31T22:05:38", "IATANum": "40007160", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 237668.52, "baseCurr": "KZT", "baseAmt": 237668.52, "userID": "40007160nurbol", "channelID": 16, "authCode": "110853158", "reference": "A5468813", "externalReference": "A5468813", "tranId": "21754921", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21754921}, {"paymentID": *********, "paxID": *********, "method": "INVC", "status": "1", "paidDate": "2025-04-25T22:22:33", "IATANum": "40007160", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 35724.8, "baseCurr": "KZT", "baseAmt": 35724.8, "userID": "40007160alexa1", "channelID": 16, "authCode": "110554694", "reference": "A5252802", "externalReference": "A5252802", "tranId": "21029897", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21029897}, {"paymentID": *********, "paxID": *********, "method": "INVC", "status": "1", "paidDate": "2025-03-27T08:06:53", "IATANum": "40007160", "paidCurr": "KZT", "paidAmt": 461291, "baseCurr": "KZT", "baseAmt": 461291, "userID": "apikaspitravel", "channelID": 5, "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1", "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": *********, "method": "VISA", "status": "2", "paidDate": "2025-05-27T16:31:03", "cardNum": "************4838", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 46571.45, "baseCurr": "KZT", "baseAmt": 46571.45, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "REGINA GUMAROVA", "reference": "23285732", "externalReference": "23285732", "tranId": "21666325", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21666325}, {"paymentID": *********, "paxID": *********, "method": "VISA", "status": "1", "paidDate": "2025-05-27T16:33:01", "cardNum": "************4838", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 46571.45, "baseCurr": "KZT", "baseAmt": 46571.45, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "REGINA GUMAROVA", "authCode": "469776", "reference": "23285771", "externalReference": "23285771", "tranId": "21666325", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21666325}], "OAFlights": null, "physicalFlights": [{"key": "16142953:181504:2025-04-09T07:30:00 AM", "LFID": 16142953, "PFID": 181504, "org": "NQZ", "dest": "DXB", "depDate": "2025-04-09T07:30:00", "depTime": "2025-04-09T07:30:00", "arrTime": "2025-04-09T11:20:00", "carrier": "FZ", "flightNum": "1308", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1308", "flightStatus": "CLOSED", "originMetroGroup": "NQZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 17400, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/4/2025 6:05:25 AM"}, {"key": "16142953:181017:2025-04-09T02:35:00 PM", "LFID": 16142953, "PFID": 181017, "org": "DXB", "dest": "BAH", "depDate": "2025-04-09T14:35:00", "depTime": "2025-04-09T14:35:00", "arrTime": "2025-04-09T14:50:00", "carrier": "FZ", "flightNum": "021", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "021", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BAH", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bahrain", "isActive": false, "changeType": "AC", "flightChangeTime": "3/4/2025 6:05:25 AM"}, {"key": "16092398:181015:2025-05-07T03:35:00 PM", "LFID": 16092398, "PFID": 181015, "org": "BAH", "dest": "DXB", "depDate": "2025-05-07T15:35:00", "depTime": "2025-05-07T15:35:00", "arrTime": "2025-05-07T17:55:00", "carrier": "FZ", "flightNum": "022", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "022", "flightStatus": "CLOSED", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:36:28 AM"}, {"key": "16092398:181446:2025-05-07T09:50:00 PM", "LFID": 16092398, "PFID": 181446, "org": "DXB", "dest": "NQZ", "depDate": "2025-05-07T21:50:00", "depTime": "2025-05-07T21:50:00", "arrTime": "2025-05-08T03:25:00", "carrier": "FZ", "flightNum": "1303", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1303", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "NQZ", "operatingCarrier": "FZ", "flightDuration": 16500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON>", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:36:28 AM"}, {"key": "16092398:181015:2025-06-01T03:35:00 PM", "LFID": 16092398, "PFID": 181015, "org": "BAH", "dest": "DXB", "depDate": "2025-06-01T15:35:00", "depTime": "2025-06-01T15:35:00", "arrTime": "2025-06-01T17:55:00", "carrier": "FZ", "flightNum": "022", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "022", "flightStatus": "CLOSED", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "5/6/2025 8:35:54 AM"}, {"key": "16092398:181446:2025-06-01T09:50:00 PM", "LFID": 16092398, "PFID": 181446, "org": "DXB", "dest": "NQZ", "depDate": "2025-06-01T21:50:00", "depTime": "2025-06-01T21:50:00", "arrTime": "2025-06-02T03:25:00", "carrier": "FZ", "flightNum": "1303", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1303", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "NQZ", "operatingCarrier": "FZ", "flightDuration": 16500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON>", "isActive": false, "changeType": "AC", "flightChangeTime": "5/6/2025 8:35:54 AM"}, {"key": "16092398:181015:2025-06-10T03:35:00 PM", "LFID": 16092398, "PFID": 181015, "org": "BAH", "dest": "DXB", "depDate": "2025-06-10T15:35:00", "depTime": "2025-06-10T15:35:00", "arrTime": "2025-06-10T17:55:00", "carrier": "FZ", "flightNum": "022", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "022", "flightStatus": "OPEN", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/10/2025 6:35:33 AM"}, {"key": "16092398:181446:2025-06-10T09:50:00 PM", "LFID": 16092398, "PFID": 181446, "org": "DXB", "dest": "NQZ", "depDate": "2025-06-10T21:50:00", "depTime": "2025-06-10T21:50:00", "arrTime": "2025-06-11T03:25:00", "carrier": "FZ", "flightNum": "1303", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1303", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "NQZ", "operatingCarrier": "FZ", "flightDuration": 16500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON>", "isActive": true, "changeType": "AC", "flightChangeTime": "3/10/2025 6:35:33 AM"}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 6970.8, "curr": "KZT", "originalAmt": 6970.8, "originalCurr": "KZT", "status": 0, "exchRate": 1, "billDate": "2025-04-25T22:22:27", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 6970.8, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-25T22:22:27"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 60872, "curr": "KZT", "originalAmt": 60872, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 60872, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 727, "curr": "KZT", "originalAmt": 727, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 727, "approveCode": 0}]}, {"chargeID": 1320701831, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": **********, "amt": 414, "curr": "KZT", "originalAmt": 414, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320701831:*********", "paymentID": *********, "amt": 414, "approveCode": 0}]}, {"chargeID": 1320701807, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 6543, "curr": "KZT", "originalAmt": 6543, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320701807:*********", "paymentID": *********, "amt": 6543, "approveCode": 0}]}, {"chargeID": 1320701832, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": **********, "amt": 13787, "curr": "KZT", "originalAmt": 13787, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320701832:*********", "paymentID": *********, "amt": 13787, "approveCode": 0}]}, {"chargeID": 1320701808, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": **********, "amt": 535, "curr": "KZT", "originalAmt": 535, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320701808:*********", "paymentID": *********, "amt": 535, "approveCode": 0}]}, {"chargeID": 1370897926, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -727, "curr": "KZT", "originalAmt": -727, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:04:51", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1370897926:*********", "paymentID": *********, "amt": -727, "approveCode": 0}]}, {"chargeID": 1370897921, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": **********, "amt": -535, "curr": "KZT", "originalAmt": -535, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:04:51", "desc": "International Advanced Passenger Information Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320701808, "paymentMap": [{"key": "1370897921:*********", "paymentID": *********, "amt": -535, "approveCode": 0}]}, {"chargeID": 1370897925, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -60872, "curr": "KZT", "originalAmt": -60872, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:04:51", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1370897925:*********", "paymentID": *********, "amt": -60872, "approveCode": 0}]}, {"chargeID": 1370897923, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": **********, "amt": -414, "curr": "KZT", "originalAmt": -414, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:04:51", "desc": "Passenger Service Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320701831, "paymentMap": [{"key": "1370897923:*********", "paymentID": *********, "amt": -414, "approveCode": 0}]}, {"chargeID": 1370897924, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -6543, "curr": "KZT", "originalAmt": -6543, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:04:51", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320701807, "paymentMap": [{"key": "1370897924:*********", "paymentID": *********, "amt": -6543, "approveCode": 0}]}, {"chargeID": 1370897920, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": **********, "amt": -13787, "curr": "KZT", "originalAmt": -13787, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:04:51", "desc": "Passenger Service Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320701832, "paymentMap": [{"key": "1370897920:*********", "paymentID": *********, "amt": -13787, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 171136, "curr": "KZT", "originalAmt": 171136, "originalCurr": "KZT", "status": 0, "billDate": "2025-04-25T22:22:27", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 28754, "approveCode": 0}, {"key": "**********:*********", "paymentID": *********, "amt": 142382, "approveCode": 0}]}, {"chargeID": 1370897922, "codeType": "AIR", "amt": -171136, "curr": "KZT", "originalAmt": -171136, "originalCurr": "KZT", "status": 0, "billDate": "2025-05-31T22:04:51", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1370897922:*********", "paymentID": *********, "amt": -142382, "approveCode": 0}, {"key": "1370897922:*********", "paymentID": *********, "amt": -28754, "approveCode": 0}]}, {"chargeID": 1364549463, "codeType": "PMNT", "amt": 1356.45, "curr": "KZT", "originalAmt": 1356.45, "originalCurr": "KZT", "status": 0, "billDate": "2025-05-27T16:33:12", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364549463:*********", "paymentID": *********, "amt": 1356.45, "approveCode": 0}]}, {"chargeID": 1370897927, "codeType": "PNLT", "amt": 232008, "curr": "KZT", "originalAmt": 232008, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-31T22:04:51", "desc": "Penalty AddedDueToModify FZ  022 BAH  - DXB  01-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897927:*********", "paymentID": *********, "amt": 28754, "approveCode": 0}, {"key": "1370897927:*********", "paymentID": *********, "amt": 203254, "approveCode": 0}]}, {"chargeID": 1364541732, "codeType": "BUPX", "amt": 45215, "curr": "KZT", "originalAmt": 45215, "originalCurr": "KZT", "status": 0, "exchRate": 1, "billDate": "2025-05-27T16:30:28", "desc": "BUPX", "comment": "FLXID:BUPX_GLOBAL_Z1_Z3:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364541732:*********", "paymentID": *********, "amt": 45215, "approveCode": 0}]}, {"chargeID": 1320701833, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1320701839, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181015"}, {"chargeID": 1320701840, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181446"}]}, {"recNum": 4, "charges": [{"chargeID": 1370897947, "codeType": "INSU", "amt": 4966.52, "curr": "KZT", "originalAmt": 4966.52, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-31T22:04:52", "desc": "INSU", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897947:*********", "paymentID": *********, "amt": 4966.52, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.0\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-31T22:04:52"}, {"chargeID": 1370897933, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1370897928, "amt": 414, "curr": "KZT", "originalAmt": 414, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897933:*********", "paymentID": *********, "amt": 414, "approveCode": 0}]}, {"chargeID": 1370897934, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1370897928, "amt": 13787, "curr": "KZT", "originalAmt": 13787, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897934:*********", "paymentID": *********, "amt": 13787, "approveCode": 0}]}, {"chargeID": 1370897931, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370897928, "amt": 60872, "curr": "KZT", "originalAmt": 60872, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897931:*********", "paymentID": *********, "amt": 60872, "approveCode": 0}]}, {"chargeID": 1370897929, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370897928, "amt": 6543, "curr": "KZT", "originalAmt": 6543, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897929:*********", "paymentID": *********, "amt": 6543, "approveCode": 0}]}, {"chargeID": 1370897930, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1370897928, "amt": 535, "curr": "KZT", "originalAmt": 535, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897930:*********", "paymentID": *********, "amt": 535, "approveCode": 0}]}, {"chargeID": 1370897932, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370897928, "amt": 727, "curr": "KZT", "originalAmt": 727, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897932:*********", "paymentID": *********, "amt": 727, "approveCode": 0}]}, {"chargeID": 1370897928, "codeType": "AIR", "amt": 171830, "curr": "KZT", "originalAmt": 171830, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-31T22:04:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370897928:*********", "paymentID": *********, "amt": 171830, "approveCode": 0}]}, {"chargeID": 1370897935, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370897928, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370897942, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181015"}, {"chargeID": 1370897941, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-05-31T22:04:52", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181446"}]}, {"recNum": 1, "charges": [{"chargeID": 1281018670, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1281018667, "amt": 503, "curr": "KZT", "originalAmt": 503, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018670:*********", "paymentID": *********, "amt": 503, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-03-27T08:05:02"}, {"chargeID": 1281018668, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1281018667, "amt": 57320, "curr": "KZT", "originalAmt": 57320, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018668:*********", "paymentID": *********, "amt": 57320, "approveCode": 0}]}, {"chargeID": 1281018674, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1281018667, "amt": 6161, "curr": "KZT", "originalAmt": 6161, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018674:*********", "paymentID": *********, "amt": 6161, "approveCode": 0}]}, {"chargeID": 1281018673, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": 1281018667, "amt": 1916, "curr": "KZT", "originalAmt": 1916, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Aviation Security Fee", "comment": "Aviation Security Fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018673:*********", "paymentID": *********, "amt": 1916, "approveCode": 0}]}, {"chargeID": 1281018675, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": 1281018667, "amt": 6034, "curr": "KZT", "originalAmt": 6034, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018675:*********", "paymentID": *********, "amt": 6034, "approveCode": 0}]}, {"chargeID": 1281018672, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1281018667, "amt": 685, "curr": "KZT", "originalAmt": 685, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018672:*********", "paymentID": *********, "amt": 685, "approveCode": 0}]}, {"chargeID": 1281018667, "codeType": "AIR", "amt": 132236, "curr": "KZT", "originalAmt": 132236, "originalCurr": "KZT", "status": 1, "billDate": "2025-03-27T08:05:02", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018667:*********", "paymentID": *********, "amt": 132236, "approveCode": 0}]}, {"chargeID": 1281018669, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1281018667, "amt": 5028, "curr": "KZT", "originalAmt": 5028, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018669:*********", "paymentID": *********, "amt": 5028, "approveCode": 0}]}, {"chargeID": 1281018671, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1281018667, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1281018687, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181504"}, {"chargeID": 1281018688, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181017"}]}, {"recNum": 2, "charges": [{"chargeID": 1281018682, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1281018677, "amt": 13373, "curr": "KZT", "originalAmt": 13373, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018682:*********", "paymentID": *********, "amt": 13373, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-03-27T08:05:02"}, {"chargeID": 1281018678, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1281018677, "amt": 57320, "curr": "KZT", "originalAmt": 57320, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018678:*********", "paymentID": *********, "amt": 57320, "approveCode": 0}]}, {"chargeID": 1281018685, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1281018677, "amt": 402, "curr": "KZT", "originalAmt": 402, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018685:*********", "paymentID": *********, "amt": 402, "approveCode": 0}]}, {"chargeID": 1281018683, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1281018677, "amt": 685, "curr": "KZT", "originalAmt": 685, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018683:*********", "paymentID": *********, "amt": 685, "approveCode": 0}]}, {"chargeID": 1281018684, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1281018677, "amt": 6161, "curr": "KZT", "originalAmt": 6161, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018684:*********", "paymentID": *********, "amt": 6161, "approveCode": 0}]}, {"chargeID": 1281018680, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1281018677, "amt": 503, "curr": "KZT", "originalAmt": 503, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018680:*********", "paymentID": *********, "amt": 503, "approveCode": 0}]}, {"chargeID": 1320701818, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1281018677, "amt": -685, "curr": "KZT", "originalAmt": -685, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018683, "paymentMap": [{"key": "1320701818:*********", "paymentID": *********, "amt": -685, "approveCode": 0}]}, {"chargeID": 1320701816, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1281018677, "amt": -402, "curr": "KZT", "originalAmt": -402, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Passenger Service Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018685, "paymentMap": [{"key": "1320701816:*********", "paymentID": *********, "amt": -402, "approveCode": 0}]}, {"chargeID": 1320701815, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1281018677, "amt": -57320, "curr": "KZT", "originalAmt": -57320, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018678, "paymentMap": [{"key": "1320701815:*********", "paymentID": *********, "amt": -57320, "approveCode": 0}]}, {"chargeID": 1320701820, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1281018677, "amt": -13373, "curr": "KZT", "originalAmt": -13373, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Passenger Service Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018682, "paymentMap": [{"key": "1320701820:*********", "paymentID": *********, "amt": -13373, "approveCode": 0}]}, {"chargeID": 1320701819, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1281018677, "amt": -503, "curr": "KZT", "originalAmt": -503, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "International Advanced Passenger Information Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018680, "paymentMap": [{"key": "1320701819:*********", "paymentID": *********, "amt": -503, "approveCode": 0}]}, {"chargeID": 1320701814, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1281018677, "amt": -6161, "curr": "KZT", "originalAmt": -6161, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-25T22:22:27", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018684, "paymentMap": [{"key": "1320701814:*********", "paymentID": *********, "amt": -6161, "approveCode": 0}]}, {"chargeID": 1281018677, "codeType": "AIR", "amt": 167936, "curr": "KZT", "originalAmt": 167936, "originalCurr": "KZT", "status": 0, "billDate": "2025-03-27T08:05:02", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018677:*********", "paymentID": *********, "amt": 167936, "approveCode": 0}]}, {"chargeID": 1320701817, "codeType": "AIR", "amt": -167936, "curr": "KZT", "originalAmt": -167936, "originalCurr": "KZT", "status": 0, "billDate": "2025-04-25T22:22:27", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1281018677, "paymentMap": [{"key": "1320701817:*********", "paymentID": *********, "amt": -167936, "approveCode": 0}]}, {"chargeID": 1281018679, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1281018677, "amt": 5028, "curr": "KZT", "originalAmt": 5028, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1281018679:*********", "paymentID": *********, "amt": 5028, "approveCode": 0}]}, {"chargeID": 1320701821, "codeType": "PNLT", "amt": 21120, "curr": "KZT", "originalAmt": 21120, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-25T22:22:27", "desc": "Penalty AddedDueToModify FZ  022 BAH  - DXB  07-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320701821:*********", "paymentID": *********, "amt": 21120, "approveCode": 0}]}, {"chargeID": 1281018681, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1281018677, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1281018690, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181015"}, {"chargeID": 1281018689, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-03-27T08:05:02", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181446"}]}], "parentPNRs": [], "childPNRs": []}