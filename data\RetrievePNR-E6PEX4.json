{"seriesNum": "299", "PNR": "E6PEX4", "bookAgent": "WEB_MOBILE", "resCurrency": "AED", "PNRPin": "82602455", "bookDate": "2025-05-06T17:50:18", "modifyDate": "2025-05-16T08:31:57", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "5058hdu24bm9466ezds3d2d4f02eb33df09da21da7d1", "securityGUID": "5058hdu24bm9466ezds3d2d4f02eb33df09da21da7d1", "lastLoadGUID": "353D5018622A638BE0631E206F0ADC0F", "isAsyncPNR": false, "MasterPNR": "E6PEX4", "segments": [{"segKey": "16087271:16087271:5/17/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-05-17T17:05:00", "depTimeGMT": "2025-05-17T14:05:00", "arrTime": "2025-05-17T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ", "mrktFlightNum": "018", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-17T17:05:00", "legKey": "16087271:181022:5/17/2025 5:05:00 PM", "customerKey": "A28AC8BACA08D2979813B0063B3CF6A59B378665E5626EF4C2C8CCF409F9C2ED"}], "active": true, "changeType": "TK"}, {"segKey": "16066159:16066159:5/15/2025 7:40:00 PM", "LFID": 16066159, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16066159", "org": "DXB", "dest": "DOH", "depTime": "2025-05-15T19:40:00", "depTimeGMT": "2025-05-15T15:40:00", "arrTime": "2025-05-15T19:50:00", "operCarrier": "FZ", "operFlightNum": "005", "mrktCarrier": "FZ ", "mrktFlightNum": "005", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181000, "depDate": "2025-05-15T19:40:00", "legKey": "16066159:181000:5/15/2025 7:40:00 PM", "customerKey": "4183101D6AE59E3D3DC3A31A01185AEB0488A1A21D39AF5EFC8EFC059364A6F0"}], "active": true, "changeType": "TK"}, {"segKey": "16087259:16087259:5/17/2025 10:55:00 AM", "LFID": 16087259, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087259", "org": "DOH", "dest": "DXB", "depTime": "2025-05-17T10:55:00", "depTimeGMT": "2025-05-17T07:55:00", "arrTime": "2025-05-17T13:10:00", "operCarrier": "FZ", "operFlightNum": "004", "mrktCarrier": "FZ ", "mrktFlightNum": "004", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181009, "depDate": "2025-05-17T10:55:00", "legKey": "16087259:181009:5/17/2025 10:55:00 AM", "customerKey": "9A639BC980E5DE19D00FF85594B0B3828E059CB55CC6DE05215B2EC98DF6E545"}], "active": true}], "persons": [{"paxID": 268110678, "fName": "MOHAMMAD FAAZ ZAID", "lName": "ALI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/6/2025 5:50:18 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "RJ9XG-WH8GZ-INS", "insuTransID": "RJ9XG-WH8GZ-INS/6a15dceb-9def-4504-864b-85c889efbc35", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6824bade0007770000003984#268110678#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-06T17:50:18"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/6/2025 5:50:18 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "RJ9XG-WH8GZ-INS", "insuTransID": "RJ9XG-WH8GZ-INS/6a15dceb-9def-4504-864b-85c889efbc35", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681a4a8d0007770000001d56#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-06T17:50:18"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "hifsa.tahir", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/6/2025 5:50:18 PM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "RJ9XG-WH8GZ-INS/6a15dceb-9def-4504-864b-85c889efbc35", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6824bade0007770000003984#268110678#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-14T15:49:26"}]}], "payments": [{"paymentID": *********, "paxID": 268110906, "method": "MSCD", "status": "1", "paidDate": "2025-05-06T17:52:03", "cardNum": "************3656", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1453.02, "baseCurr": "AED", "baseAmt": 1453.02, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "357896", "reference": "22879330", "externalReference": "22879330", "tranId": "21251724", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21251724}, {"paymentID": *********, "paxID": 268983578, "method": "IPAY", "status": "1", "paidDate": "2025-05-14T15:52:04", "cardNum": "************2412", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 247.2, "baseCurr": "AED", "baseAmt": 247.2, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON> ", "authCode": "495208", "reference": "23033791", "externalReference": "23033791", "tranId": "21409516", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21409516}], "OAFlights": null, "physicalFlights": [{"key": "16066159:181000:2025-05-15T07:40:00 PM", "LFID": 16066159, "PFID": 181000, "org": "DXB", "dest": "DOH", "depDate": "2025-05-15T19:40:00", "depTime": "2025-05-15T19:40:00", "arrTime": "2025-05-15T19:50:00", "carrier": "FZ", "flightNum": "005", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "005", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "TK", "flightChangeTime": "11/29/2024 4:28:29 AM"}, {"key": "16087259:181009:2025-05-17T10:55:00 AM", "LFID": 16087259, "PFID": 181009, "org": "DOH", "dest": "DXB", "depDate": "2025-05-17T10:55:00", "depTime": "2025-05-17T10:55:00", "arrTime": "2025-05-17T13:10:00", "carrier": "FZ", "flightNum": "004", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "004", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087271:181022:2025-05-17T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-17T17:05:00", "depTime": "2025-05-17T17:05:00", "arrTime": "2025-05-17T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:39:49 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1334842634, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842634:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-06T17:50:18"}, {"chargeID": 1346333108, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:46:41", "billDate": "2025-05-14T15:49:26", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333108:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1346333109, "codeType": "INSU", "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842634, "paymentMap": [{"key": "1346333109:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1334842619, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1334842615, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842619:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1334842618, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1334842615, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842618:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1334842617, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1334842615, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842617:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1334842620, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1334842615, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842620:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1334842622, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1334842615, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842622:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1334842621, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1334842615, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842621:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1334842615, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 1, "billDate": "2025-05-06T17:50:18", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842615:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": 1346336452, "codeType": "PMNT", "amt": 7.2, "curr": "AED", "originalAmt": 7.2, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T15:52:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346336452:*********", "paymentID": *********, "amt": 7.2, "approveCode": 0}]}, {"chargeID": 1334847781, "codeType": "PMNT", "amt": 42.32, "curr": "AED", "originalAmt": 42.32, "originalCurr": "AED", "status": 1, "billDate": "2025-05-06T17:52:07", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334847781:*********", "paymentID": *********, "amt": 42.32, "approveCode": 0}]}, {"chargeID": 1334842616, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1334842615, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1334842637, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181000"}]}, {"recNum": 2, "charges": [{"chargeID": 1334842636, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842636:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-06T17:50:18"}, {"chargeID": 1346333117, "codeType": "INSU", "taxChargeID": 1346333110, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842636, "paymentMap": [{"key": "1346333117:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1334842626, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1334842624, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842626:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1334842628, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1334842624, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842628:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1334842630, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1334842624, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842630:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1334842629, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1334842624, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842629:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1334842627, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1334842624, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842627:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1334842631, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1334842624, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842631:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346333111, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1346333110, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "Airport Fee.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842627, "paymentMap": [{"key": "1346333111:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1346333112, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346333110, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842629, "paymentMap": [{"key": "1346333112:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1346333113, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346333110, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "Passenger Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842630, "paymentMap": [{"key": "1346333113:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1346333114, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1346333110, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "Passenger safety and security fees (PSSF)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842631, "paymentMap": [{"key": "1346333114:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1346333118, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346333110, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842626, "paymentMap": [{"key": "1346333118:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346333119, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1346333110, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "Passenger Facility Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842628, "paymentMap": [{"key": "1346333119:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1334842624, "codeType": "AIR", "amt": 495, "curr": "AED", "originalAmt": 495, "originalCurr": "AED", "status": 0, "billDate": "2025-05-06T17:50:18", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334842624:*********", "paymentID": *********, "amt": 495, "approveCode": 0}]}, {"chargeID": 1346333110, "codeType": "AIR", "amt": -495, "curr": "AED", "originalAmt": -495, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T15:49:26", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842624, "paymentMap": [{"key": "1346333110:*********", "paymentID": *********, "amt": -495, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:26", "billDate": "2025-05-14T15:49:26", "desc": "CancelNoRefund FZ 004 DOH - DXB 17.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1334842625, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1334842624, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346333115, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1346333110, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842625, "paymentMap": []}, {"chargeID": 1334842638, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T17:50:18", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181009"}, {"chargeID": 1346333116, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346333110, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T15:49:26", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334842638, "paymentMap": [], "PFID": "181009"}]}, {"recNum": 3, "charges": [{"chargeID": 1346333131, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:46:42", "billDate": "2025-05-14T15:49:27", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333131:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T15:49:27"}, {"chargeID": 1346333122, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346333121, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:26", "billDate": "2025-05-14T15:49:26", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333122:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346333123, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1346333121, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:26", "billDate": "2025-05-14T15:49:27", "desc": "QA: Airport Fee.", "comment": "QA: Airport Fee.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333123:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1346333124, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1346333121, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "R9: Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333124:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346333125, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1346333121, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333125:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1346333126, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1346333121, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "G4: Passenger Facility Charge.", "comment": "G4: Passenger Facility Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333126:*********", "paymentID": *********, "amt": 64.3, "approveCode": 0}, {"key": "1346333126:*********", "paymentID": *********, "amt": 5.7, "approveCode": 0}]}, {"chargeID": 1346333127, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346333121, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333127:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1346333121, "codeType": "AIR", "amt": 525, "curr": "AED", "originalAmt": 525, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:26", "billDate": "2025-05-14T15:49:26", "desc": "FZ 018 DOH-DXB 17May2025 Sat 17:05 19:20\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333121:*********", "paymentID": *********, "amt": 525, "approveCode": 0}]}, {"chargeID": 1346333130, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346333130:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1346333128, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1346333121, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346333129, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346333121, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T15:49:27", "billDate": "2025-05-14T15:49:27", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}], "parentPNRs": [], "childPNRs": []}