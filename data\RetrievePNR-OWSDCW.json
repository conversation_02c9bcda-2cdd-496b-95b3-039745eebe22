{"seriesNum": "299", "PNR": "OWSDCW", "bookAgent": "MOBILE_APP", "resCurrency": "QAR", "PNRPin": "82511637", "bookDate": "2025-05-03T13:23:27", "modifyDate": "2025-05-23T14:56:02", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "5b3e4a3ar05c9i24ufe4z568aeebf07abd157086b0a7", "securityGUID": "5b3e4a3ar05c9i24ufe4z568aeebf07abd157086b0a7", "lastLoadGUID": "731e0f19-a183-4733-9b86-261922f4d7cf", "isAsyncPNR": false, "MasterPNR": "OWSDCW", "segments": [{"segKey": "16087259:16087259:5/23/2025 10:55:00 AM", "LFID": 16087259, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087259", "org": "DOH", "dest": "DXB", "depTime": "2025-05-23T10:55:00", "depTimeGMT": "2025-05-23T07:55:00", "arrTime": "2025-05-23T13:10:00", "operCarrier": "FZ", "operFlightNum": "004", "mrktCarrier": "FZ ", "mrktFlightNum": "004", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181009, "depDate": "2025-05-23T10:55:00", "legKey": "16087259:181009:5/23/2025 10:55:00 AM", "customerKey": "C87FC5B4DF5314E9545B2E2064A55959120AE1B09FE9B838ADF266FAC9C11160"}], "active": true}, {"segKey": "16087260:16087260:5/24/2025 5:20:00 PM", "LFID": 16087260, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16087260", "org": "DXB", "dest": "DOH", "depTime": "2025-05-24T17:20:00", "depTimeGMT": "2025-05-24T13:20:00", "arrTime": "2025-05-24T17:30:00", "operCarrier": "FZ", "operFlightNum": "009", "mrktCarrier": "FZ ", "mrktFlightNum": "009", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181011, "depDate": "2025-05-24T17:20:00", "legKey": "16087260:181011:5/24/2025 5:20:00 PM", "customerKey": "D3BE68C8DB21E59AF6C2A6FA8B2CC56241AFE38C96FFA662E0F89DC931EA7109"}], "active": true, "changeType": "AC"}, {"segKey": "16087258:16087258:5/24/2025 8:45:00 AM", "LFID": 16087258, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16087258", "org": "DXB", "dest": "DOH", "depTime": "2025-05-24T08:45:00", "depTimeGMT": "2025-05-24T04:45:00", "arrTime": "2025-05-24T08:55:00", "operCarrier": "FZ", "operFlightNum": "001", "mrktCarrier": "FZ ", "mrktFlightNum": "001", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181010, "depDate": "2025-05-24T08:45:00", "legKey": "16087258:181010:5/24/2025 8:45:00 AM", "customerKey": "076BFC148B799D009F81AF3868D6599BE0EDFD6E8C51902A2E5B79D65DCAA42C"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 267769561, "fName": "TENEALYA", "lName": "LITTLEJOHN", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1989-09-04T00:00:00", "FFNum": "775279890", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/3/2025 1:22:40 PM", "provider": "AIG", "status": 5, "fareClass": "Q", "operFareClass": "Q", "FBC": "QRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "987124611", "insuTransID": "54b6952c-a308-420f-b7c4-1c08c461fff1", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681618850007780000002e53#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-03T13:23:27"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/3/2025 1:22:40 PM", "provider": "AIG", "status": 0, "fareClass": "Q", "operFareClass": "Q", "FBC": "QRB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "987124611", "insuTransID": "54b6952c-a308-420f-b7c4-1c08c461fff1", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681618850007780000002e53#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-03T13:23:27"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/23/2025 2:54:12 PM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "6QGYQ-MHNYK-INS/77fc5ab6-521e-4ff5-96dc-0df864ea39d3", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68308bb3000778000001279d#267769561#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-23T14:54:11"}]}], "payments": [{"paymentID": *********, "paxID": 267769583, "method": "IPAY", "status": "1", "paidDate": "2025-05-03T13:23:32", "cardNum": "************3210", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1154.63, "baseCurr": "QAR", "baseAmt": 1154.63, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "249411", "reference": "22811212", "externalReference": "22811212", "tranId": "21184977", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21184977}, {"paymentID": *********, "paxID": 269943848, "method": "IPAY", "status": "1", "paidDate": "2025-05-23T14:54:17", "cardNum": "************3210", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 608.15, "baseCurr": "QAR", "baseAmt": 608.15, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "982856", "reference": "23207854", "externalReference": "23207854", "tranId": "21588901", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21588901}], "OAFlights": null, "physicalFlights": [{"key": "16087259:181009:2025-05-23T10:55:00 AM", "LFID": 16087259, "PFID": 181009, "org": "DOH", "dest": "DXB", "depDate": "2025-05-23T10:55:00", "depTime": "2025-05-23T10:55:00", "arrTime": "2025-05-23T13:10:00", "carrier": "FZ", "flightNum": "004", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "004", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087258:181010:2025-05-24T08:45:00 AM", "LFID": 16087258, "PFID": 181010, "org": "DXB", "dest": "DOH", "depDate": "2025-05-24T08:45:00", "depTime": "2025-05-24T08:45:00", "arrTime": "2025-05-24T08:55:00", "carrier": "FZ", "flightNum": "001", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "001", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "AC", "flightChangeTime": "5/16/2025 7:05:17 AM"}, {"key": "16087260:181011:2025-05-24T05:20:00 PM", "LFID": 16087260, "PFID": 181011, "org": "DXB", "dest": "DOH", "depDate": "2025-05-24T17:20:00", "depTime": "2025-05-24T17:20:00", "arrTime": "2025-05-24T17:30:00", "carrier": "FZ", "flightNum": "009", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "009", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:35:50 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-03T13:23:27", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-03T13:23:27"}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1330463388, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463388:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1330463385, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463385:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1330463386, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463386:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1330463387, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463387:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 330, "curr": "QAR", "originalAmt": 330, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-03T13:23:27", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 330, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1330465583, "codeType": "PMNT", "amt": 33.63, "curr": "QAR", "originalAmt": 33.63, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-03T13:23:36", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330465583:*********", "paymentID": *********, "amt": 33.63, "approveCode": 0}]}, {"chargeID": 1330463384, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1330463403, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181009", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-03T13:23:27", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-03T13:23:27"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330463399, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1359063840, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330463394, "paymentMap": [{"key": "1359063840:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1359063888, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330463397, "paymentMap": [{"key": "1359063888:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1359063887, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": -80, "curr": "QAR", "originalAmt": -80, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330463396, "paymentMap": [{"key": "1359063887:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1359063838, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330463398, "paymentMap": [{"key": "1359063838:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1330463398, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463398:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1330463394, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463394:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1330463396, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463396:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1330463397, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463397:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1330463399, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330463399:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1359063837, "codeType": "AIR", "amt": -355, "curr": "QAR", "originalAmt": -355, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-23T14:54:11", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1359063837:*********", "paymentID": *********, "amt": -355, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 355, "curr": "QAR", "originalAmt": 355, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-03T13:23:27", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 355, "approveCode": 0}]}, {"chargeID": 1359063890, "codeType": "PNLT", "amt": 395, "curr": "QAR", "originalAmt": 395, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-23T14:54:11", "desc": "Penalty AddedDueToModify FZ  009 DXB  - DOH  24-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063890:*********", "paymentID": *********, "amt": 395, "approveCode": 0}]}, {"chargeID": 1330463393, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1330463404, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-03T13:23:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181011"}]}, {"recNum": 3, "charges": [{"chargeID": 1359063942, "codeType": "INSU", "amt": 35.44, "curr": "QAR", "originalAmt": 35.44, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T14:54:12", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063942:*********", "paymentID": *********, "amt": 35.44, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-23T14:54:12"}, {"chargeID": 1359063893, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1359063891, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063893:*********", "paymentID": *********, "amt": 40, "approveCode": 0}, {"key": "1359063893:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1359063897, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1359063891, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063897:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1359063892, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1359063891, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:12", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063892:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1359063895, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1359063891, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063895:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1359063896, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1359063891, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063896:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1359063894, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1359063891, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359063894:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1359063891, "codeType": "AIR", "amt": 515, "curr": "QAR", "originalAmt": 515, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-23T14:54:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1359063891:*********", "paymentID": *********, "amt": 515, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1359066011, "codeType": "PMNT", "amt": 17.71, "curr": "QAR", "originalAmt": 17.71, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-23T14:54:22", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359066011:*********", "paymentID": *********, "amt": 17.71, "approveCode": 0}]}, {"chargeID": 1359063898, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1359063891, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:12", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1359063930, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T14:54:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181010", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}