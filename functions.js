/**
 * functions.js - Utility functions for extracting and processing policy information from PNR data
 * This file handles the extraction of Cover Genius policy IDs from FlyDubai PNR responses
 * and processes insurance data for report generation
 */

// Import required dependencies
const moment = require('moment');
const { statusCodeMap, channelCodeMap } = require('./constants');

/**
 * Extracts Cover Genius policy IDs from the PNR data
 * @param {Object} pnrData - The parsed PNR data object
 * @returns {Array} - Array of policy IDs
 */
function extractCoverGeniusPolicyIds(pnrData) {
    const policyIds = [];
    const seen = new Set(); // To prevent duplicate policy IDs

    // Check each paxSegment for insurance information
    if (pnrData && pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {
        pnrData.paxSegments.forEach(paxSegment => {
            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
                paxSegment.recordDetails.forEach(record => {
                    if (record.insuTransID &&
                        (!record.provider || record.provider !== 'AIG')) {
                        // Extract the policy ID from the insurance transaction ID
                        const parts = record.insuTransID.split('/');
                        if (parts.length > 0) {
                            const policyId = parts[0];
                            // Add to array if not already added
                            if (!seen.has(policyId)) {
                                policyIds.push(policyId);
                                seen.add(policyId);
                                console.log(`Found Cover Genius policy ID: ${policyId}`);
                            }
                        }
                    }
                });
            }
        });
    }

    return policyIds;
}

/**
 * Processes insurance records from PNR data and Cover Genius policy data
 * @param {Object} pnrData - The PNR data from FlyDubai API
 * @param {Object} policyData - The policy data from Cover Genius API
 * @param {string} policyId - The policy ID
 * @returns {Object} - Processed data for report generation
 */
function processInsuranceData(pnrData, policyData, policyId) {
    const insuranceRecords = [];

    // Extract policy dates from Cover Genius response
    let policyStartDate = null;
    let policyEndDate = null;

    // Handle different possible Cover Genius response structures
    if (policyData && policyData.data) {
        // Try different possible structures
        if (policyData.data.quotes && policyData.data.quotes.length > 0) {
            // Structure: data.quotes[0].policy_start_date
            const quote = policyData.data.quotes[0];
            if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
            if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
        } else if (policyData.data.policy) {
            // Structure: data.policy.start_date
            const policy = policyData.data.policy;
            if (policy.start_date) policyStartDate = moment(policy.start_date);
            if (policy.end_date) policyEndDate = moment(policy.end_date);
        }
    } else if (policyData && policyData.quotes && policyData.quotes.length > 0) {
        // Direct quotes structure
        const quote = policyData.quotes[0];
        if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
        if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
    }

    // Function to check if a date falls within policy period
    function isWithinPolicyPeriod(date) {
        if (!date || !policyStartDate || !policyEndDate) return "Unknown";
        const momentDate = moment(date);
        return momentDate.isSameOrAfter(policyStartDate) && momentDate.isSameOrBefore(policyEndDate);
    }

    // Create a mapping of passengers
    const passengersMap = {};
    if (pnrData.persons && Array.isArray(pnrData.persons)) {
        pnrData.persons.forEach(person => {
            passengersMap[person.paxID] = {
                fullName: `${person.title || ''} ${person.fName || ''} ${person.lName || ''}`.trim(),
                recNums: person.recNum || []
            };
        });
    }

    // Build segment information
    const segmentsMap = {};
    if (pnrData.segments && Array.isArray(pnrData.segments)) {
        pnrData.segments.forEach(segment => {
            segmentsMap[segment.segKey] = {
                origin: segment.org,
                destination: segment.dest,
                departureTime: segment.depTime,
                arrivalTime: segment.arrTime,
                flightNumber: `${segment.operCarrier}${segment.operFlightNum}`,
                persons: segment.persons || []
            };
        });
    }

    // Create a map to link record numbers to segments
    const recNumToSegmentMap = {};
    if (pnrData.segments && Array.isArray(pnrData.segments)) {
        pnrData.segments.forEach(segment => {
            if (segment.persons && Array.isArray(segment.persons)) {
                segment.persons.forEach(person => {
                    recNumToSegmentMap[person.recNum] = segment;
                });
            }
        });
    }

    // Build a mapping between recNum and person IDs
    const recNumToPersonMap = {};
    if (pnrData.persons && Array.isArray(pnrData.persons)) {
        pnrData.persons.forEach(person => {
            if (person.recNum && Array.isArray(person.recNum)) {
                person.recNum.forEach(recNum => {
                    recNumToPersonMap[recNum] = person.paxID;
                });
            }
        });
    }

    // Process paxSegments data to find ALL records with insurance
    console.log(`Processing ${pnrData.paxSegments ? pnrData.paxSegments.length : 0} paxSegments for insurance records...`);

    if (pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {
        pnrData.paxSegments.forEach((paxSegment, segmentIndex) => {
            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
                console.log(`  Segment ${segmentIndex + 1} (recNum: ${paxSegment.recNum}): Found ${paxSegment.recordDetails.length} record details`);
                paxSegment.recordDetails.forEach((record, recordIndex) => {
                    // Include ALL insurance records - check for any insurance-related fields
                    // This ensures we capture all insurance-related records in the PNR
                    const hasInsuranceData = record.insuTransID ||
                                           (record.insuTransID && record.insuTransID.trim() !== '');

                    if (hasInsuranceData) {
                        console.log(`    Record ${recordIndex + 1}: Found insurance data - insuTransID: ${record.insuTransID || 'N/A'}, provider: ${record.provider || 'N/A'}`);

                        // Find passenger ID from record number
                        const paxId = recNumToPersonMap[paxSegment.recNum];

                        // Build full insurance record with relevant information
                        const insuranceInfo = {
                            recordNumber: paxSegment.recNum,
                            insuTransID: record.insuTransID || "N/A",
                            insuConfNum: record.insuConfNum || null,
                            statusCode: record.status,
                            statusText: statusCodeMap[record.status] || "Unknown",
                            channelCode: record.channelID,
                            channelText: channelCodeMap[record.channelID] || "Unknown",
                            provider: record.provider || "Unknown",
                            insuPurchaseDate: record.insuPurchasedate ?
                                moment(record.insuPurchasedate, "M/D/YYYY h:mm:ss A").format("YYYY-MM-DD HH:mm:ss") :
                                null,
                            paxId: paxId,
                            passengerName: (paxId && passengersMap[paxId]?.fullName) || "Unknown",
                            bookDate: record.bookDate,
                            hasConfirmation: !!record.insuConfNum,
                            segmentInfo: null,
                            departureDate: null,
                            withinPolicyPeriod: "Unknown",
                            matchesCoverGeniusPolicyId: record.insuTransID ? record.insuTransID.includes(policyId) : false
                        };

                        // Get segment info from recNum
                        const segment = recNumToSegmentMap[paxSegment.recNum];
                        if (segment) {
                            // Format segment info in the expected structure
                            insuranceInfo.segmentInfo = {
                                origin: segment.org,
                                destination: segment.dest,
                                departureTime: segment.depTime,
                                arrivalTime: segment.arrTime,
                                flightNumber: `${segment.operCarrier}${segment.operFlightNum}`
                            };
                            insuranceInfo.departureDate = segment.depTime;
                        }

                        // Check if date is within policy period
                        insuranceInfo.withinPolicyPeriod = isWithinPolicyPeriod(insuranceInfo.departureDate);

                        insuranceRecords.push(insuranceInfo);
                    }
                });
            }
        });
    }

    console.log(`Total insurance records found: ${insuranceRecords.length}`);
    if (insuranceRecords.length > 0) {
        console.log('Insurance record providers:', [...new Set(insuranceRecords.map(r => r.provider))].join(', '));
    }

    return {
        insuranceRecords,
        policyStartDate: policyStartDate || moment(),
        policyEndDate: policyEndDate || moment()
    };
}

// Export all functions for use in other modules
module.exports = {
    extractCoverGeniusPolicyIds,
    processInsuranceData
};
