# Manual Policy Request Generation

This document explains how to use the manual policy request generation feature when Cover Genius API returns 404 errors.

## Overview

When the Cover Genius API returns a 404 error (policy not found), the system automatically generates a manual policy creation request that can be executed outside the application. This ensures that policies can still be issued even when the automated process fails.

## How It Works

1. **Automatic Detection**: The system detects 404 errors from Cover Genius API calls
2. **Data Extraction**: Passenger and contact information is extracted from PNR data
3. **Request Generation**: A complete curl command is generated with proper payload
4. **File Saving**: The request details are saved to a JSON file for reference

## Generated Files

When a 404 error occurs, the system creates a file named:
```
ManualPolicyRequest-{POLICY_ID}-{TIMESTAMP}.json
```

This file contains:
- Complete curl command ready for execution
- Request payload with passenger and contact details
- Step-by-step instructions
- Metadata about the extraction process

## Request Format

The generated request follows this structure:

```bash
curl --location 'https://api.xcover.com/x/partners/{PARTNER_ID}/bookings/{POLICY_ID}' \
--header 'Content-Type: application/json' \
--data-raw '{
  "quotes": [
    {
      "id": "{QUOTE_ID}",
      "insured": [
        {
          "first_name": "PASSENGER_FIRST_NAME",
          "last_name": "PASSENGER_LAST_NAME"
        }
      ]
    }
  ],
  "policyholder": {
    "first_name": "PRIMARY_PASSENGER_FIRST_NAME",
    "last_name": "PRIMARY_PASSENGER_LAST_NAME",
    "email": "<EMAIL>",
    "country": "DETECTED_COUNTRY_CODE"
  }
}'
```

## Data Mapping

### Passenger Information
- **Source**: PNR `persons` array
- **Mapping**:
  - `fName` → `first_name`
  - `lName` → `last_name`
- **Fallback**: If no passengers found, uses placeholder names

### Email Extraction
- **Source**: PNR `contactInfos` array (requires `DisplayContactInfos: true`)
- **Logic**:
  1. Identifies the primary passenger from `paxSegments` where `primaryPax: true`
  2. Searches for email contact info matching primary passenger's `paxID`
  3. Filters for `contactType: "Email"` and valid email format
- **Fallback**: "<EMAIL>" if no primary passenger email found

### Country Detection
- **Source**: First segment's origin airport code (`segments[0].org`)
- **Mapping**:
  - DXB, AUH, SHJ → AE (UAE)
  - DOH → QA (Qatar)
  - RUH, JED, DMA → SA (Saudi Arabia)
  - KWI → KW (Kuwait)
  - BAH → BH (Bahrain)
  - MCT → OM (Oman)
  - CAI, SPX → EG (Egypt)
  - LHR → GB (United Kingdom)
  - JFK, LAX → US (United States)
  - CDG → FR (France)
  - FRA → DE (Germany)
  - IST → TR (Turkey)
  - BOM, DEL → IN (India)
  - BKK → TH (Thailand)
  - SIN → SG (Singapore)
  - KHI, LHE, ISB → PK (Pakistan)
- **Default**: AE (UAE)

### Quote ID Extraction
- **Source**: Policy ID with `-INS` suffix removed
- **Example**: `ABC123-INS` → `ABC123`

## Manual Execution Steps

1. **Locate the generated file** in the `data/` directory
2. **Open the JSON file** and review the request details
3. **Verify the email address** in the policyholder section
4. **Verify passenger names** are correct
5. **Confirm country code** is appropriate
6. **Copy the curl command** from the `curlCommand` field
7. **Execute the command** in your terminal or API client
8. **Save the response** for record keeping

## Example Usage

### CLI Mode
```bash
node main.js ABC123
```

If a 404 error occurs, you'll see:
```
❌ Cover Genius API call failed for policy ABC123-INS: 404 Not Found
📋 Generating manual policy creation request for ABC123-INS...
✓ Manual policy request generated successfully
📄 Request saved to: ManualPolicyRequest-ABC123-INS-*.json
📋 Curl command ready for manual execution
```

### API Mode
When using the API, 404 errors return additional fields:
```json
{
  "error": "Cover Genius API failed: 404 Not Found",
  "errorType": "COVER_GENIUS_404_ERROR",
  "manualPolicyRequest": {
    "curlCommand": "curl --location '...'",
    "requestDetails": { ... },
    "instructions": [ ... ]
  }
}
```

## Testing

Run the test script to see how the feature works:
```bash
node test-manual-policy.js
```

This will generate sample requests and demonstrate the functionality.

## Configuration

### Environment Variables
- `COVER_GENIUS_PARTNER_ID`: Your Cover Genius partner ID
- `COVER_GENIUS_API_BASE_URL`: Base URL for Cover Genius API (defaults to test environment)

### PNR Retrieval Settings
- `DisplayContactInfos: true` is required to extract email addresses from PNR data

### File Storage
- Generated files are saved to the `data/` directory
- Files are automatically created if the directory doesn't exist

## Troubleshooting

### Common Issues

1. **No contact information found**
   - Ensure `DisplayContactInfos: true` is set in PNR retrieval
   - Check if PNR has contact information in `contactInfos` array
   - System will use placeholder email if none found

2. **Incorrect country detection**
   - Verify first segment's origin airport code
   - Update airport-to-country mapping if needed
   - Manually update country code in generated request

3. **File save errors**
   - Ensure `data/` directory exists and is writable
   - Check disk space and permissions

### Error Messages

- `Error generating manual policy request`: Check PNR data structure
- `Failed to save manual request`: Check file system permissions
- `Failed to generate manual request`: Review error logs for details

## Security Notes

- Generated files contain passenger and contact information - handle securely
- Email addresses are extracted from PNR data when available
- Review all data before executing manual requests
- Keep generated files for audit purposes

## Support

For issues or questions about manual policy request generation:
1. Check the generated JSON file for detailed information
2. Review the console logs for error messages
3. Verify PNR data structure and content
4. Test with the provided test script
