{"seriesNum": "299", "PNR": "CYU39G", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82147667", "bookDate": "2025-04-20T08:02:23", "modifyDate": "2025-05-27T19:12:06", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "107c694n2b64446559aeb4n4vc02j9ud18cb643102fe", "securityGUID": "107c694n2b64446559aeb4n4vc02j9ud18cb643102fe", "lastLoadGUID": "7c9e7c98-d8cc-47b0-8df0-0d9a98633204", "isAsyncPNR": false, "MasterPNR": "CYU39G", "segments": [{"segKey": "17095610:17095610:6/4/2025 8:50:00 PM", "LFID": 17095610, "depDate": "2025-06-04T00:00:00", "flightGroupId": "17095610", "org": "DOH", "dest": "ISB", "depTime": "2025-06-04T20:50:00", "depTimeGMT": "2025-06-04T17:50:00", "arrTime": "2025-06-05T04:50:00", "operCarrier": "FZ", "operFlightNum": "006/353", "mrktCarrier": "FZ ", "mrktFlightNum": "006/353", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181013, "depDate": "2025-06-04T20:50:00", "legKey": "17095610:181013:6/4/2025 8:50:00 PM", "customerKey": "FADE5F7D8AC1950ACB01DCDD57DC2CBED04A148CD546A620CD6D57DC99AEF165"}, {"PFID": 187806, "depDate": "2025-06-05T00:40:00", "legKey": "17095610:187806:6/5/2025 12:40:00 AM", "customerKey": "6D99D21CFD3F8822BC3A8EA8FC1EEB68EB5C798EC93AE1AB4E6EEEEF359C71B0"}], "active": true, "changeType": "TK"}, {"segKey": "17096883:17096883:6/14/2025 5:50:00 AM", "LFID": 17096883, "depDate": "2025-06-14T00:00:00", "flightGroupId": "17096883", "org": "ISB", "dest": "DOH", "depTime": "2025-06-14T05:50:00", "depTimeGMT": "2025-06-14T00:50:00", "arrTime": "2025-06-14T09:55:00", "operCarrier": "FZ", "operFlightNum": "354/003", "mrktCarrier": "FZ ", "mrktFlightNum": "354/003", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 187788, "depDate": "2025-06-14T05:50:00", "legKey": "17096883:187788:6/14/2025 5:50:00 AM", "customerKey": "201B348628B00E60CAA09912C567E8CA8D26046B4CCF87BD7491F3F52E6D5B35"}, {"PFID": 180997, "depDate": "2025-06-14T09:35:00", "legKey": "17096883:180997:6/14/2025 9:35:00 AM", "customerKey": "B7BED976476E4C0D333D71060E89E05F45B6793F5D67D7A58780FD9EA4F61B65"}], "active": true}, {"segKey": "17096883:17096883:6/9/2025 5:50:00 AM", "LFID": 17096883, "depDate": "2025-06-09T00:00:00", "flightGroupId": "17096883", "org": "ISB", "dest": "DOH", "depTime": "2025-06-09T05:50:00", "depTimeGMT": "2025-06-09T00:50:00", "arrTime": "2025-06-09T09:55:00", "operCarrier": "FZ", "operFlightNum": "354/003", "mrktCarrier": "FZ ", "mrktFlightNum": "354/003", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 187788, "depDate": "2025-06-09T05:50:00", "legKey": "17096883:187788:6/9/2025 5:50:00 AM", "customerKey": "1CED35EDD9705EA072FB495125A15B9962B40C398A10D8B1B6287A5A4D97228A"}, {"PFID": 180997, "depDate": "2025-06-09T09:35:00", "legKey": "17096883:180997:6/9/2025 9:35:00 AM", "customerKey": "3D906A55C4D35F9D88E76B2DC14080C5B14E14D965EBA5C27FF9356920A9376F"}], "active": true, "changeType": "AC"}, {"segKey": "17095610:17095610:5/29/2025 8:50:00 PM", "LFID": 17095610, "depDate": "2025-05-29T00:00:00", "flightGroupId": "17095610", "org": "DOH", "dest": "ISB", "depTime": "2025-05-29T20:50:00", "depTimeGMT": "2025-05-29T17:50:00", "arrTime": "2025-05-30T04:50:00", "operCarrier": "FZ", "operFlightNum": "006/353", "mrktCarrier": "FZ ", "mrktFlightNum": "006/353", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181013, "depDate": "2025-05-29T20:50:00", "legKey": "17095610:181013:5/29/2025 8:50:00 PM", "customerKey": "D4D387C3F3193697704EBEF007C91934E089E4819744BEAB2DFB59A6F47BD653"}, {"PFID": 187806, "depDate": "2025-05-30T00:40:00", "legKey": "17095610:187806:5/30/2025 12:40:00 AM", "customerKey": "D5BC9D7FF0705BA652E6378635EEA9F7477275307C85F061F4212198E849944D"}], "active": true}], "persons": [{"paxID": 266359364, "fName": "AHMAD", "lName": "HASSAN TARIQ", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1998-06-02T00:00:00", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "4/20/2025 7:57:39 AM", "provider": "AIG", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "986600267", "insuTransID": "2bf837dc-ba65-4438-87c0-4d326f24b46c", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6804a190000777000000b7f2#1#1#WEB#VAYANT#CREATE", "fareTypeID": 11, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-20T08:02:23"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "4/29/2025 11:58:22 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "986600267", "insuTransID": "45026555-2a23-41c8-beac-032f8ae085e6", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6810beca0007770000006bf9#266359364#2#WEB#OneSearch#CHANGE", "fareTypeID": 11, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-20T08:02:23"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "M", "status": 5, "fareClass": "M", "operFareClass": "M", "FBC": "MRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "ASPVW-MVG6B-INS/49e285d1-2152-4e25-ad77-391493bce09d", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68360b27000778000000649c#266359364#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-29T12:01:21"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRL7QA5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "ASPVW-MVG6B-INS/49e285d1-2152-4e25-ad77-391493bce09d", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68360b27000778000000649c#266359364#2#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T18:59:10"}]}], "payments": [{"paymentID": 206330846, "paxID": 266359736, "method": "MSCD", "status": "2", "paidDate": "2025-04-20T08:02:55", "cardNum": "************7979", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 2296.9, "baseCurr": "QAR", "baseAmt": 2296.9, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "AHMAD HASSAN TARIQ", "reference": "22530460", "externalReference": "22530460", "tranId": "20903228", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 20903228}, {"paymentID": *********, "paxID": 266359743, "method": "MSCD", "status": "1", "paidDate": "2025-04-20T08:06:15", "cardNum": "************7979", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 2296.9, "baseCurr": "QAR", "baseAmt": 2296.9, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "AHMAD HASSAN TARIQ", "authCode": "049821", "reference": "22530503", "externalReference": "22530503", "tranId": "20903228", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 20903228}, {"paymentID": *********, "paxID": 267351714, "method": "MSCD", "status": "1", "paidDate": "2025-04-29T12:02:04", "cardNum": "************7979", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1093.86, "baseCurr": "QAR", "baseAmt": 1093.86, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "AHMAD HASSAN TARIQ", "authCode": "660897", "reference": "22722600", "externalReference": "22722600", "tranId": "21101654", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21101654}, {"paymentID": *********, "paxID": 267352173, "method": "MSCD", "status": "1", "paidDate": "2025-04-29T12:05:59", "cardNum": "************7979", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 72.1, "baseCurr": "QAR", "baseAmt": 72.1, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "AHMAD HASSAN TARIQ", "authCode": "607146", "reference": "22722855", "externalReference": "22722855", "tranId": "21101798", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21101798}, {"paymentID": *********, "paxID": 270367599, "method": "MSCD", "status": "1", "paidDate": "2025-05-27T19:12:00", "cardNum": "************7979", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 569.52, "baseCurr": "QAR", "baseAmt": 569.52, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "193243", "reference": "23288146", "externalReference": "23288146", "tranId": "21669490", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21669490}], "OAFlights": null, "physicalFlights": [{"key": "17095610:181013:2025-05-29T08:50:00 PM", "LFID": 17095610, "PFID": 181013, "org": "DOH", "dest": "DXB", "depDate": "2025-05-29T20:50:00", "depTime": "2025-05-29T20:50:00", "arrTime": "2025-05-29T23:05:00", "carrier": "FZ", "flightNum": "006", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "006", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "17095610:187806:2025-05-30T12:40:00 AM", "LFID": 17095610, "PFID": 187806, "org": "DXB", "dest": "ISB", "depDate": "2025-05-30T00:40:00", "depTime": "2025-05-30T00:40:00", "arrTime": "2025-05-30T04:50:00", "carrier": "FZ", "flightNum": "353", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "353", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ISB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Islamabad", "isActive": false}, {"key": "17095610:181013:2025-06-04T08:50:00 PM", "LFID": 17095610, "PFID": 181013, "org": "DOH", "dest": "DXB", "depDate": "2025-06-04T20:50:00", "depTime": "2025-06-04T20:50:00", "arrTime": "2025-06-04T22:55:00", "carrier": "FZ", "flightNum": "006", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "006", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 3900, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "5/28/2025 5:47:05 AM"}, {"key": "17095610:187806:2025-06-05T12:40:00 AM", "LFID": 17095610, "PFID": 187806, "org": "DXB", "dest": "ISB", "depDate": "2025-06-05T00:40:00", "depTime": "2025-06-05T00:40:00", "arrTime": "2025-06-05T04:50:00", "carrier": "FZ", "flightNum": "353", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "353", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ISB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Islamabad", "isActive": true, "changeType": "TK", "flightChangeTime": "5/28/2025 5:47:05 AM"}, {"key": "17096883:187788:2025-06-09T05:50:00 AM", "LFID": 17096883, "PFID": 187788, "org": "ISB", "dest": "DXB", "depDate": "2025-06-09T05:50:00", "depTime": "2025-06-09T05:50:00", "arrTime": "2025-06-09T08:10:00", "carrier": "FZ", "flightNum": "354", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "354", "flightStatus": "OPEN", "originMetroGroup": "ISB", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Islamabad", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/27/2025 11:35:37 AM"}, {"key": "17096883:180997:2025-06-09T09:35:00 AM", "LFID": 17096883, "PFID": 180997, "org": "DXB", "dest": "DOH", "depDate": "2025-06-09T09:35:00", "depTime": "2025-06-09T09:35:00", "arrTime": "2025-06-09T09:55:00", "carrier": "FZ", "flightNum": "003", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "003", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true, "changeType": "AC", "flightChangeTime": "5/27/2025 11:35:37 AM"}, {"key": "17096883:187788:2025-06-14T05:50:00 AM", "LFID": 17096883, "PFID": 187788, "org": "ISB", "dest": "DXB", "depDate": "2025-06-14T05:50:00", "depTime": "2025-06-14T05:50:00", "arrTime": "2025-06-14T08:10:00", "carrier": "FZ", "flightNum": "354", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "354", "flightStatus": "OPEN", "originMetroGroup": "ISB", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Islamabad", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "17096883:180997:2025-06-14T09:35:00 AM", "LFID": 17096883, "PFID": 180997, "org": "DXB", "dest": "DOH", "depDate": "2025-06-14T09:35:00", "depTime": "2025-06-14T09:35:00", "arrTime": "2025-06-14T09:55:00", "carrier": "FZ", "flightNum": "003", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "003", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1312508471, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-20T08:02:23", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508471:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-04-20T08:02:22"}, {"chargeID": 1325218571, "codeType": "INSU", "amt": -23, "curr": "QAR", "originalAmt": -23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T12:01:22", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508471, "paymentMap": [{"key": "1325218571:*********", "paymentID": *********, "amt": -23, "approveCode": 0}]}, {"chargeID": 1312508320, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1312508319, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508320:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1312508322, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1312508319, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508322:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1312508364, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1312508319, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508364:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1312508366, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1312508319, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508366:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1312508365, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1312508319, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508365:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1312508321, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1312508319, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508321:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1312508323, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1312508319, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508323:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325218889, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1312508319, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:21", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508321, "paymentMap": [{"key": "1325218889:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1325218890, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1312508319, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:21", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508323, "paymentMap": [{"key": "1325218890:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1325218883, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1312508319, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:21", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508322, "paymentMap": [{"key": "1325218883:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1325218888, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1312508319, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:21", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508365, "paymentMap": [{"key": "1325218888:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1325218886, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1312508319, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:21", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508366, "paymentMap": [{"key": "1325218886:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1325218885, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1312508319, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508364, "paymentMap": [{"key": "1325218885:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1325218882, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1312508319, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508320, "paymentMap": [{"key": "1325218882:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1312508319, "codeType": "AIR", "amt": 1010, "curr": "QAR", "originalAmt": 1010, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-20T08:02:23", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508319:*********", "paymentID": *********, "amt": 1010, "approveCode": 0}]}, {"chargeID": 1325218887, "codeType": "AIR", "amt": -1010, "curr": "QAR", "originalAmt": -1010, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-29T12:01:21", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508319, "paymentMap": [{"key": "1325218887:*********", "paymentID": *********, "amt": -1010, "approveCode": 0}]}, {"chargeID": 1312518109, "codeType": "PMNT", "amt": 66.9, "curr": "QAR", "originalAmt": 66.9, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-20T08:06:25", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312518109:*********", "paymentID": *********, "amt": 66.9, "approveCode": 0}]}, {"chargeID": 1325218862, "codeType": "PNLT", "amt": 1050, "curr": "QAR", "originalAmt": 1050, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-29T12:01:22", "desc": "Penalty AddedDueToModify FZ  006 DOH  - DXB  04-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218862:*********", "paymentID": *********, "amt": 1050, "approveCode": 0}]}, {"chargeID": 1312508375, "codeType": "BAGL", "amt": 22, "curr": "QAR", "originalAmt": 22, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "BAGL", "comment": "FLXID:GCC-AE DOH-SPX/PK/CMB/KBL:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508375:*********", "paymentID": *********, "amt": 22, "approveCode": 0}]}, {"chargeID": 1325218884, "codeType": "BAGL", "amt": -22, "curr": "QAR", "originalAmt": -22, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "BAGL", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508375, "paymentMap": [{"key": "1325218884:*********", "paymentID": *********, "amt": -22, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-20T08:02:23", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-20T08:02:22"}, {"chargeID": **********, "codeType": "INSU", "amt": -23, "curr": "QAR", "originalAmt": -23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T12:01:22", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -23, "approveCode": 0}], "parameter2Name": "PROVIDER", "parameter2Value": "AIG"}, {"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T12:01:22", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "parameter2Name": "PROVIDER", "parameter2Value": "AIG"}, {"chargeID": **********, "codeType": "TAX", "taxID": 7364, "taxCode": "RG", "taxChargeID": **********, "amt": -170, "curr": "QAR", "originalAmt": -170, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Federal Excise Duty", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -170, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11107, "taxCode": "YD", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Infrastructure Development Charges", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508372, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1364746667, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508371, "paymentMap": [{"key": "1364746667:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1364746658, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508368, "paymentMap": [{"key": "1364746658:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1364746666, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508369, "paymentMap": [{"key": "1364746666:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1364746664, "codeType": "TAX", "taxID": 11066, "taxCode": "SP", "taxChargeID": **********, "amt": -30, "curr": "QAR", "originalAmt": -30, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Embarkation fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508374, "paymentMap": [{"key": "1364746664:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1364746661, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508370, "paymentMap": [{"key": "1364746661:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1312508369, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508369:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1312508372, "codeType": "TAX", "taxID": 11107, "taxCode": "YD", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Infrastructure Development Charges", "comment": "Infrastructure Development Charges", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508372:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1312508374, "codeType": "TAX", "taxID": 11066, "taxCode": "SP", "taxChargeID": **********, "amt": 30, "curr": "QAR", "originalAmt": 30, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Embarkation fee", "comment": "Embarkation fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508374:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1312508371, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508371:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1312508370, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508370:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1312508368, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508368:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 7364, "taxCode": "RG", "taxChargeID": **********, "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "Federal Excise Duty", "comment": "Federal Excise Duty", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 170, "approveCode": 0}]}, {"chargeID": 1364746662, "codeType": "AIR", "amt": -540, "curr": "QAR", "originalAmt": -540, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-27T18:59:10", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1364746662:*********", "paymentID": *********, "amt": -540, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 540, "curr": "QAR", "originalAmt": 540, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-20T08:02:23", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 540, "approveCode": 0}]}, {"chargeID": 1364746670, "codeType": "PNLT", "amt": 580, "curr": "QAR", "originalAmt": 580, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-27T18:59:11", "desc": "Penalty AddedDueToModify FZ  354 ISB  - DXB  14-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746670:*********", "paymentID": *********, "amt": 580, "approveCode": 0}]}, {"chargeID": 1312508376, "codeType": "BAGL", "amt": 22, "curr": "QAR", "originalAmt": 22, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T08:02:23", "desc": "BAGL", "comment": "FLXID:GCC-AE DOH-SPX/PK/CMB/KBL:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312508376:*********", "paymentID": *********, "amt": 22, "approveCode": 0}]}, {"chargeID": 1364746659, "codeType": "BAGL", "amt": -22, "curr": "QAR", "originalAmt": -22, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "BAGL", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312508376, "paymentMap": [{"key": "1364746659:*********", "paymentID": *********, "amt": -22, "approveCode": 0}]}, {"chargeID": 1364746663, "codeType": "MLIN", "amt": -35, "curr": "QAR", "originalAmt": -35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-27T18:59:11", "desc": "MLIN", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325230009, "paymentMap": [{"key": "1364746663:*********", "paymentID": *********, "amt": -35, "approveCode": 0}], "PFID": "187788"}, {"chargeID": 1325230009, "codeType": "MLIN", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T12:05:26", "desc": "MLIN", "comment": "FLXID:35 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325230009:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "187788"}, {"chargeID": 1325230010, "codeType": "MLIN", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T12:05:26", "desc": "MLIN", "comment": "FLXID:35 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325230010:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "180997"}, {"chargeID": 1364746668, "codeType": "MLIN", "amt": -35, "curr": "QAR", "originalAmt": -35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-27T18:59:11", "desc": "MLIN", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325230010, "paymentMap": [{"key": "1364746668:*********", "paymentID": *********, "amt": -35, "approveCode": 0}], "PFID": "180997"}]}, {"recNum": 3, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T12:01:22", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-29T12:01:22"}, {"chargeID": **********, "codeType": "INSU", "amt": 22.46, "curr": "QAR", "originalAmt": 22.46, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-27T18:59:11", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 22.46, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}"}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325218868, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218868:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1325218864, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218864:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1325218866, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218866:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325218865, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218865:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1325218910, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218910:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1325218909, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325218909:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1020, "curr": "QAR", "originalAmt": 1020, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-29T12:01:22", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 28, "approveCode": 0}, {"key": "**********:*********", "paymentID": *********, "amt": 992, "approveCode": 0}]}, {"chargeID": 1364761365, "codeType": "PMNT", "amt": 16.59, "curr": "QAR", "originalAmt": 16.59, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-27T19:12:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364761365:*********", "paymentID": *********, "amt": 16.59, "approveCode": 0}]}, {"chargeID": 1325232334, "codeType": "PMNT", "amt": 2.1, "curr": "QAR", "originalAmt": 2.1, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-29T12:06:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325232334:*********", "paymentID": *********, "amt": 2.1, "approveCode": 0}]}, {"chargeID": 1325226740, "codeType": "PMNT", "amt": 31.86, "curr": "QAR", "originalAmt": 31.86, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-29T12:02:10", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325226740:*********", "paymentID": *********, "amt": 31.86, "approveCode": 0}]}, {"chargeID": 1325218911, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1325218918, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181013"}, {"chargeID": 1325218919, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T12:01:22", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187806"}]}, {"recNum": 4, "charges": [{"chargeID": 1364746785, "codeType": "INSU", "amt": 22.47, "curr": "QAR", "originalAmt": 22.47, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-27T18:59:12", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746785:*********", "paymentID": *********, "amt": 22.47, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-27T18:59:12"}, {"chargeID": 1364746695, "codeType": "TAX", "taxID": 11066, "taxCode": "SP", "taxChargeID": 1364746588, "amt": 30, "curr": "QAR", "originalAmt": 30, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Embarkation fee", "comment": "Embarkation fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746695:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364746692, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364746588, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746692:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1364746694, "codeType": "TAX", "taxID": 7364, "taxCode": "RG", "taxChargeID": 1364746588, "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Federal Excise Duty", "comment": "Federal Excise Duty", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746694:*********", "paymentID": *********, "amt": 152, "approveCode": 0}, {"key": "1364746694:*********", "paymentID": *********, "amt": 18, "approveCode": 0}]}, {"chargeID": 1364746691, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364746588, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746691:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1364746690, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364746588, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746690:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364746689, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1364746588, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746689:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1364746693, "codeType": "TAX", "taxID": 11107, "taxCode": "YD", "taxChargeID": 1364746588, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Infrastructure Development Charges", "comment": "Infrastructure Development Charges", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746693:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1364746588, "codeType": "AIR", "amt": 560, "curr": "QAR", "originalAmt": 560, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-27T18:59:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364746588:*********", "paymentID": *********, "amt": 508, "approveCode": 0}, {"key": "1364746588:*********", "paymentID": *********, "amt": 52, "approveCode": 0}]}, {"chargeID": 1364746696, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1364746588, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364746703, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187788"}, {"chargeID": 1364746702, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:59:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180997"}]}], "parentPNRs": [], "childPNRs": []}