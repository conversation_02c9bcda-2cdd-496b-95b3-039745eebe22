{"seriesNum": "299", "PNR": "S7NPG0", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82304303", "bookDate": "2025-04-25T13:10:51", "modifyDate": "2025-05-20T14:24:39", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "4fd8417461m82tu187a1641fl7ffbfc3ba68aae07d14", "securityGUID": "4fd8417461m82tu187a1641fl7ffbfc3ba68aae07d14", "lastLoadGUID": "a01095e8-a1c1-4245-833c-db5aa8153bc7", "isAsyncPNR": false, "MasterPNR": "S7NPG0", "segments": [{"segKey": "17114913:17114913:6/21/2025 2:10:00 PM", "LFID": 17114913, "depDate": "2025-06-21T00:00:00", "flightGroupId": "17114913", "org": "SPX", "dest": "DXB", "depTime": "2025-06-21T14:10:00", "depTimeGMT": "2025-06-21T11:10:00", "arrTime": "2025-06-21T18:45:00", "operCarrier": "FZ", "operFlightNum": "194", "mrktCarrier": "FZ", "mrktFlightNum": "194", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 187959, "depDate": "2025-06-21T14:10:00", "legKey": "17114913:187959:6/21/2025 2:10:00 PM", "customerKey": "04E9C2E03E37E8B1159E187621173216D93D826D9F2443A2D1F3471C275C5277"}], "active": true}, {"segKey": "16882760:16882760:4/28/2025 10:20:00 AM", "LFID": 16882760, "depDate": "2025-04-28T00:00:00", "flightGroupId": "16882760", "org": "DXB", "dest": "SPX", "depTime": "2025-04-28T10:20:00", "depTimeGMT": "2025-04-28T06:20:00", "arrTime": "2025-04-28T13:05:00", "operCarrier": "FZ", "operFlightNum": "193", "mrktCarrier": "FZ ", "mrktFlightNum": "193", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 186428, "depDate": "2025-04-28T10:20:00", "legKey": "16882760:186428:4/28/2025 10:20:00 AM", "customerKey": "570B0BD27547BEE7E0E88264E843A7DCBF09A17BDD616EF1F63B6775C2CEC4CD"}], "active": true}, {"segKey": "17109645:17109645:5/22/2025 2:10:00 PM", "LFID": 17109645, "depDate": "2025-05-22T00:00:00", "flightGroupId": "17109645", "org": "SPX", "dest": "DXB", "depTime": "2025-05-22T14:10:00", "depTimeGMT": "2025-05-22T11:10:00", "arrTime": "2025-05-22T18:45:00", "operCarrier": "FZ", "operFlightNum": "194", "mrktCarrier": "FZ ", "mrktFlightNum": "194", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 187852, "depDate": "2025-05-22T14:10:00", "legKey": "17109645:187852:5/22/2025 2:10:00 PM", "customerKey": "99F0D45F1B27145106123FFB6E12C56784529BF997804E8CFAF3F573F3C533B0"}], "active": true}], "persons": [{"paxID": 266956374, "fName": "HALA", "lName": "ABOUSHADY", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "4/25/2025 1:14:48 PM", "provider": "AIG", "status": 5, "fareClass": "H", "operFareClass": "H", "FBC": "HRX8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "986785920", "insuTransID": "320b98a7-b88e-47b7-9062-cc1b8f265441", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680b830e0007780000003db3#1#1#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-25T13:10:51"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "H", "insuPurchasedate": "4/25/2025 1:14:48 PM", "provider": "AIG", "status": 0, "fareClass": "H", "operFareClass": "H", "FBC": "HRX8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "986785920", "insuTransID": "320b98a7-b88e-47b7-9062-cc1b8f265441", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680b830e0007780000003db3#1#2#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-04-25T13:10:51"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "mahm<PERSON>.gouda", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/20/2025 2:17:17 PM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "V7X27-HAWXH-INS/98cb5744-65b0-4f83-8ef1-2fe93d1a2e63", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682c8d1000077700000023e3#266956374#2#ENT#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-20T14:17:11"}]}], "payments": [{"paymentID": 206946143, "paxID": 266956374, "method": "VCHR", "status": "1", "paidDate": "2025-04-25T13:31:32", "voucherNum": 3251365, "paidCurr": "AED", "paidAmt": -115.55, "baseCurr": "AED", "baseAmt": -115.55, "userID": "WEB2_LIVE", "channelID": 2, "voucherNumFull": "M3GXWL", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 266956478, "method": "TABY", "status": "1", "paidDate": "2025-04-25T13:11:46", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1546.65, "baseCurr": "AED", "baseAmt": 1546.65, "userID": "WEB2_LIVE", "channelID": 2, "authCode": "A5250568", "reference": "A5250568", "externalReference": "A5250568", "tranId": "21021735", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FDAE", "exchangeRate": "1", "resExternalPaymentID": 21021735}, {"paymentID": *********, "paxID": 269598091, "method": "VISA", "status": "1", "paidDate": "2025-05-20T14:24:34", "cardNum": "************2149", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 31.05, "baseCurr": "AED", "baseAmt": 31.05, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "430285", "reference": "23147822", "externalReference": "23147822", "tranId": "21525161", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21525161}, {"paymentID": *********, "paxID": 266956374, "method": "VCHR", "status": "1", "paidDate": "2025-05-20T14:17:11", "voucherNum": 3251365, "paidCurr": "AED", "paidAmt": 115.55, "baseCurr": "AED", "baseAmt": 115.55, "userID": "mahm<PERSON>.gouda", "channelID": 1, "voucherNumFull": "M3GXWL", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "16882760:186428:2025-04-28T10:20:00 AM", "LFID": 16882760, "PFID": 186428, "org": "DXB", "dest": "SPX", "depDate": "2025-04-28T10:20:00", "depTime": "2025-04-28T10:20:00", "arrTime": "2025-04-28T13:05:00", "carrier": "FZ", "flightNum": "193", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "193", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "SPX", "operatingCarrier": "FZ", "flightDuration": 13500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Sphinx International Airport", "isActive": false}, {"key": "17109645:187852:2025-05-22T02:10:00 PM", "LFID": 17109645, "PFID": 187852, "org": "SPX", "dest": "DXB", "depDate": "2025-05-22T14:10:00", "depTime": "2025-05-22T14:10:00", "arrTime": "2025-05-22T18:45:00", "carrier": "FZ", "flightNum": "194", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "194", "flightStatus": "CLOSED", "originMetroGroup": "SPX", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12900, "reaccomChangeAlert": false, "originName": "Sphinx International Airport", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "17114913:187959:2025-06-21T02:10:00 PM", "LFID": 17114913, "PFID": 187959, "org": "SPX", "dest": "DXB", "depDate": "2025-06-21T14:10:00", "depTime": "2025-06-21T14:10:00", "arrTime": "2025-06-21T18:45:00", "carrier": "FZ", "flightNum": "194", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "194", "flightStatus": "OPEN", "originMetroGroup": "SPX", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12900, "reaccomChangeAlert": false, "originName": "Sphinx International Airport", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1320202144, "codeType": "INSU", "amt": 36.23, "curr": "AED", "originalAmt": 36.23, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-04-25T13:31:33", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320202144:*********", "paymentID": *********, "amt": 36.23, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-04-25T13:31:33"}, {"chargeID": 1320186762, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1320186759, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186762:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1320186763, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1320186759, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186763:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1320186765, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1320186759, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186765:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1320186760, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1320186759, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186760:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1320186761, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1320186759, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186761:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1320186764, "codeType": "TAX", "taxID": 13279, "taxCode": "S4", "taxChargeID": 1320186759, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "International Advanced Passenger Information Fee", "comment": "International Advanced Passenger Information Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186764:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320186759, "codeType": "AIR", "amt": 315, "curr": "AED", "originalAmt": 315, "originalCurr": "AED", "status": 1, "billDate": "2025-04-25T13:10:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186759:*********", "paymentID": *********, "amt": 315, "approveCode": 0}]}, {"chargeID": 1320195650, "codeType": "PMNT", "amt": 73.65, "curr": "AED", "originalAmt": 73.65, "originalCurr": "AED", "status": 1, "billDate": "2025-04-25T13:11:51", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320195650:*********", "paymentID": *********, "amt": 73.65, "approveCode": 0}]}, {"chargeID": 1320186782, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::186428", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186782:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "186428"}, {"chargeID": 1320202329, "codeType": "XLGR", "amt": -188, "curr": "AED", "originalAmt": -188, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:31:34", "desc": "XLGR", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186782, "paymentMap": [{"key": "1320202329:206946143", "paymentID": 206946143, "amt": -115.55, "approveCode": 0}, {"key": "1320202329:*********", "paymentID": *********, "amt": -72.45, "approveCode": 0}], "PFID": "186428"}, {"chargeID": 1320202339, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-04-25T13:31:34", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::186428", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186428"}, {"chargeID": 1320186767, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1320186759, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1320186766, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1320186759, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1320186785, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186428"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 36.22, "curr": "AED", "originalAmt": 36.22, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-04-25T13:31:34", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 36.22, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-25T13:31:34"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13130, "taxCode": "JK", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Departure Fee", "comment": "Departure Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13210, "taxCode": "O9", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "CUTE/CUPPS fee", "comment": "CUTE/CUPPS fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320186774, "codeType": "TAX", "taxID": 13070, "taxCode": "O2", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Departure Service Fee", "comment": "Departure Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186774:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320186776, "codeType": "TAX", "taxID": 13071, "taxCode": "EQ", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Service Charge.", "comment": "Service Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186776:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320186771, "codeType": "TAX", "taxID": 13279, "taxCode": "S4", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "International Advanced Passenger Information Fee", "comment": "International Advanced Passenger Information Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186771:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320186772, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186772:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1320186777, "codeType": "TAX", "taxID": 13131, "taxCode": "QH", "taxChargeID": **********, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Embarkation Tax.", "comment": "Embarkation Tax.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186777:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1320186770, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320186770:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1354422051, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1354422050, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186770, "paymentMap": [{"key": "1354422051:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1354422053, "codeType": "TAX", "taxID": 13131, "taxCode": "QH", "taxChargeID": 1354422050, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Embarkation Tax.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186777, "paymentMap": [{"key": "1354422053:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1354422055, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1354422050, "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186772, "paymentMap": [{"key": "1354422055:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1354422056, "codeType": "TAX", "taxID": 13071, "taxCode": "EQ", "taxChargeID": 1354422050, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Service Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186776, "paymentMap": [{"key": "1354422056:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1354422058, "codeType": "TAX", "taxID": 13070, "taxCode": "O2", "taxChargeID": 1354422050, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Departure Service Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186774, "paymentMap": [{"key": "1354422058:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1354422060, "codeType": "TAX", "taxID": 13210, "taxCode": "O9", "taxChargeID": 1354422050, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "CUTE/CUPPS fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1354422060:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1354422061, "codeType": "TAX", "taxID": 13130, "taxCode": "JK", "taxChargeID": 1354422050, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Departure Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1354422061:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1354422062, "codeType": "TAX", "taxID": 13279, "taxCode": "S4", "taxChargeID": 1354422050, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "International Advanced Passenger Information Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186771, "paymentMap": [{"key": "1354422062:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 315, "curr": "AED", "originalAmt": 315, "originalCurr": "AED", "status": 0, "billDate": "2025-04-25T13:10:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 315, "approveCode": 0}]}, {"chargeID": 1354422050, "codeType": "AIR", "amt": -315, "curr": "AED", "originalAmt": -315, "originalCurr": "AED", "status": 0, "billDate": "2025-05-20T14:17:11", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1354422050:*********", "paymentID": *********, "amt": -315, "approveCode": 0}]}, {"chargeID": 1320186784, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::187852", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187852"}, {"chargeID": 1354422059, "codeType": "SPST", "taxChargeID": 1354422050, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "SPST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186784, "paymentMap": [], "PFID": "187852"}, {"chargeID": 1320186779, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1354422052, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1354422050, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186779, "paymentMap": []}, {"chargeID": 1320186778, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1354422054, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1354422050, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186778, "paymentMap": []}, {"chargeID": 1320186786, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:10:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187852"}, {"chargeID": 1354422057, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1354422050, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-20T14:17:12", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320186786, "paymentMap": [], "PFID": "187852"}]}, {"recNum": 3, "charges": [{"chargeID": 1354422079, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:09:28", "billDate": "2025-05-20T14:17:13", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422079:*********", "paymentID": *********, "amt": 30.15, "approveCode": 0}, {"key": "1354422079:*********", "paymentID": *********, "amt": 5.55, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-20T14:17:13"}, {"chargeID": 1354422065, "codeType": "TAX", "taxID": 13279, "taxCode": "S4", "taxChargeID": 1354422064, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:12", "billDate": "2025-05-20T14:17:12", "desc": "S4: International Advanced Passenger Information Fee", "comment": "S4: International Advanced Passenger Information Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422065:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1354422067, "codeType": "TAX", "taxID": 13130, "taxCode": "JK", "taxChargeID": 1354422064, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:12", "billDate": "2025-05-20T14:17:12", "desc": "JK: Departure <PERSON>", "comment": "JK: Departure <PERSON>", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422067:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1354422068, "codeType": "TAX", "taxID": 13070, "taxCode": "O2", "taxChargeID": 1354422064, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:12", "billDate": "2025-05-20T14:17:13", "desc": "O2: Departure Service Fee", "comment": "O2: Departure Service Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422068:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1354422070, "codeType": "TAX", "taxID": 13131, "taxCode": "QH", "taxChargeID": 1354422064, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "QH: Embarkation Tax.", "comment": "QH: Embarkation Tax.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422070:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1354422071, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1354422064, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422071:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1354422072, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1354422064, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422072:*********", "paymentID": *********, "amt": 150, "approveCode": 0}, {"key": "1354422072:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1354422073, "codeType": "TAX", "taxID": 13210, "taxCode": "O9", "taxChargeID": 1354422064, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "O9: CUTE/CUPPS fee", "comment": "O9: CUTE/CUPPS fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422073:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1354422074, "codeType": "TAX", "taxID": 13071, "taxCode": "EQ", "taxChargeID": 1354422064, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "EQ: Service Charge.", "comment": "EQ: Service Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422074:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1354422064, "codeType": "AIR", "amt": 365, "curr": "AED", "originalAmt": 365, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:12", "billDate": "2025-05-20T14:17:12", "desc": "FZ 194 SPX-DXB 21Jun2025 Sat 14:10 18:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422064:*********", "paymentID": *********, "amt": 365, "approveCode": 0}]}, {"chargeID": 1354433891, "codeType": "PMNT", "amt": 0.9, "curr": "AED", "originalAmt": 0.9, "originalCurr": "AED", "status": 1, "billDate": "2025-05-20T14:24:38", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354433891:*********", "paymentID": *********, "amt": 0.9, "approveCode": 0}]}, {"chargeID": 1354422078, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354422078:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1354422077, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1354422064, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1354422075, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1354422064, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1354422076, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1354422064, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-20T14:17:13", "billDate": "2025-05-20T14:17:13", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187959"}]}], "parentPNRs": [], "childPNRs": []}