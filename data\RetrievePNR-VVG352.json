{"seriesNum": "299", "PNR": "VVG352", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82459429", "bookDate": "2025-05-01T10:01:11", "modifyDate": "2025-05-25T02:08:46", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "ce4aae72asc605h4gbb6u449x460d0lcf8799b27809c", "securityGUID": "ce4aae72asc605h4gbb6u449x460d0lcf8799b27809c", "lastLoadGUID": "00f3c95a-c8fe-4493-b28e-00a3dcb5d93b", "isAsyncPNR": false, "MasterPNR": "VVG352", "segments": [{"segKey": "16087722:16087722:5/25/2025 7:35:00 AM", "LFID": 16087722, "depDate": "2025-05-25T00:00:00", "flightGroupId": "16087722", "org": "DXB", "dest": "NAP", "depTime": "2025-05-25T07:35:00", "depTimeGMT": "2025-05-25T03:35:00", "arrTime": "2025-05-25T12:00:00", "operCarrier": "FZ", "operFlightNum": "1681", "mrktCarrier": "FZ ", "mrktFlightNum": "1681", "persons": [{"recNum": 2, "status": 5}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181492, "depDate": "2025-05-25T07:35:00", "legKey": "16087722:181492:5/25/2025 7:35:00 AM", "customerKey": "C595309ED27289B571CE0295A6403677A00BEBC937D5985DD6F9D845BF5698DC"}], "active": true, "changeType": "TK"}, {"segKey": "16087722:16087722:5/23/2025 7:40:00 AM", "LFID": 16087722, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087722", "org": "DXB", "dest": "NAP", "depTime": "2025-05-23T07:40:00", "depTimeGMT": "2025-05-23T03:40:00", "arrTime": "2025-05-23T12:00:00", "operCarrier": "FZ", "operFlightNum": "1681", "mrktCarrier": "FZ", "mrktFlightNum": "1681", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181492, "depDate": "2025-05-23T07:40:00", "legKey": "16087722:181492:5/23/2025 7:40:00 AM", "customerKey": "D99E2CE814FA595A791D54C6DBF1FFEAC9ABC33FD47A8ECB9786507665929540"}], "active": true}], "persons": [{"paxID": 267560687, "fName": "FELICETTA", "lName": "GUERRIERO", "title": "MS", "PTCID": 1, "gender": "F", "FFNum": "556086296", "FFTier": "BLUE", "TierID": "3", "recNum": [2]}, {"paxID": 267560686, "fName": "ANGELO", "lName": "GUERRIERO", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1997-07-14T00:00:00", "nationality": "380", "FFNum": "568618400", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681344d9000777000000f98f#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-01T10:01:11"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/22/2025 7:33:19 PM", "provider": "<PERSON>", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "emergencyContactID": 269934172, "discloseEmergencyContact": 1, "insuTransID": "NL6VT-UR88T-INS/780480a7-6f6d-4b4d-be13-116611eea6dc", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681344d9000777000000f98f#2#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-01T10:01:11"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "sohail.rashed", "statusReasonID": 0, "markFareClass": "T", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 269722286, "discloseEmergencyContact": 1, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682b17b70007780000002818#267560686#1#ENT#SFQE#CHANGE", "fareTypeID": 23, "channelID": 1, "bookDate": "2025-05-19T11:38:03"}]}], "payments": [{"paymentID": *********, "paxID": 267560708, "method": "AMEX", "status": "1", "paidDate": "2025-05-01T10:01:22", "cardNum": "***********1001", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 3618.02, "baseCurr": "AED", "baseAmt": 3618.02, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "204418", "reference": "22766741", "externalReference": "22766741", "tranId": "21142801", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallamxallv100", "exchangeRate": "1", "resExternalPaymentID": 21142801}, {"paymentID": *********, "paxID": 267560705, "method": "MILE", "status": "1", "paidDate": "2025-05-01T10:01:18", "gateway": "EPS", "paidCurr": "PNT", "paidAmt": 2700, "baseCurr": "AED", "baseAmt": 77.36, "userID": "WEB2_LIVE", "channelID": 2, "FFNum": "568618400", "tierID": "3", "authCode": "23771277", "reference": "A5288003", "externalReference": "A5288003", "tranId": "21142801", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "34.901758", "resExternalPaymentID": 21142801}, {"paymentID": *********, "paxID": 269933913, "method": "IPAY", "status": "1", "paidDate": "2025-05-23T13:18:08", "cardNum": "************4428", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 230.41, "baseCurr": "AED", "baseAmt": 230.41, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "H13947", "reference": "23205222", "externalReference": "23205222", "tranId": "21587153", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21587153}, {"paymentID": *********, "paxID": 269457534, "method": "IPAY", "status": "1", "paidDate": "2025-05-19T11:39:14", "cardNum": "************0617", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 256.47, "baseCurr": "AED", "baseAmt": 256.47, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "B40408", "reference": "23124195", "externalReference": "23124195", "tranId": "21499169", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21499169}], "OAFlights": null, "physicalFlights": [{"key": "16087722:181492:2025-05-23T07:40:00 AM", "LFID": 16087722, "PFID": 181492, "org": "DXB", "dest": "NAP", "depDate": "2025-05-23T07:40:00", "depTime": "2025-05-23T07:40:00", "arrTime": "2025-05-23T12:00:00", "carrier": "FZ", "flightNum": "1681", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1681", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "NAP", "operatingCarrier": "FZ", "flightDuration": 22800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Naples", "isActive": false}, {"key": "16087722:181492:2025-05-25T07:35:00 AM", "LFID": 16087722, "PFID": 181492, "org": "DXB", "dest": "NAP", "depDate": "2025-05-25T07:35:00", "depTime": "2025-05-25T07:35:00", "arrTime": "2025-05-25T12:00:00", "carrier": "FZ", "flightNum": "1681", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1681", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "NAP", "operatingCarrier": "FZ", "flightDuration": 23100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Naples", "isActive": false, "changeType": "TK", "flightChangeTime": "3/13/2025 11:31:18 AM"}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1358041494, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-22T19:33:19", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358041494:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-22T19:33:19"}, {"chargeID": 1327867848, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1327867844, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867848:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1327867845, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1327867844, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867845:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327867869, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1327867844, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867869:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1327867870, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1327867844, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867870:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327867847, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1327867844, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867847:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1327867844, "codeType": "AIR", "amt": 1385, "curr": "AED", "originalAmt": 1385, "originalCurr": "AED", "status": 1, "billDate": "2025-05-01T10:01:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1327867844:*********", "paymentID": *********, "amt": 1385, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1358941536, "codeType": "PMNT", "amt": 6.71, "curr": "AED", "originalAmt": 6.71, "originalCurr": "AED", "status": 1, "billDate": "2025-05-23T13:18:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358941536:*********", "paymentID": *********, "amt": 6.71, "approveCode": 0}]}, {"chargeID": 1358041440, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-22T19:33:19", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181492", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1358041440:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "181492", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1358041391, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T19:33:19", "desc": "FRST", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867876, "paymentMap": [], "PFID": "181492"}, {"chargeID": 1327867876, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181492", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181492"}, {"chargeID": 1327867871, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1327867844, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1327867846, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1327867844, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1327867877, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181492", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1360627440, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-25T02:08:46", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181492", "ssrCommentId": "*********"}]}, {"recNum": 1, "charges": [{"chargeID": 1352555947, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352555945, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867840, "paymentMap": [{"key": "1352555947:*********", "paymentID": *********, "amt": -45, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-19T11:38:04"}, {"chargeID": 1352555948, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352555945, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867836, "paymentMap": [{"key": "1352555948:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": *********0, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352555945, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867839, "paymentMap": [{"key": "*********0:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": *********1, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352555945, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867841, "paymentMap": [{"key": "*********1:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": *********3, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352555945, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867838, "paymentMap": [{"key": "*********3:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1327867840, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1327867835, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867840:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1327867838, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1327867835, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867838:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1327867841, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1327867835, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867841:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1327867839, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1327867835, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867839:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1327867836, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1327867835, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867836:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352555945, "codeType": "AIR", "amt": -1385, "curr": "AED", "originalAmt": -1385, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T11:38:04", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867835, "paymentMap": [{"key": "1352555945:*********", "paymentID": *********, "amt": -77.36, "approveCode": 0}, {"key": "1352555945:*********", "paymentID": *********, "amt": -1307.64, "approveCode": 0}]}, {"chargeID": 1327867835, "codeType": "AIR", "amt": 1385, "curr": "AED", "originalAmt": 1385, "originalCurr": "AED", "status": 0, "billDate": "2025-05-01T10:01:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327867835:*********", "paymentID": *********, "amt": 1307.64, "approveCode": 0}, {"key": "1327867835:*********", "paymentID": *********, "amt": 77.36, "approveCode": 0}]}, {"chargeID": 1327871246, "codeType": "PMNT", "amt": 105.38, "curr": "AED", "originalAmt": 105.38, "originalCurr": "AED", "status": 0, "billDate": "2025-05-01T10:01:29", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1327871246:*********", "paymentID": *********, "amt": 105.38, "approveCode": 0}]}, {"chargeID": 1352555999, "codeType": "FRST", "taxChargeID": 1352555945, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867874, "paymentMap": [], "PFID": "181492"}, {"chargeID": 1327867874, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181492", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181492"}, {"chargeID": 1352555998, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352555945, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867842, "paymentMap": []}, {"chargeID": 1327867842, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1327867835, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352555946, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1352555945, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867837, "paymentMap": []}, {"chargeID": 1327867837, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1327867835, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********2, "codeType": "CHML", "taxChargeID": 1352555945, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T11:38:04", "desc": "CHML", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1327867873, "paymentMap": [], "PFID": "181492"}, {"chargeID": 1327867873, "codeType": "CHML", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-01T10:01:11", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181492"}]}, {"recNum": 3, "charges": [{"chargeID": *********5, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********4, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:04", "billDate": "2025-05-19T11:38:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********5:*********", "paymentID": *********, "amt": 45, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-19T11:38:05"}, {"chargeID": *********6, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": *********4, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********6:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********7, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********4, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********7:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********9, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": *********4, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********9:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352556010, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********4, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352556010:*********", "paymentID": *********, "amt": 91, "approveCode": 0}, {"key": "1352556010:*********", "paymentID": *********, "amt": 189, "approveCode": 0}]}, {"chargeID": *********4, "codeType": "AIR", "amt": 1574, "curr": "AED", "originalAmt": 1574, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:04", "billDate": "2025-05-19T11:38:04", "desc": "FZ 1681 DXB-NAP 23May2025 Fri 07:40 12:00\r\n", "reasonID": 2, "channelID": 1, "basePoints": 856, "tierPoints": 856, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "*********4:*********", "paymentID": *********, "amt": 77.36, "approveCode": 0}, {"key": "*********4:*********", "paymentID": *********, "amt": 1496.64, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352558148, "codeType": "PMNT", "amt": 7.47, "curr": "AED", "originalAmt": 7.47, "originalCurr": "AED", "status": 1, "billDate": "2025-05-19T11:39:18", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352558148:*********", "paymentID": *********, "amt": 7.47, "approveCode": 0}]}, {"chargeID": 1352556014, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352556014:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1356098693, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-21T14:50:46", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181492", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1352556013, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": *********4, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352556011, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": *********4, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352556012, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": *********4, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T11:38:05", "billDate": "2025-05-19T11:38:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181492", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1358203928, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-23T01:58:42", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181492", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}]}], "parentPNRs": [], "childPNRs": []}