{"seriesNum": "299", "PNR": "NZMNFL", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "83185917", "bookDate": "2025-05-27T18:20:35", "modifyDate": "2025-05-28T03:29:59", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 2, "activeSegCount": 2, "webBookingID": "1396zc3ci4n3u5t4p96uc8c4473e85667fd92584c39f", "securityGUID": "1396zc3ci4n3u5t4p96uc8c4473e85667fd92584c39f", "lastLoadGUID": "e572183d-3abb-4eb1-8600-15ed1ab0afab", "isAsyncPNR": false, "MasterPNR": "NZMNFL", "segments": [{"segKey": "16087871:16087871:6/6/2025 5:15:00 AM", "LFID": 16087871, "depDate": "2025-06-06T00:00:00", "flightGroupId": "16087871", "org": "DXB", "dest": "TBS", "depTime": "2025-06-06T05:15:00", "depTimeGMT": "2025-06-06T01:15:00", "arrTime": "2025-06-06T08:40:00", "operCarrier": "FZ", "operFlightNum": "8625", "mrktCarrier": "FZ ", "mrktFlightNum": "8625", "persons": [{"recNum": 1, "status": 1}, {"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181625, "depDate": "2025-06-06T05:15:00", "legKey": "16087871:181625:6/6/2025 5:15:00 AM", "customerKey": "E64BA7144CE9AFCD97B46010B6D51C105C3A1981A6564844C838E954868ECEA1"}], "active": true, "changeType": "TK"}, {"segKey": "16087474:16087474:6/8/2025 6:05:00 AM", "LFID": 16087474, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16087474", "org": "TBS", "dest": "DXB", "depTime": "2025-06-08T06:05:00", "depTimeGMT": "2025-06-08T02:05:00", "arrTime": "2025-06-08T09:15:00", "operCarrier": "FZ", "operFlightNum": "714", "mrktCarrier": "FZ ", "mrktFlightNum": "714", "persons": [{"recNum": 4, "status": 1}, {"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181235, "depDate": "2025-06-08T06:05:00", "legKey": "16087474:181235:6/8/2025 6:05:00 AM", "customerKey": "C008A500413D2C9C133B420CF5E6E67F1CC35CA1F3F1926CC086D2F9B4EB2247"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270363012, "fName": "OLIVER", "lName": "CAPACIA", "title": "MR", "PTCID": 1, "gender": "M", "FFNum": "564948274", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 4]}, {"paxID": 270363013, "fName": "GRACHELLE", "lName": "MENDOZA", "title": "MS", "PTCID": 1, "gender": "F", "FFNum": "532443450", "FFTier": "BLUE", "TierID": "3", "recNum": [2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 6:20:35 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WT4MJ-RXFKL-INS/6a6f8770-5756-48d9-913f-1d6ac59ac678", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835ff6a0007780000005c4d#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T18:20:35"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 6:20:35 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WT4MJ-RXFKL-INS/6a6f8770-5756-48d9-913f-1d6ac59ac678", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835ff6a0007780000005c4d#2#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T18:20:35"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 6:20:35 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WT4MJ-RXFKL-INS/6a6f8770-5756-48d9-913f-1d6ac59ac678", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6835ff6a0007780000005c4d#2#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T18:20:35"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 6:20:35 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WT4MJ-RXFKL-INS/6a6f8770-5756-48d9-913f-1d6ac59ac678", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835ff6a0007780000005c4d#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T18:20:35"}]}], "payments": [{"paymentID": 210496086, "paxID": 270367041, "method": "MSCD", "status": "2", "paidDate": "2025-05-27T18:21:13", "cardNum": "************5953", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2789.65, "baseCurr": "AED", "baseAmt": 2789.65, "userID": "smartrez", "channelID": 5, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "reference": "23287441", "externalReference": "23287441", "tranId": "21668529", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21668529}, {"paymentID": *********, "paxID": 270384461, "method": "MSCD", "status": "1", "paidDate": "2025-05-28T03:29:56", "cardNum": "************5953", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2789.65, "baseCurr": "AED", "baseAmt": 2789.65, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "authCode": "766137", "reference": "23291985", "externalReference": "23291985", "tranId": "21672367", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21672367}], "OAFlights": null, "physicalFlights": [{"key": "16087871:181625:2025-06-06T05:15:00 AM", "LFID": 16087871, "PFID": 181625, "org": "DXB", "dest": "TBS", "depDate": "2025-06-06T05:15:00", "depTime": "2025-06-06T05:15:00", "arrTime": "2025-06-06T08:40:00", "carrier": "FZ", "flightNum": "8625", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "8625", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": true, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": true, "changeType": "TK", "flightChangeTime": "5/8/2025 12:37:54 PM"}, {"key": "16087474:181235:2025-06-08T06:05:00 AM", "LFID": 16087474, "PFID": 181235, "org": "TBS", "dest": "DXB", "depDate": "2025-06-08T06:05:00", "depTime": "2025-06-08T06:05:00", "arrTime": "2025-06-08T09:15:00", "carrier": "FZ", "flightNum": "714", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "714", "flightStatus": "OPEN", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:26 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1364698332, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698332:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-27T18:20:35"}, {"chargeID": 1364698304, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364698299, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698304:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1364698302, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364698299, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698302:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364698303, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364698299, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698303:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364698301, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364698299, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698301:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364698300, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364698299, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698300:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364698299, "codeType": "AIR", "amt": 305, "curr": "AED", "originalAmt": 305, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T18:20:35", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698299:*********", "paymentID": *********, "amt": 305, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364992655, "codeType": "PMNT", "amt": 81.25, "curr": "AED", "originalAmt": 81.25, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T03:29:59", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364992655:*********", "paymentID": *********, "amt": 81.25, "approveCode": 0}]}, {"chargeID": 1364698333, "codeType": "NSST", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "NSST", "comment": "FLXID:73X_NSST_ZONE3_WIN_AIS::181625", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698333:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "181625", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698305, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364698299, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698343, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181625", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1364698335, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698335:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-27T18:20:35"}, {"chargeID": 1364698312, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1364698309, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698312:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364698311, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364698309, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698311:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1364698313, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1364698309, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698313:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1364698310, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364698309, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698310:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364698309, "codeType": "AIR", "amt": 305, "curr": "AED", "originalAmt": 305, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T18:20:35", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698309:*********", "paymentID": *********, "amt": 305, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698336, "codeType": "NSST", "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "NSST", "comment": "FLXID:NSST_ZONE3_MID::181235", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698336:*********", "paymentID": *********, "amt": 30, "approveCode": 0}], "PFID": "181235", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698314, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364698309, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698344, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181235", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1364698338, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698338:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-27T18:20:35"}, {"chargeID": 1364698317, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364698316, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698317:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364698319, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364698316, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698319:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364698321, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364698316, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698321:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1364698318, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364698316, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698318:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364698320, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364698316, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698320:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364698316, "codeType": "AIR", "amt": 305, "curr": "AED", "originalAmt": 305, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T18:20:35", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698316:*********", "paymentID": *********, "amt": 305, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698339, "codeType": "NSST", "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "NSST", "comment": "FLXID:73X_NSST_ZONE3_MID::181625", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698339:*********", "paymentID": *********, "amt": 30, "approveCode": 0}], "PFID": "181625", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698322, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364698316, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698345, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181625", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1364698341, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698341:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-27T18:20:35"}, {"chargeID": 1364698326, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364698324, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698326:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1364698327, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1364698324, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698327:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1364698325, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364698324, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698325:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364698328, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1364698324, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364698328:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1364698324, "codeType": "AIR", "amt": 305, "curr": "AED", "originalAmt": 305, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T18:20:35", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698324:*********", "paymentID": *********, "amt": 305, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698342, "codeType": "NSST", "amt": 32, "curr": "AED", "originalAmt": 32, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "NSST", "comment": "FLXID:NSST_ZONE3_WIN_AIS::181235", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364698342:*********", "paymentID": *********, "amt": 32, "approveCode": 0}], "PFID": "181235", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698329, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364698324, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364698346, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T18:20:35", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181235", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}