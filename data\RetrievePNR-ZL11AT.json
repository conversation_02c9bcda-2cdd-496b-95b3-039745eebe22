{"seriesNum": "299", "PNR": "ZL11AT", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82767204", "bookDate": "2025-05-12T18:15:20", "modifyDate": "2025-05-14T04:16:44", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "99b862sbie0fub75bc66840442d9ba3e96f3199941f6", "securityGUID": "99b862sbie0fub75bc66840442d9ba3e96f3199941f6", "lastLoadGUID": "9253304b-2133-4f73-833d-3b5fb4c08ce6", "isAsyncPNR": false, "MasterPNR": "ZL11AT", "segments": [{"segKey": "16087749:16087749:5/14/2025 10:15:00 AM", "LFID": 16087749, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087749", "org": "DXB", "dest": "ZNZ", "depTime": "2025-05-14T10:15:00", "depTimeGMT": "2025-05-14T06:15:00", "arrTime": "2025-05-14T14:40:00", "operCarrier": "FZ", "operFlightNum": "1687", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181499, "depDate": "2025-05-14T10:15:00", "legKey": "16087749:181499:5/14/2025 10:15:00 AM", "customerKey": "39E713C6055F3AB8EC0CEFD436BE18B0F0B966F81148A4588AE8BDBBE5B68855"}], "active": true, "changeType": "TK"}, {"segKey": "16087777:16087777:5/17/2025 9:20:00 PM", "LFID": 16087777, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087777", "org": "ZNZ", "dest": "DXB", "depTime": "2025-05-17T21:20:00", "depTimeGMT": "2025-05-17T18:20:00", "arrTime": "2025-05-18T03:45:00", "operCarrier": "FZ", "operFlightNum": "1688", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "persons": [{"recNum": 4, "status": 5}], "legDetails": [{"PFID": 181515, "depDate": "2025-05-17T21:20:00", "legKey": "16087777:181515:5/17/2025 9:20:00 PM", "customerKey": "57F818E21D85ED28A4BECE8CBEC3DE5C8EDBB0470801D0AB61C3485AB6D56D41"}], "active": true, "changeType": "TK"}, {"segKey": "16087749:16087749:5/13/2025 10:05:00 AM", "LFID": 16087749, "depDate": "2025-05-13T00:00:00", "flightGroupId": "16087749", "org": "DXB", "dest": "ZNZ", "depTime": "2025-05-13T10:05:00", "depTimeGMT": "2025-05-13T06:05:00", "arrTime": "2025-05-13T14:30:00", "operCarrier": "FZ", "operFlightNum": "1687", "mrktCarrier": "FZ ", "mrktFlightNum": "1687", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181499, "depDate": "2025-05-13T10:05:00", "legKey": "16087749:181499:5/13/2025 10:05:00 AM", "customerKey": "6FE28DBF6F1DF65F1063AA4E690E6D0B9868CC2CA8F2FC6E7C3FD42CE43D5B4A"}], "active": true, "changeType": "TK"}, {"segKey": "16087777:16087777:5/16/2025 9:20:00 PM", "LFID": 16087777, "depDate": "2025-05-16T00:00:00", "flightGroupId": "16087777", "org": "ZNZ", "dest": "DXB", "depTime": "2025-05-16T21:20:00", "depTimeGMT": "2025-05-16T18:20:00", "arrTime": "2025-05-17T03:45:00", "operCarrier": "FZ", "operFlightNum": "1688", "mrktCarrier": "FZ ", "mrktFlightNum": "1688", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181515, "depDate": "2025-05-16T21:20:00", "legKey": "16087777:181515:5/16/2025 9:20:00 PM", "customerKey": "6ECD85AB1A3BE3CE40EDF7FAFBBD4D3D2DA1F635DEC37E7C515BB9EAFE52D29E"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268747688, "fName": "MARCO PRUDENCIO", "lName": "BARBOZA", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/12/2025 6:15:21 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "HBAJ2-MDB3A-INS", "insuTransID": "HBAJ2-MDB3A-INS/5f28efcd-bcd2-4dda-bbef-662784c07460", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6822391700077800000037e9#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-12T18:15:20"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/12/2025 6:15:21 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "HBAJ2-MDB3A-INS", "insuTransID": "HBAJ2-MDB3A-INS/5f28efcd-bcd2-4dda-bbef-662784c07460", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6822391700077800000037e9#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-12T18:15:20"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "kizhakethil.a", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 6:15:21 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "HBAJ2-MDB3A-INS/5f28efcd-bcd2-4dda-bbef-662784c07460", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6823176e0007770000007031#268747688#1#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-13T09:59:32"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "kizhakethil.a", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 6:15:21 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "HBAJ2-MDB3A-INS/5f28efcd-bcd2-4dda-bbef-662784c07460", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6823176e0007770000007031#268747688#2#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-13T09:59:33"}]}], "payments": [{"paymentID": *********, "paxID": 268812185, "method": "TABY", "status": "1", "paidDate": "2025-05-13T10:12:14", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 782.25, "baseCurr": "AED", "baseAmt": 782.25, "userID": "paybylink", "channelID": 2, "authCode": "A5358439", "reference": "A5358439", "externalReference": "A5358439", "tranId": "21379366", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FDAE", "exchangeRate": "1", "resExternalPaymentID": 21379366}, {"paymentID": *********, "paxID": 268747888, "method": "TABY", "status": "1", "paidDate": "2025-05-12T18:16:56", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1470.74, "baseCurr": "AED", "baseAmt": 1470.74, "userID": "MOBILE_APP", "channelID": 12, "authCode": "A5355124", "reference": "A5355124", "externalReference": "A5355124", "tranId": "21367873", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FDAE", "exchangeRate": "1", "resExternalPaymentID": 21367873}], "OAFlights": null, "physicalFlights": [{"key": "16087749:181499:2025-05-13T10:05:00 AM", "LFID": 16087749, "PFID": 181499, "org": "DXB", "dest": "ZNZ", "depDate": "2025-05-13T10:05:00", "depTime": "2025-05-13T10:05:00", "arrTime": "2025-05-13T14:30:00", "carrier": "FZ", "flightNum": "1687", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": false, "changeType": "TK", "flightChangeTime": "3/13/2025 11:31:04 AM"}, {"key": "16087749:181499:2025-05-14T10:15:00 AM", "LFID": 16087749, "PFID": 181499, "org": "DXB", "dest": "ZNZ", "depDate": "2025-05-14T10:15:00", "depTime": "2025-05-14T10:15:00", "arrTime": "2025-05-14T14:40:00", "carrier": "FZ", "flightNum": "1687", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 2:38:31 PM"}, {"key": "16087777:181515:2025-05-16T09:20:00 PM", "LFID": 16087777, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-05-16T21:20:00", "depTime": "2025-05-16T21:20:00", "arrTime": "2025-05-17T03:45:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "CLOSED", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/2/2025 7:00:49 AM"}, {"key": "16087777:181515:2025-05-17T09:20:00 PM", "LFID": 16087777, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-05-17T21:20:00", "depTime": "2025-05-17T21:20:00", "arrTime": "2025-05-18T03:45:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "CLOSED", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/2/2025 7:00:49 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1343212093, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212093:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-12T18:15:20"}, {"chargeID": 1344033617, "codeType": "INSU", "taxChargeID": 1344033612, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212093, "paymentMap": [{"key": "1344033617:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1343212077, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343212076, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212077:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343212082, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343212076, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212082:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343212081, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343212076, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212081:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1343212079, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343212076, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212079:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1343212080, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343212076, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212080:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1344033613, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1344033612, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:33", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212080, "paymentMap": [{"key": "1344033613:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1344033615, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344033612, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212079, "paymentMap": [{"key": "1344033615:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1344033616, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1344033612, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212081, "paymentMap": [{"key": "1344033616:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1344033618, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1344033612, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212082, "paymentMap": [{"key": "1344033618:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1344033619, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344033612, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212077, "paymentMap": [{"key": "1344033619:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343212076, "codeType": "AIR", "amt": 230, "curr": "AED", "originalAmt": 230, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T18:15:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212076:*********", "paymentID": *********, "amt": 230, "approveCode": 0}]}, {"chargeID": 1344033612, "codeType": "AIR", "amt": -230, "curr": "AED", "originalAmt": -230, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T09:59:33", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212076, "paymentMap": [{"key": "1344033612:*********", "paymentID": *********, "amt": -230, "approveCode": 0}]}, {"chargeID": 1343217054, "codeType": "PMNT", "amt": 70.04, "curr": "AED", "originalAmt": 70.04, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T18:17:00", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343217054:*********", "paymentID": *********, "amt": 70.04, "approveCode": 0}]}, {"chargeID": 1344033621, "codeType": "PNLT", "amt": 460, "curr": "AED", "originalAmt": 460, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:34", "billDate": "2025-05-13T09:59:34", "desc": "CancelNoRefund FZ 1687 DXB - ZNZ 13.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033621:*********", "paymentID": *********, "amt": 460, "approveCode": 0}]}, {"chargeID": 1343212078, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1343212076, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1344033620, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1344033612, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212078, "paymentMap": []}, {"chargeID": 1343212096, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}, {"chargeID": 1344033614, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1344033612, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:33", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212096, "paymentMap": [], "PFID": "181499"}]}, {"recNum": 2, "charges": [{"chargeID": 1343212095, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212095:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-12T18:15:20"}, {"chargeID": 1344033653, "codeType": "INSU", "taxChargeID": 1344033622, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212095, "paymentMap": [{"key": "1344033653:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1343212088, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343212084, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212088:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1343212085, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343212084, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212085:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343212089, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1343212084, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212089:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343212090, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1343212084, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Airport Security Fee (International)", "comment": "Airport Security Fee (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212090:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1343212087, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1343212084, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Aviation Safety Fee", "comment": "Aviation Safety Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212087:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1344033625, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1344033622, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Airport Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212090, "paymentMap": [{"key": "1344033625:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1344033636, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1344033622, "amt": -150, "curr": "AED", "originalAmt": -150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Airport Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212089, "paymentMap": [{"key": "1344033636:*********", "paymentID": *********, "amt": -150, "approveCode": 0}]}, {"chargeID": 1344033651, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1344033622, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Aviation Safety Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212087, "paymentMap": [{"key": "1344033651:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1344033652, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344033622, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212085, "paymentMap": [{"key": "1344033652:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1344033654, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344033622, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212088, "paymentMap": [{"key": "1344033654:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1343212084, "codeType": "AIR", "amt": 230, "curr": "AED", "originalAmt": 230, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T18:15:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343212084:*********", "paymentID": *********, "amt": 230, "approveCode": 0}]}, {"chargeID": 1344033622, "codeType": "AIR", "amt": -230, "curr": "AED", "originalAmt": -230, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T09:59:34", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212084, "paymentMap": [{"key": "1344033622:*********", "paymentID": *********, "amt": -230, "approveCode": 0}]}, {"chargeID": 1344033660, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:34", "billDate": "2025-05-13T09:59:34", "desc": "CancelNoRefund FZ 1688 ZNZ - DXB 16.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033660:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343212086, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1343212084, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1344033655, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1344033622, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212086, "paymentMap": []}, {"chargeID": 1343212097, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T18:15:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181515"}, {"chargeID": 1344033642, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1344033622, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T09:59:34", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1343212097, "paymentMap": [], "PFID": "181515"}]}, {"recNum": 3, "charges": [{"chargeID": 1344033685, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:57:07", "billDate": "2025-05-13T09:59:35", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033685:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-13T09:59:35"}, {"chargeID": 1344033676, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1344033668, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033676:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1344033678, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1344033668, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033678:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1344033679, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344033668, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033679:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1344033680, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344033668, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033680:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1344033681, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1344033668, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033681:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1344033668, "codeType": "AIR", "amt": 235, "curr": "AED", "originalAmt": 235, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:34", "billDate": "2025-05-13T09:59:35", "desc": "FZ 1687 DXB-ZNZ 14May2025 Wed 10:15 14:40\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033668:*********", "paymentID": *********, "amt": 235, "approveCode": 0}]}, {"chargeID": 1344056235, "codeType": "PMNT", "amt": 37.25, "curr": "AED", "originalAmt": 37.25, "originalCurr": "AED", "status": 1, "billDate": "2025-05-13T10:12:20", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344056235:*********", "paymentID": *********, "amt": 37.25, "approveCode": 0}]}, {"chargeID": 1344033684, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033684:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1344033682, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1344033668, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1344033683, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1344033668, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}, {"chargeID": 1345168789, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T04:16:44", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499", "ssrCommentId": "*********"}]}, {"recNum": 4, "charges": [{"chargeID": 1344033695, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:57:07", "billDate": "2025-05-13T09:59:36", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033695:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-13T09:59:36"}, {"chargeID": 1344033687, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1344033686, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "NN: Airport Service Charge", "comment": "NN: Airport Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033687:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1344033688, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1344033686, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "HY: Aviation Safety Fee", "comment": "HY: Aviation Safety Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033688:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1344033689, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344033686, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033689:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1344033690, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344033686, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033690:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1344033691, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1344033686, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "P9: Airport Security Fee (International)", "comment": "P9: Airport Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033691:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1344033686, "codeType": "AIR", "amt": 240, "curr": "AED", "originalAmt": 240, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "FZ 1688 ZNZ-DXB 17May2025 Sat 21:20 03:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033686:*********", "paymentID": *********, "amt": 112.15, "approveCode": 0}, {"key": "1344033686:*********", "paymentID": *********, "amt": 127.85, "approveCode": 0}]}, {"chargeID": 1344033694, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:36", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1344033694:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1344033692, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1344033686, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1344033693, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1344033686, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T09:59:35", "billDate": "2025-05-13T09:59:35", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181515"}]}], "parentPNRs": [], "childPNRs": []}