{"seriesNum": "299", "PNR": "9E6KU5", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82635486", "bookDate": "2025-05-07T18:10:12", "modifyDate": "2025-05-16T06:20:26", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "2473d7248b0746aba8a4q1c3tf53u6e2ea6769wf4263", "securityGUID": "2473d7248b0746aba8a4q1c3tf53u6e2ea6769wf4263", "lastLoadGUID": "35382304369311BCE0631E206F0AD8BD", "MasterPNR": "9E6KU5", "segments": [{"segKey": "16087261:16087261:5/10/2025 4:00:00 PM", "LFID": 16087261, "depDate": "2025-05-10T00:00:00", "flightGroupId": "16087261", "org": "DXB", "dest": "DOH", "depTime": "2025-05-10T16:00:00", "depTimeGMT": "2025-05-10T12:00:00", "arrTime": "2025-05-10T16:10:00", "operCarrier": "FZ", "operFlightNum": "017", "mrktCarrier": "FZ ", "mrktFlightNum": "017", "persons": [{"recNum": 1, "status": 0}, {"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181012, "depDate": "2025-05-10T16:00:00", "legKey": "16087261:181012:5/10/2025 4:00:00 PM", "customerKey": "3AD05F05ED168FCC5509CDB6EA4FBA2B93895A8D61955960B21A465D8653A33F"}], "active": true, "changeType": "TK"}, {"segKey": "16087271:16087271:5/11/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-05-11T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-05-11T17:05:00", "depTimeGMT": "2025-05-11T14:05:00", "arrTime": "2025-05-11T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ ", "mrktFlightNum": "018", "persons": [{"recNum": 3, "status": 0}, {"recNum": 4, "status": 0}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-11T17:05:00", "legKey": "16087271:181022:5/11/2025 5:05:00 PM", "customerKey": "29B1E510F16CF4C15E278027B12B74493C70E98B0B0CC1DFA550149C0C457122"}], "active": true, "changeType": "TK"}, {"segKey": "16087261:16087261:5/17/2025 3:55:00 PM", "LFID": 16087261, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087261", "org": "DXB", "dest": "DOH", "depTime": "2025-05-17T15:55:00", "depTimeGMT": "2025-05-17T11:55:00", "arrTime": "2025-05-17T16:05:00", "operCarrier": "FZ", "operFlightNum": "017", "mrktCarrier": "FZ", "mrktFlightNum": "017", "persons": [{"recNum": 5, "status": 0}, {"recNum": 7, "status": 0}], "legDetails": [{"PFID": 181012, "depDate": "2025-05-17T15:55:00", "legKey": "16087261:181012:5/17/2025 3:55:00 PM", "customerKey": "66088DC718AB9D43C203E07C2F2AD0C0A931679BBFA4240240D9BFC8CA35367C"}], "active": true, "changeType": "TK"}, {"segKey": "16087271:16087271:5/18/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-05-18T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-05-18T17:05:00", "depTimeGMT": "2025-05-18T14:05:00", "arrTime": "2025-05-18T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ", "mrktFlightNum": "018", "persons": [{"recNum": 6, "status": 0}, {"recNum": 8, "status": 0}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-18T17:05:00", "legKey": "16087271:181022:5/18/2025 5:05:00 PM", "customerKey": "BA0EFD5F13F9FE7875677141333AE6E1CD1B81832CC80590EBED5135E33A53B7"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268234728, "fName": "AHMED", "lName": "ABDELAZIM OSMAN MUSA", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1984-11-29T00:00:00", "FFNum": "495048713", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 3, 5, 6]}, {"paxID": 268234729, "fName": "LIMYA", "lName": "IBRAHIM", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1987-04-26T00:00:00", "recNum": [2, 4, 7, 8]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "APC3N-VZJUR-INS", "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681b9cf70007770000001093#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-07T18:10:12"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "APC3N-VZJUR-INS", "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "toRecNum": 7, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681b9cf70007770000001093#2#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-07T18:10:12"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "APC3N-VZJUR-INS", "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681b9cf70007770000001093#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-07T18:10:12"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "APC3N-VZJUR-INS", "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "toRecNum": 8, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681b9cf70007770000001093#2#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-07T18:10:12"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "johana.ab<PERSON>ah", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681c80680007770000000642#268234728#1#ENT#SFQE#CHANGE", "fareTypeID": 23, "channelID": 1, "cancelReasonID": 7, "bookDate": "2025-05-08T10:02:01"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "johana.ab<PERSON>ah", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681c80680007770000000642#268234728#2#ENT#SFQE#CHANGE", "fareTypeID": 23, "channelID": 1, "cancelReasonID": 7, "bookDate": "2025-05-08T10:02:01"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "johana.ab<PERSON>ah", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681c80680007770000000642#268234729#1#ENT#SFQE#CHANGE", "fareTypeID": 23, "channelID": 1, "cancelReasonID": 7, "bookDate": "2025-05-08T10:02:01"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "johana.ab<PERSON>ah", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/7/2025 6:10:13 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "APC3N-VZJUR-INS/3124e912-80f3-4609-b50c-c3163083c5c2", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681c80680007770000000642#268234729#2#ENT#SFQE#CHANGE", "fareTypeID": 23, "channelID": 1, "cancelReasonID": 7, "bookDate": "2025-05-08T10:02:02"}]}], "payments": [{"paymentID": *********, "paxID": 268391902, "method": "IPAY", "status": "1", "paidDate": "2025-05-09T08:33:31", "cardNum": "************7041", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 267.8, "baseCurr": "AED", "baseAmt": 267.8, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "532245", "reference": "22929839", "externalReference": "22929839", "tranId": "21301227", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21301227}, {"paymentID": *********, "paxID": 268234728, "method": "VCHR", "status": "1", "paidDate": "2025-05-16T06:17:00", "voucherNum": 3260873, "paidCurr": "AED", "paidAmt": -1710, "baseCurr": "AED", "baseAmt": -1710, "userID": "leqaa.abdallah", "channelID": 1, "paymentComment": "XX", "tierID": "3", "voucherNumFull": "VJVBG8", "reference": "Cancel Refund Voucher Flier", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1"}, {"paymentID": *********, "paxID": 268234729, "method": "VCHR", "status": "1", "paidDate": "2025-05-16T06:17:00", "voucherNum": 3260874, "paidCurr": "AED", "paidAmt": -1710, "baseCurr": "AED", "baseAmt": -1710, "userID": "leqaa.abdallah", "channelID": 1, "paymentComment": "XX", "voucherNumFull": "JTGZK2", "reference": "Cancel Refund Voucher Flier", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1"}, {"paymentID": *********, "paxID": 268234764, "method": "VISA", "status": "1", "paidDate": "2025-05-07T18:10:48", "cardNum": "************7714", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 3575.54, "baseCurr": "AED", "baseAmt": 3575.54, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "350012", "reference": "22899449", "externalReference": "22899449", "tranId": "21273486", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21273486}, {"paymentID": *********, "paxID": 268241035, "method": "IPAY", "status": "1", "paidDate": "2025-05-07T19:29:50", "cardNum": "************1611", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 113.3, "baseCurr": "AED", "baseAmt": 113.3, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "801609", "reference": "22900842", "externalReference": "22900842", "tranId": "21274727", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21274727}], "OAFlights": null, "physicalFlights": [{"key": "16087261:181012:2025-05-10T04:00:00 PM", "LFID": 16087261, "PFID": 181012, "org": "DXB", "dest": "DOH", "depDate": "2025-05-10T16:00:00", "depTime": "2025-05-10T16:00:00", "arrTime": "2025-05-10T16:10:00", "carrier": "FZ", "flightNum": "017", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "017", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "TK", "flightChangeTime": "5/6/2025 1:21:37 PM"}, {"key": "16087271:181022:2025-05-11T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-11T17:05:00", "depTime": "2025-05-11T17:05:00", "arrTime": "2025-05-11T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:39:48 AM"}, {"key": "16087261:181012:2025-05-17T03:55:00 PM", "LFID": 16087261, "PFID": 181012, "org": "DXB", "dest": "DOH", "depDate": "2025-05-17T15:55:00", "depTime": "2025-05-17T15:55:00", "arrTime": "2025-05-17T16:05:00", "carrier": "FZ", "flightNum": "017", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "017", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:39:48 AM"}, {"key": "16087271:181022:2025-05-18T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-18T17:05:00", "depTime": "2025-05-18T17:05:00", "arrTime": "2025-05-18T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:39:50 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1337276862, "codeType": "INSU", "taxChargeID": 1337276857, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "INSU", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461795, "paymentMap": [{"key": "1337276862:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-08T10:02:03"}, {"chargeID": 1336461795, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461795:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1337276859, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337276857, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "YQ - DUMMY", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461717, "paymentMap": [{"key": "1337276859:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1337276865, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337276857, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Passenger Service Charge (Intl)", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461718, "paymentMap": [{"key": "1337276865:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1337276910, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337276857, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Passenger Facilities Charge.", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461720, "paymentMap": [{"key": "1337276910:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1337276911, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337276857, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Passengers Security & Safety Service Fees", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461721, "paymentMap": [{"key": "1337276911:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1337276912, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337276857, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Passenger Service Charge", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461719, "paymentMap": [{"key": "1337276912:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1337276916, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337276857, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Advanced passenger information fee", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461715, "paymentMap": [{"key": "1337276916:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1336461719, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1336461714, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461719:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1336461721, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336461714, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461721:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336461720, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336461714, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461720:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1336461718, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336461714, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461718:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1336461717, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336461714, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461717:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1336461715, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336461714, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461715:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337276857, "codeType": "AIR", "amt": -640, "curr": "AED", "originalAmt": -640, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T10:02:02", "desc": "WEB:AIR", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461714, "paymentMap": [{"key": "1337276857:*********", "paymentID": *********, "amt": -640, "approveCode": 0}]}, {"chargeID": 1336461714, "codeType": "AIR", "amt": 640, "curr": "AED", "originalAmt": 640, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T18:10:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461714:*********", "paymentID": *********, "amt": 640, "approveCode": 0}]}, {"chargeID": 1336548152, "codeType": "PMNT", "amt": 3.3, "curr": "AED", "originalAmt": 3.3, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T19:29:55", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336548152:*********", "paymentID": *********, "amt": 3.3, "approveCode": 0}]}, {"chargeID": 1336466688, "codeType": "PMNT", "amt": 104.14, "curr": "AED", "originalAmt": 104.14, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T18:10:54", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336466688:*********", "paymentID": *********, "amt": 104.14, "approveCode": 0}]}, {"chargeID": 1337276914, "codeType": "SPST", "taxChargeID": 1337276857, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "SPST", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461796, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1336461796, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181012", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1337276913, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337276857, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Included seat", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461722, "paymentMap": []}, {"chargeID": 1336461722, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1336461714, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337276860, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337276857, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "30kg BAG INCLUDED IN FARE", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461716, "paymentMap": []}, {"chargeID": 1336461716, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1336461714, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337276915, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337276857, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "Standard meal", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461806, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1336461806, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181012"}]}, {"recNum": 3, "charges": [{"chargeID": 1337276932, "codeType": "INSU", "taxChargeID": 1337276917, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "INSU", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461798, "paymentMap": [{"key": "1337276932:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-08T10:02:04"}, {"chargeID": 1336461798, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461798:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1337276933, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1337276917, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Passenger safety and security fees (PSSF)", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461771, "paymentMap": [{"key": "1337276933:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1337276934, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1337276917, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Airport Fee.", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461726, "paymentMap": [{"key": "1337276934:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1337276936, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337276917, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "YQ - DUMMY", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461769, "paymentMap": [{"key": "1337276936:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1337276938, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337276917, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Passenger Service Charge", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461770, "paymentMap": [{"key": "1337276938:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1337276940, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337276917, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Advanced passenger information fee", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461725, "paymentMap": [{"key": "1337276940:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1337276941, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1337276917, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Passenger Facility Charge.", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461728, "paymentMap": [{"key": "1337276941:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1336461771, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1336461724, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461771:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1336461769, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336461724, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461769:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1336461770, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1336461724, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461770:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1336461725, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336461724, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461725:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336461728, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1336461724, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461728:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1336461726, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1336461724, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461726:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1337276917, "codeType": "AIR", "amt": -595, "curr": "AED", "originalAmt": -595, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T10:02:03", "desc": "WEB:AIR", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461724, "paymentMap": [{"key": "1337276917:*********", "paymentID": *********, "amt": -595, "approveCode": 0}]}, {"chargeID": 1336461724, "codeType": "AIR", "amt": 595, "curr": "AED", "originalAmt": 595, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T18:10:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461724:*********", "paymentID": *********, "amt": 595, "approveCode": 0}]}, {"chargeID": 1337276928, "codeType": "SPST", "taxChargeID": 1337276917, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:03", "desc": "SPST", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461799, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1336461799, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181022", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1337276929, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337276917, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Included seat", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461772, "paymentMap": []}, {"chargeID": 1336461772, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1336461724, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337276930, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337276917, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "30kg BAG INCLUDED IN FARE", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461727, "paymentMap": []}, {"chargeID": 1336461727, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1336461724, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337276939, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337276917, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:04", "desc": "Standard meal", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461807, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1336461807, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 5, "charges": [{"chargeID": 1337276981, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:22", "billDate": "2025-05-08T10:02:04", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276981:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":19.44,\"tax\":0.93,\"currency\":\"USD\",\"segPaxCount\":4}", "ChargeBookDate": "2025-05-08T10:02:04"}, {"chargeID": 1337276943, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337276942, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276943:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1337276944, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337276942, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276944:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337276945, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337276942, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276945:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337276946, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337276942, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276946:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337276947, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337276942, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276947:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1337276948, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337276942, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276948:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1348497871, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1348497870, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "F6: Passenger Facilities Charge.", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276943, "paymentMap": [{"key": "1348497871:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1348497872, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1348497870, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276944, "paymentMap": [{"key": "1348497872:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348497873, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1348497870, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "ZR: Advanced passenger information fee", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276945, "paymentMap": [{"key": "1348497873:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348497874, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1348497870, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "PZ: Passenger Service Charge", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276946, "paymentMap": [{"key": "1348497874:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1348497875, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1348497870, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "AE: Passenger Service Charge (Intl)", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276947, "paymentMap": [{"key": "1348497875:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1348497876, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1348497870, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "YQ: YQ - DUMMY", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276948, "paymentMap": [{"key": "1348497876:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1337276942, "codeType": "AIR", "amt": 640, "curr": "AED", "originalAmt": 640, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "FZ 017 DXB-DOH 17May2025 Sat 15:55 16:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276942:*********", "paymentID": *********, "amt": 640, "approveCode": 0}]}, {"chargeID": 1348497870, "codeType": "AIR", "amt": -640, "curr": "AED", "originalAmt": -640, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:00", "desc": "FZ 017 DXB-DOH 17May2025 Sat 15:55 16:05\r\n", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276942, "paymentMap": [{"key": "1348497870:*********", "paymentID": *********, "amt": -640, "approveCode": 0}]}, {"chargeID": 1338544155, "codeType": "PMNT", "amt": 7.8, "curr": "AED", "originalAmt": 7.8, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T08:33:39", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338544155:*********", "paymentID": *********, "amt": 7.8, "approveCode": 0}]}, {"chargeID": 1337276980, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276980:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1337276982, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:53", "billDate": "2025-05-08T10:02:04", "desc": "Special Service Request:SPST-14A", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nSPECIAL SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181012", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497880, "codeType": "SPST", "taxChargeID": 1348497870, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:53", "billDate": "2025-05-16T06:17:01", "desc": "Special Service Request:SPST-14A", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276982, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1337276975, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337276942, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497879, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1348497870, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "INST: Included seat", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276975, "paymentMap": []}, {"chargeID": 1337276969, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337276942, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497877, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1348497870, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276969, "paymentMap": []}, {"chargeID": 1337276970, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337276942, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181012", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497878, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1348497870, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "MLIN: Standard meal", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276970, "paymentMap": [], "PFID": "181012"}]}, {"recNum": 6, "charges": [{"chargeID": 1337276998, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:22", "billDate": "2025-05-08T10:02:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276998:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "1337276998:*********", "paymentID": *********, "amt": 7.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":19.44,\"tax\":0.93,\"currency\":\"USD\",\"segPaxCount\":4}", "ChargeBookDate": "2025-05-08T10:02:05"}, {"chargeID": 1337276984, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337276983, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276984:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337276989, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1337276983, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "QA: Airport Fee.", "comment": "QA: Airport Fee.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276989:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1337276990, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1337276983, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "R9: Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276990:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337276991, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337276983, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276991:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337276992, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1337276983, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "G4: Passenger Facility Charge.", "comment": "G4: Passenger Facility Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276992:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1337276993, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337276983, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276993:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1348497882, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1348497881, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:02", "desc": "ZR: Advanced passenger information fee", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276984, "paymentMap": [{"key": "1348497882:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348497883, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1348497881, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "QA: Airport Fee.", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276989, "paymentMap": [{"key": "1348497883:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1348497884, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1348497881, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276990, "paymentMap": [{"key": "1348497884:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1348497885, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1348497881, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "PZ: Passenger Service Charge", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276991, "paymentMap": [{"key": "1348497885:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1348497914, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1348497881, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "G4: Passenger Facility Charge.", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276992, "paymentMap": [{"key": "1348497914:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1348497931, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1348497881, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "YQ: YQ - DUMMY", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276993, "paymentMap": [{"key": "1348497931:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1337276983, "codeType": "AIR", "amt": 605, "curr": "AED", "originalAmt": 605, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-08T10:02:04", "desc": "FZ 018 DOH-DXB 18May2025 Sun 17:05 19:20\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276983:*********", "paymentID": *********, "amt": 605, "approveCode": 0}]}, {"chargeID": 1348497881, "codeType": "AIR", "amt": -605, "curr": "AED", "originalAmt": -605, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:04", "billDate": "2025-05-16T06:17:01", "desc": "FZ 018 DOH-DXB 18May2025 Sun 17:05 19:20\r\n", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276983, "paymentMap": [{"key": "1348497881:*********", "paymentID": *********, "amt": -605, "approveCode": 0}]}, {"chargeID": 1337276997, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337276997:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1337276999, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:58", "billDate": "2025-05-08T10:02:05", "desc": "Special Service Request:SPST-14A", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nSPECIAL SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497927, "codeType": "SPST", "taxChargeID": 1348497881, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:58", "billDate": "2025-05-16T06:17:02", "desc": "Special Service Request:SPST-14A", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276999, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1337276996, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337276983, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497930, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1348497881, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "INST: Included seat", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276996, "paymentMap": []}, {"chargeID": 1337276994, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337276983, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497928, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1348497881, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276994, "paymentMap": []}, {"chargeID": 1337276995, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337276983, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-08T10:02:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497929, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1348497881, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:05", "billDate": "2025-05-16T06:17:02", "desc": "MLIN: Standard meal", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337276995, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 2, "charges": [{"chargeID": 1336545845, "codeType": "NCFB", "amt": 110, "curr": "AED", "originalAmt": 110, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-07T19:24:47", "billDate": "2025-05-07T19:26:53", "desc": "Special Service Request", "comment": "NAME CHANGE FEE 2 CHAR", "reasonID": 12, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336545845:*********", "paymentID": *********, "amt": 110, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-07T19:26:53"}, {"chargeID": 1337277028, "codeType": "INSU", "taxChargeID": 1337277000, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "INSU", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461801, "paymentMap": [{"key": "1337277028:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1336461801, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461801:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1337277013, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337277000, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passenger Service Charge", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461779, "paymentMap": [{"key": "1337277013:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1337277021, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337277000, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passenger Service Charge (Intl)", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461778, "paymentMap": [{"key": "1337277021:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1337277027, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337277000, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passenger Facilities Charge.", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461780, "paymentMap": [{"key": "1337277027:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1337277001, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337277000, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:05", "desc": "YQ - DUMMY", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461777, "paymentMap": [{"key": "1337277001:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1337277004, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337277000, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:05", "desc": "Advanced passenger information fee", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461775, "paymentMap": [{"key": "1337277004:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1337277036, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337277000, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passengers Security & Safety Service Fees", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461781, "paymentMap": [{"key": "1337277036:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1336461780, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336461774, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461780:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1336461778, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336461774, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461778:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1336461779, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1336461774, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461779:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1336461775, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336461774, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461775:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336461777, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336461774, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461777:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1336461781, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336461774, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461781:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337277000, "codeType": "AIR", "amt": -640, "curr": "AED", "originalAmt": -640, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T10:02:05", "desc": "WEB:AIR", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461774, "paymentMap": [{"key": "1337277000:*********", "paymentID": *********, "amt": -640, "approveCode": 0}]}, {"chargeID": 1336461774, "codeType": "AIR", "amt": 640, "curr": "AED", "originalAmt": 640, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T18:10:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461774:*********", "paymentID": *********, "amt": 640, "approveCode": 0}]}, {"chargeID": 1337277012, "codeType": "SPST", "taxChargeID": 1337277000, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "SPST", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461802, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1336461802, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181012", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1337277005, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337277000, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:05", "desc": "Included seat", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461782, "paymentMap": []}, {"chargeID": 1336461782, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1336461774, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337277006, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337277000, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "30kg BAG INCLUDED IN FARE", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461776, "paymentMap": []}, {"chargeID": 1336461776, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1336461774, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337277002, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337277000, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:05", "desc": "Standard meal", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461808, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1336461808, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181012"}]}, {"recNum": 4, "charges": [{"chargeID": 1337277080, "codeType": "INSU", "taxChargeID": 1337277041, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "INSU", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461804, "paymentMap": [{"key": "1337277080:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-08T10:02:06"}, {"chargeID": 1336461804, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461804:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1337277084, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1337277041, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passenger Facility Charge.", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461788, "paymentMap": [{"key": "1337277084:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1337277085, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1337277041, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passenger safety and security fees (PSSF)", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461791, "paymentMap": [{"key": "1337277085:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1337277086, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337277041, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Advanced passenger information fee", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461785, "paymentMap": [{"key": "1337277086:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1337277068, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337277041, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "YQ - DUMMY", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461789, "paymentMap": [{"key": "1337277068:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1337277075, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337277041, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Passenger Service Charge", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461790, "paymentMap": [{"key": "1337277075:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1337277077, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1337277041, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Airport Fee.", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461786, "paymentMap": [{"key": "1337277077:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1336461789, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336461784, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461789:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1336461790, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1336461784, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461790:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1336461786, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1336461784, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461786:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1336461785, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336461784, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461785:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336461788, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1336461784, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461788:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1336461791, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1336461784, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461791:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337277041, "codeType": "AIR", "amt": -595, "curr": "AED", "originalAmt": -595, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T10:02:06", "desc": "WEB:AIR", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461784, "paymentMap": [{"key": "1337277041:*********", "paymentID": *********, "amt": -595, "approveCode": 0}]}, {"chargeID": 1336461784, "codeType": "AIR", "amt": 595, "curr": "AED", "originalAmt": 595, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T18:10:12", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336461784:*********", "paymentID": *********, "amt": 595, "approveCode": 0}]}, {"chargeID": 1337277078, "codeType": "SPST", "taxChargeID": 1337277041, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "SPST", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461805, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1336461805, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181022", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1337277083, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337277041, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Included seat", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461792, "paymentMap": []}, {"chargeID": 1336461792, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1336461784, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337277081, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337277041, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "30kg BAG INCLUDED IN FARE", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461787, "paymentMap": []}, {"chargeID": 1336461787, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1336461784, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1337277082, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337277041, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T10:02:06", "desc": "Standard meal", "comment": "JJ No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336461809, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1336461809, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T18:10:12", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 7, "charges": [{"chargeID": 1337277098, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:22", "billDate": "2025-05-08T10:02:07", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277098:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":19.44,\"tax\":0.93,\"currency\":\"USD\",\"segPaxCount\":4}", "ChargeBookDate": "2025-05-08T10:02:07"}, {"chargeID": 1337277088, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1337277087, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:06", "billDate": "2025-05-08T10:02:06", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277088:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1337277089, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1337277087, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:06", "billDate": "2025-05-08T10:02:07", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277089:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337277090, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337277087, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277090:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337277091, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337277087, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277091:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337277092, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1337277087, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277092:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1337277093, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337277087, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277093:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1348497947, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1348497936, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "YQ: YQ - DUMMY", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277093, "paymentMap": [{"key": "1348497947:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1348497948, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1348497936, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "AE: Passenger Service Charge (Intl)", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277092, "paymentMap": [{"key": "1348497948:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1348497949, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1348497936, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "PZ: Passenger Service Charge", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277091, "paymentMap": [{"key": "1348497949:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1348497950, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1348497936, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "ZR: Advanced passenger information fee", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277090, "paymentMap": [{"key": "1348497950:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348497951, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1348497936, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:06", "billDate": "2025-05-16T06:17:03", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277089, "paymentMap": [{"key": "1348497951:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348497952, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1348497936, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:06", "billDate": "2025-05-16T06:17:03", "desc": "F6: Passenger Facilities Charge.", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277088, "paymentMap": [{"key": "1348497952:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1337277087, "codeType": "AIR", "amt": 640, "curr": "AED", "originalAmt": 640, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:06", "billDate": "2025-05-08T10:02:06", "desc": "FZ 017 DXB-DOH 17May2025 Sat 15:55 16:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1337277087:*********", "paymentID": *********, "amt": 640, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497936, "codeType": "AIR", "amt": -640, "curr": "AED", "originalAmt": -640, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:06", "billDate": "2025-05-16T06:17:02", "desc": "FZ 017 DXB-DOH 17May2025 Sat 15:55 16:05\r\n", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277087, "paymentMap": [{"key": "1348497936:*********", "paymentID": *********, "amt": -640, "approveCode": 0}]}, {"chargeID": 1337277097, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277097:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1337277099, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:55", "billDate": "2025-05-08T10:02:07", "desc": "Special Service Request:SPST-14B", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nSPECIAL SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181012", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497943, "codeType": "SPST", "taxChargeID": 1348497936, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:55", "billDate": "2025-05-16T06:17:03", "desc": "Special Service Request:SPST-14B", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277099, "paymentMap": [], "PFID": "181012"}, {"chargeID": 1337277096, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337277087, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497944, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1348497936, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "INST: Included seat", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277096, "paymentMap": []}, {"chargeID": 1337277094, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337277087, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497946, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1348497936, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277094, "paymentMap": []}, {"chargeID": 1337277095, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337277087, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181012", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497945, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1348497936, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "MLIN: Standard meal", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277095, "paymentMap": [], "PFID": "181012"}]}, {"recNum": 8, "charges": [{"chargeID": 1337277111, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:22", "billDate": "2025-05-08T10:02:08", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277111:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "1337277111:*********", "paymentID": *********, "amt": 7.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":19.44,\"tax\":0.93,\"currency\":\"USD\",\"segPaxCount\":4}", "ChargeBookDate": "2025-05-08T10:02:08"}, {"chargeID": 1337277101, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1337277100, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277101:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1337277102, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1337277100, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "QA: Airport Fee.", "comment": "QA: Airport Fee.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277102:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1337277103, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1337277100, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "R9: Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277103:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337277104, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1337277100, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277104:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1337277105, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1337277100, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "G4: Passenger Facility Charge.", "comment": "G4: Passenger Facility Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277105:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1337277106, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1337277100, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277106:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1348497957, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1348497953, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "YQ: YQ - DUMMY", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277106, "paymentMap": [{"key": "1348497957:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1348497959, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1348497953, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "PZ: Passenger Service Charge", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277104, "paymentMap": [{"key": "1348497959:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1348497960, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1348497953, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277103, "paymentMap": [{"key": "1348497960:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1348497961, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1348497953, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "QA: Airport Fee.", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277102, "paymentMap": [{"key": "1348497961:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1348497962, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1348497953, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "ZR: Advanced passenger information fee", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277101, "paymentMap": [{"key": "1348497962:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348497963, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1348497953, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "G4: Passenger Facility Charge.", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277105, "paymentMap": [{"key": "1348497963:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1337277100, "codeType": "AIR", "amt": 605, "curr": "AED", "originalAmt": 605, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "FZ 018 DOH-DXB 18May2025 Sun 17:05 19:20\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1337277100:*********", "paymentID": *********, "amt": 605, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497953, "codeType": "AIR", "amt": -605, "curr": "AED", "originalAmt": -605, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:03", "desc": "FZ 018 DOH-DXB 18May2025 Sun 17:05 19:20\r\n", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277100, "paymentMap": [{"key": "1348497953:*********", "paymentID": *********, "amt": -605, "approveCode": 0}]}, {"chargeID": 1337277110, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1337277110:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1337277112, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:59", "billDate": "2025-05-08T10:02:08", "desc": "Special Service Request:SPST-14B", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nSPECIAL SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497958, "codeType": "SPST", "taxChargeID": 1348497953, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T09:59:59", "billDate": "2025-05-16T06:17:04", "desc": "Special Service Request:SPST-14B", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277112, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1337277109, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1337277100, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497954, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1348497953, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "INST: Included seat", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277109, "paymentMap": []}, {"chargeID": 1337277107, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1337277100, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497956, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1348497953, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277107, "paymentMap": []}, {"chargeID": 1337277108, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1337277100, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-08T10:02:07", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1348497955, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1348497953, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-08T10:02:07", "billDate": "2025-05-16T06:17:04", "desc": "MLIN: Standard meal", "comment": "XX", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1337277108, "paymentMap": [], "PFID": "181022"}]}], "parentPNRs": [], "childPNRs": []}