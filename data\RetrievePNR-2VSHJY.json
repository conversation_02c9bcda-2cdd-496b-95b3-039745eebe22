{"seriesNum": "299", "PNR": "2VSHJY", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82761213", "bookDate": "2025-05-12T14:32:00", "modifyDate": "2025-05-20T03:03:20", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "9368274405924c6vvfs9vbuau6ta1a6cueo24ead04df", "securityGUID": "9368274405924c6vvfs9vbuau6ta1a6cueo24ead04df", "lastLoadGUID": "3fbed1e0-823f-49fc-9215-9433a0a07fcb", "isAsyncPNR": false, "MasterPNR": "2VSHJY", "segments": [{"segKey": "16827898:16827898:5/20/2025 8:55:00 AM", "LFID": 16827898, "depDate": "2025-05-20T00:00:00", "flightGroupId": "16827898", "org": "DXB", "dest": "TLV", "depTime": "2025-05-20T08:55:00", "depTimeGMT": "2025-05-20T04:55:00", "arrTime": "2025-05-20T11:30:00", "operCarrier": "FZ", "operFlightNum": "1635", "mrktCarrier": "FZ ", "mrktFlightNum": "1635", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 186157, "depDate": "2025-05-20T08:55:00", "legKey": "16827898:186157:5/20/2025 8:55:00 AM", "customerKey": "07296AF1C903FEE544E0A12720CEE4A44922A706BC0188328A7901B36AE515D5"}], "active": true}, {"segKey": "16659885:16659885:5/22/2025 12:05:00 PM", "LFID": 16659885, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16659885", "org": "DXB", "dest": "TLV", "depTime": "2025-05-22T12:05:00", "depTimeGMT": "2025-05-22T08:05:00", "arrTime": "2025-05-22T14:35:00", "operCarrier": "FZ", "operFlightNum": "1081", "mrktCarrier": "FZ ", "mrktFlightNum": "1081", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 185104, "depDate": "2025-05-22T12:05:00", "legKey": "16659885:185104:5/22/2025 12:05:00 PM", "customerKey": "12B087EE6FEC8D7E74653384DFE86EBB812BFA001F1DCBD7FADE3AEBE7E23C68"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268725432, "fName": "TIYA", "lName": "MAJID", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682205710007780000002733#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-12T14:32:00"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/18/2025 8:07:21 AM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "VSSCU-DTK3C-INS", "insuTransID": "VSSCU-DTK3C-INS/9b3b7dfb-49c3-4e83-ad9d-cacf36ef5dbf", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6829950b000777000000e14d#268725432#1#MOBILE#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 12, "bookDate": "2025-05-18T08:07:20"}]}], "payments": [{"paymentID": *********, "paxID": 268725432, "method": "VCHR", "status": "1", "paidDate": "2025-05-18T08:07:19", "voucherNum": 3261732, "paidCurr": "AED", "paidAmt": -147.3, "baseCurr": "AED", "baseAmt": -147.3, "userID": "MOBILE_APP", "channelID": 12, "voucherNumFull": "X9HN8T", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 268725634, "method": "VISA", "status": "1", "paidDate": "2025-05-12T14:33:16", "cardNum": "************6829", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1403.89, "baseCurr": "AED", "baseAmt": 1403.89, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "034931", "reference": "22993304", "externalReference": "22993304", "tranId": "21363599", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21363599}], "OAFlights": null, "physicalFlights": [{"key": "16827898:186157:2025-05-20T08:55:00 AM", "LFID": 16827898, "PFID": 186157, "org": "DXB", "dest": "TLV", "depDate": "2025-05-20T08:55:00", "depTime": "2025-05-20T08:55:00", "arrTime": "2025-05-20T11:30:00", "carrier": "FZ", "flightNum": "1635", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1635", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12900, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false}, {"key": "16659885:185104:2025-05-22T12:05:00 PM", "LFID": 16659885, "PFID": 185104, "org": "DXB", "dest": "TLV", "depDate": "2025-05-22T12:05:00", "depTime": "2025-05-22T12:05:00", "arrTime": "2025-05-22T14:35:00", "carrier": "FZ", "flightNum": "1081", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1081", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false, "changeType": "AC", "flightChangeTime": "5/7/2025 6:36:07 AM"}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1350964254, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-18T08:07:21", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350964254:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-18T08:07:21"}, {"chargeID": 1350963787, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1350963786, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350963787:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1350963788, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1350963786, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350963788:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1350963789, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1350963786, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350963789:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1350963790, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1350963786, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350963790:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1350963791, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1350963786, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350963791:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1350963786, "codeType": "AIR", "amt": 870, "curr": "AED", "originalAmt": 870, "originalCurr": "AED", "status": 1, "billDate": "2025-05-18T08:07:21", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350963786:*********", "paymentID": *********, "amt": 870, "approveCode": 0}]}, {"chargeID": 1350963793, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1350963786, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1350963792, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1350963786, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1350963806, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186157"}, {"chargeID": 1353403383, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-20T03:03:20", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186157", "ssrCommentId": "*********"}]}, {"recNum": 1, "charges": [{"chargeID": 1350963782, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342911627, "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911652, "paymentMap": [{"key": "1350963782:*********", "paymentID": *********, "amt": -180, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-18T08:07:21"}, {"chargeID": 1350963783, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342911627, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911651, "paymentMap": [{"key": "1350963783:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1350963784, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342911627, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911628, "paymentMap": [{"key": "1350963784:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1350963780, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342911627, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911649, "paymentMap": [{"key": "1350963780:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1350963779, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342911627, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911650, "paymentMap": [{"key": "1350963779:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1342911650, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342911627, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911650:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342911651, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342911627, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911651:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342911649, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342911627, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911649:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342911652, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342911627, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911652:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1342911628, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342911627, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911628:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1350963785, "codeType": "AIR", "amt": -865, "curr": "AED", "originalAmt": -865, "originalCurr": "AED", "status": 0, "billDate": "2025-05-18T08:07:21", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911627, "paymentMap": [{"key": "1350963785:*********", "paymentID": *********, "amt": -147.3, "approveCode": 0}, {"key": "1350963785:*********", "paymentID": *********, "amt": -717.7, "approveCode": 0}]}, {"chargeID": 1342911627, "codeType": "AIR", "amt": 865, "curr": "AED", "originalAmt": 865, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T14:32:00", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911627:*********", "paymentID": *********, "amt": 865, "approveCode": 0}]}, {"chargeID": 1342918450, "codeType": "PMNT", "amt": 40.89, "curr": "AED", "originalAmt": 40.89, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T14:33:20", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342918450:*********", "paymentID": *********, "amt": 40.89, "approveCode": 0}]}, {"chargeID": 1350963781, "codeType": "XLGR", "amt": -188, "curr": "AED", "originalAmt": -188, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T08:07:21", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342911657, "paymentMap": [{"key": "1350963781:*********", "paymentID": *********, "amt": -188, "approveCode": 0}], "PFID": "185104"}, {"chargeID": 1342911657, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::185104", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342911657:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "185104"}, {"chargeID": 1342911654, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342911627, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342911653, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342911627, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342911658, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T14:32:00", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185104"}]}], "parentPNRs": [], "childPNRs": []}