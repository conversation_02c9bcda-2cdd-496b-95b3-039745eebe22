{"seriesNum": "299", "PNR": "VDJZE7", "bookAgent": "WEB_MOBILE", "resCurrency": "USD", "PNRPin": "83189636", "bookDate": "2025-05-27T23:00:50", "modifyDate": "2025-05-27T23:05:29", "resType": "MOBILE", "resBalance": 0, "timeLimitGMT": "2025-05-28T23:00:50", "timeLimitODT": "2025-05-29T02:00:50", "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "0092a44f37d74ap1n5x9bc43ca60tdl0g1u4f483c748", "securityGUID": "0092a44f37d74ap1n5x9bc43ca60tdl0g1u4f483c748", "lastLoadGUID": "339b0f61-c963-436d-9a83-9c40cdca13d5", "isAsyncPNR": false, "MasterPNR": "VDJZE7", "segments": [{"segKey": "16865820:16865820:6/9/2025 10:40:00 PM", "LFID": 16865820, "depDate": "2025-06-09T00:00:00", "flightGroupId": "16865820", "org": "BEY", "dest": "RUH", "depTime": "2025-06-09T22:40:00", "depTimeGMT": "2025-06-09T19:40:00", "arrTime": "2025-06-10T05:10:00", "operCarrier": "FZ", "operFlightNum": "160/849", "mrktCarrier": "FZ ", "mrktFlightNum": "160/849", "persons": [{"recNum": 1, "status": 1}], "legDetails": [{"PFID": 181104, "depDate": "2025-06-09T22:40:00", "legKey": "16865820:181104:6/9/2025 10:40:00 PM", "customerKey": "CB3BE0B0C8BF642B4C341427E7DCD5988B2AFD3018D1AA6119BBEE2C4C795A15"}, {"PFID": 186002, "depDate": "2025-06-10T04:10:00", "legKey": "16865820:186002:6/10/2025 4:10:00 AM", "customerKey": "E401C0B60E5E84C65BE3FF770060F85959A0488B139177720DEE2CA62BB4925C"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 270378301, "fName": "MOHAMAD", "lName": "ALATIK", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1994-02-21T00:00:00", "nationality": "422", "recNum": [1]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 11:00:50 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KOL7LB2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "XYYXK-X78H8-INS/4ce6b145-5c2e-4a45-b625-274b5b5eccf2", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683640f000077800000004af#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-27T23:00:50"}]}], "payments": [{"paymentID": *********, "paxID": 270378417, "method": "MSCD", "status": "1", "paidDate": "2025-05-27T23:05:27", "cardNum": "************4694", "gateway": "EPS", "paidCurr": "SAR", "paidAmt": 916.75, "baseCurr": "USD", "baseAmt": 239.29, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "282769", "reference": "23290731", "externalReference": "23290731", "tranId": "21671413", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "MCCMPGSPESAR", "exchangeRate": "3.831120", "resExternalPaymentID": 21671413}], "OAFlights": null, "physicalFlights": [{"key": "16865820:181104:2025-06-09T10:40:00 PM", "LFID": 16865820, "PFID": 181104, "org": "BEY", "dest": "DXB", "depDate": "2025-06-09T22:40:00", "depTime": "2025-06-09T22:40:00", "arrTime": "2025-06-10T03:10:00", "carrier": "FZ", "flightNum": "160", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "160", "flightStatus": "OPEN", "originMetroGroup": "BEY", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Beirut", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "4/7/2025 12:39:37 PM"}, {"key": "16865820:186002:2025-06-10T04:10:00 AM", "LFID": 16865820, "PFID": 186002, "org": "DXB", "dest": "RUH", "depDate": "2025-06-10T04:10:00", "depTime": "2025-06-10T04:10:00", "arrTime": "2025-06-10T05:10:00", "carrier": "FZ", "flightNum": "849", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "849", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "RUH", "operatingCarrier": "FZ", "flightDuration": 7200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Riyadh", "isActive": true, "changeType": "AC", "flightChangeTime": "4/7/2025 12:39:37 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1364908362, "codeType": "INSU", "amt": 9.72, "curr": "USD", "originalAmt": 9.72, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908362:*********", "paymentID": *********, "amt": 9.72, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"1.0\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-27T23:00:50"}, {"chargeID": 1364908352, "codeType": "TAX", "taxID": 11689, "taxCode": "LB", "taxChargeID": 1364908351, "amt": 35, "curr": "USD", "originalAmt": 35, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "Airport Embarkation Tax", "comment": "Airport Embarkation Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908352:*********", "paymentID": *********, "amt": 35, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364908357, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364908351, "amt": 51, "curr": "USD", "originalAmt": 51, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908357:*********", "paymentID": *********, "amt": 51, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364908355, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364908351, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908355:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364908354, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364908351, "amt": 12.3, "curr": "USD", "originalAmt": 12.3, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908354:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364908353, "codeType": "TAX", "taxID": 9286, "taxCode": "H4", "taxChargeID": 1364908351, "amt": 0.77, "curr": "USD", "originalAmt": 0.77, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "API/PNR Security Charge", "comment": "API/PNR Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908353:*********", "paymentID": *********, "amt": 0.77, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364908356, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1364908351, "amt": 2.13, "curr": "USD", "originalAmt": 2.13, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908356:*********", "paymentID": *********, "amt": 2.13, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364908351, "codeType": "AIR", "amt": 120, "curr": "USD", "originalAmt": 120, "originalCurr": "USD", "status": 1, "billDate": "2025-05-27T23:00:50", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364908351:*********", "paymentID": *********, "amt": 120, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364913548, "codeType": "PMNT", "amt": 6.97, "curr": "USD", "originalAmt": 6.97, "originalCurr": "USD", "status": 1, "billDate": "2025-05-27T23:05:29", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364913548:*********", "paymentID": *********, "amt": 6.97, "approveCode": 0}]}, {"chargeID": 1364908358, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1364908351, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364908364, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186002"}, {"chargeID": 1364908363, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:00:50", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181104"}]}], "parentPNRs": [], "childPNRs": []}