export interface PNRRecord {
  pnr: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  result?: ProcessingResult;
}

export interface ProcessingResult {
  pnrNumber: string;
  policyId?: string;
  insuranceRecords: InsuranceRecord[];
  policyStartDate?: string;
  policyEndDate?: string;
  summary: {
    totalRecords: number;
    withConfirmation: number;
    missingConfirmation: number;
    withinPolicyPeriod: number;
  };
  sqlQueries: SqlQuery[];
  error?: string;
  errorType?: string;
}

export interface InsuranceRecord {
  recordNumber: number;
  insuTransID: string;
  insuConfNum?: string | null;
  statusCode: number;
  statusText: string;
  channelCode: number;
  channelText: string;
  provider: string;
  insuPurchaseDate?: string | null;
  paxId: string;
  passengerName: string;
  bookDate?: string;
  hasConfirmation: boolean;
  segmentInfo?: {
    origin: string;
    destination: string;
    departureTime: string;
    arrivalTime: string;
    flightNumber: string;
  } | null;
  departureDate?: string | null;
  withinPolicyPeriod: boolean | string;
  matchesCoverGeniusPolicyId: boolean;
}

export interface SqlQuery {
  recordNumber: number;
  passenger: string;
  query: string;
}

export interface BatchProcessingState {
  pnrs: PNRRecord[];
  isProcessing: boolean;
  completedCount: number;
  totalCount: number;
}
