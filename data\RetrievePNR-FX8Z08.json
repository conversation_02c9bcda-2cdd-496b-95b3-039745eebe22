{"seriesNum": "299", "PNR": "FX8Z08", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82719514", "bookDate": "2025-05-11T07:33:49", "modifyDate": "2025-05-12T05:30:34", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "3120985o3d6a3490gbiam0zc48w8i4t9i360u6213afa", "securityGUID": "3120985o3d6a3490gbiam0zc48w8i4t9i360u6213afa", "lastLoadGUID": "d1f829ed-c6af-40b6-8f8f-eb52fed97736", "isAsyncPNR": false, "MasterPNR": "FX8Z08", "segments": [{"segKey": "16087259:16087259:5/12/2025 10:55:00 AM", "LFID": 16087259, "depDate": "2025-05-12T00:00:00", "flightGroupId": "16087259", "org": "DOH", "dest": "DXB", "depTime": "2025-05-12T10:55:00", "depTimeGMT": "2025-05-12T07:55:00", "arrTime": "2025-05-12T13:10:00", "operCarrier": "FZ", "operFlightNum": "004", "mrktCarrier": "FZ ", "mrktFlightNum": "004", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181009, "depDate": "2025-05-12T10:55:00", "legKey": "16087259:181009:5/12/2025 10:55:00 AM", "customerKey": "A6152F1D08737E15BA707E423AB4E7739AEE6A2342243CF8477F5C711F756E10"}], "active": true}, {"segKey": "16087260:16087260:5/14/2025 5:20:00 PM", "LFID": 16087260, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087260", "org": "DXB", "dest": "DOH", "depTime": "2025-05-14T17:20:00", "depTimeGMT": "2025-05-14T13:20:00", "arrTime": "2025-05-14T17:30:00", "operCarrier": "FZ", "operFlightNum": "009", "mrktCarrier": "FZ ", "mrktFlightNum": "009", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181011, "depDate": "2025-05-14T17:20:00", "legKey": "16087260:181011:5/14/2025 5:20:00 PM", "customerKey": "44E073E9DB5157C952A411E3E0D85881CB699C6D07D57DAACCFE9F60FF6A6120"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268570319, "fName": "SANTHOSH", "lName": "VASU", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1981-02-09T00:00:00", "FFNum": "767348013", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "Q", "status": 5, "fareClass": "Q", "operFareClass": "Q", "FBC": "QR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "emergencyContactID": 268570681, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682051a7000777000000c0a0#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 2, "bookDate": "2025-05-11T07:33:49"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/11/2025 7:31:50 PM", "provider": "<PERSON>", "status": 5, "fareClass": "Q", "operFareClass": "Q", "FBC": "QR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "RGH8E-WPVM7-INS/8f6a79a5-b41b-4dc3-9cc7-4ff08270deec", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682051a7000777000000c0a0#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-11T07:33:49"}]}], "payments": [{"paymentID": *********, "paxID": 268570319, "method": "DCSH", "status": "1", "paidDate": "2025-05-12T05:30:33", "IATANum": "95093364", "paidCurr": "QAR", "paidAmt": 440, "baseCurr": "QAR", "baseAmt": 440, "userID": "ardil.ahmed", "channelID": 19, "correlationId": "3accfscf5e3ck4y4vajbu9zbxd6206vf4754f87d479d", "tierID": "3", "POSAirport": "DOH", "workStationID": "DOH1CKE004", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1", "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 268570349, "method": "VISA", "status": "1", "paidDate": "2025-05-11T07:34:18", "cardNum": "************2350", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1030, "baseCurr": "QAR", "baseAmt": 1030, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "santhosh vasu", "authCode": "038830", "reference": "22966904", "externalReference": "22966904", "tranId": "21334728", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21334728}, {"paymentID": *********, "paxID": 268630546, "method": "VISA", "status": "1", "paidDate": "2025-05-11T19:32:18", "cardNum": "************2350", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 57.05, "baseCurr": "QAR", "baseAmt": 57.05, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "018838", "reference": "22978134", "externalReference": "22978134", "tranId": "21346799", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21346799}], "OAFlights": null, "physicalFlights": [{"key": "16087259:181009:2025-05-12T10:55:00 AM", "LFID": 16087259, "PFID": 181009, "org": "DOH", "dest": "DXB", "depDate": "2025-05-12T10:55:00", "depTime": "2025-05-12T10:55:00", "arrTime": "2025-05-12T13:10:00", "carrier": "FZ", "flightNum": "004", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "004", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087260:181011:2025-05-14T05:20:00 PM", "LFID": 16087260, "PFID": 181011, "org": "DXB", "dest": "DOH", "depDate": "2025-05-14T17:20:00", "depTime": "2025-05-14T17:20:00", "arrTime": "2025-05-14T17:30:00", "carrier": "FZ", "flightNum": "009", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "009", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:35:50 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1341900597, "codeType": "XBAG", "amt": 400, "curr": "QAR", "originalAmt": 400, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-12T05:29:46", "comment": "40.00", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "chargeSequence": "1412801301991", "paymentRefStatus": 1, "paymentMap": [{"key": "1341900597:*********", "paymentID": *********, "amt": 400, "approveCode": 0}], "PFID": "181009", "POSAirport": "DOH", "bonusMiles": 0, "bonusTierMiles": 0, "workStationID": "DOH1CKE004", "promoMiles": 0, "promoTierMiles": 0, "isSSR": true, "parameter1Name": "BATCH_ID", "parameter1Value": "612f5r3971ce44615936k2yfv0ybubxaf66d0av1434a", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-12T10:55:00\",\"fltNum\":\"004\",\"depDate\":\"2025-05-12T00:00:00\",\"board\":\"DOH\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1144503749_DOHDXBDXB_XBAG", "ChargeBookDate": "2025-05-12T05:29:46"}, {"chargeID": 1340868343, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340868342, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868343:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340868345, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1340868342, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868345:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340868346, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340868342, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868346:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1340868347, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1340868342, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868347:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340868344, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1340868342, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868344:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340868348, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1340868342, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868348:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340868342, "codeType": "AIR", "amt": 305, "curr": "QAR", "originalAmt": 305, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-11T07:33:49", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1340868342:*********", "paymentID": *********, "amt": 305, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1340870486, "codeType": "PMNT", "amt": 30, "curr": "QAR", "originalAmt": 30, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-11T07:34:22", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340870486:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1341680117, "codeType": "PMNT", "amt": 1.66, "curr": "QAR", "originalAmt": 1.66, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-11T19:32:23", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341680117:*********", "paymentID": *********, "amt": 1.66, "approveCode": 0}]}, {"chargeID": 1341900599, "codeType": "GHA", "taxChargeID": 1341900597, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-12T05:29:46", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "chargeSequence": "1412801301991", "paymentRefStatus": 1, "paymentMap": [{"key": "1341900599:*********", "paymentID": *********, "amt": 40, "approveCode": 0}], "PFID": "181009", "POSAirport": "DOH", "bonusMiles": 0, "bonusTierMiles": 0, "workStationID": "DOH1CKE004", "promoMiles": 0, "promoTierMiles": 0, "parameter1Name": "BATCH_ID", "parameter1Value": "612f5r3971ce44615936k2yfv0ybubxaf66d0av1434a", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-12T10:55:00\",\"fltNum\":\"004\",\"depDate\":\"2025-05-12T00:00:00\",\"board\":\"DOH\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1144503749_DOHDXBDXB_XBAG"}]}, {"recNum": 2, "charges": [{"chargeID": 1341678173, "codeType": "INSU", "amt": 35.39, "curr": "QAR", "originalAmt": 35.39, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-11T19:31:50", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341678173:*********", "paymentID": *********, "amt": 35.39, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-11T19:31:50"}, {"chargeID": 1340868394, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340868389, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868394:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1340868391, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340868389, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868391:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1340868392, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1340868389, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868392:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1340868393, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1340868389, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868393:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340868395, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1340868389, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868395:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340868390, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340868389, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-11T07:33:49", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340868390:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1340868389, "codeType": "AIR", "amt": 305, "curr": "QAR", "originalAmt": 305, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-11T07:33:49", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1340868389:*********", "paymentID": *********, "amt": 305, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1341678165, "codeType": "BAGB", "amt": 20, "curr": "QAR", "originalAmt": 20, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-11T19:31:50", "desc": "BAGB", "comment": "FLXID:GCC-AE DXB-BAH/DOH:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1341678165:*********", "paymentID": *********, "amt": 20, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}