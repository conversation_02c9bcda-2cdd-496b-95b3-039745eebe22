{"seriesNum": "299", "PNR": "CZ5TLK", "bookAgent": "WEB_MOBILE", "resCurrency": "QAR", "PNRPin": "83212768", "bookDate": "2025-05-28T15:02:56", "modifyDate": "2025-05-28T17:31:58", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "9fc46e81f5b6419vsf58z2lau2s4y562lfs44bd0f195", "securityGUID": "9fc46e81f5b6419vsf58z2lau2s4y562lfs44bd0f195", "lastLoadGUID": "d1b69dd1-d4f0-4987-b9d0-d383f9e63d0d", "isAsyncPNR": false, "MasterPNR": "CZ5TLK", "segments": [{"segKey": "16107502:16107502:7/1/2025 8:50:00 PM", "LFID": 16107502, "depDate": "2025-07-01T00:00:00", "flightGroupId": "16107502", "org": "DOH", "dest": "ZNZ", "depTime": "2025-07-01T20:50:00", "depTimeGMT": "2025-07-01T17:50:00", "arrTime": "2025-07-02T06:10:00", "operCarrier": "FZ", "operFlightNum": "006/1259", "mrktCarrier": "FZ ", "mrktFlightNum": "006/1259", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181013, "depDate": "2025-07-01T20:50:00", "legKey": "16107502:181013:7/1/2025 8:50:00 PM", "customerKey": "04585C59F8A184E1B544F03A5B6B4241FF471F3508D6123E0B813C4C7048CA73"}, {"PFID": 181507, "depDate": "2025-07-02T01:45:00", "legKey": "16107502:181507:7/2/2025 1:45:00 AM", "customerKey": "2356E62C1B4984D618F0B2643498ACC6184F72C5FF624866A7F66A729FE130AA"}], "active": true, "changeType": "TK"}, {"segKey": "16107502:16107502:6/10/2025 8:50:00 PM", "LFID": 16107502, "depDate": "2025-06-10T00:00:00", "flightGroupId": "16107502", "org": "DOH", "dest": "ZNZ", "depTime": "2025-06-10T20:50:00", "depTimeGMT": "2025-06-10T17:50:00", "arrTime": "2025-06-11T06:10:00", "operCarrier": "FZ", "operFlightNum": "006/1259", "mrktCarrier": "FZ", "mrktFlightNum": "006/1259", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181013, "depDate": "2025-06-10T20:50:00", "legKey": "16107502:181013:6/10/2025 8:50:00 PM", "customerKey": "AD6E88E258731D43DB014384C38C683FE458E8B4663D8501DC13A331A0500F20"}, {"PFID": 181507, "depDate": "2025-06-11T01:45:00", "legKey": "16107502:181507:6/11/2025 1:45:00 AM", "customerKey": "C1431B9FA21A209BDB9FC04ACC9C2F7393E29BD3DDD1749EE97A97725F4E6118"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270462124, "fName": "RADHIYA", "lName": "MOHAMMED", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/28/2025 3:02:56 PM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROX8QA2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "WG424-G266Q-INS", "insuTransID": "WG424-G266Q-INS/df602c34-ee48-4954-abaf-4e4fba430b14", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683724c60007780000004eba#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-28T15:02:56"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "noon.arbab", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/28/2025 3:02:56 PM", "provider": "<PERSON>", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NOX8QA5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "WG424-G266Q-INS/df602c34-ee48-4954-abaf-4e4fba430b14", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6837472000077800000066b0#270462124#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-28T17:29:19"}]}], "payments": [{"paymentID": *********, "paxID": 270478339, "method": "IPAY", "status": "1", "paidDate": "2025-05-28T17:31:56", "cardNum": "************3654", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 248.93, "baseCurr": "QAR", "baseAmt": 248.93, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "856783", "reference": "23306468", "externalReference": "23306468", "tranId": "21689555", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21689555}, {"paymentID": *********, "paxID": 270462193, "method": "MSCD", "status": "1", "paidDate": "2025-05-28T15:03:25", "cardNum": "************1823", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1488.81, "baseCurr": "QAR", "baseAmt": 1488.81, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "BADRIYA Al Najjar", "authCode": "425027", "reference": "23304268", "externalReference": "23304268", "tranId": "21686188", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21686188}], "OAFlights": null, "physicalFlights": [{"key": "16107502:181013:2025-06-10T08:50:00 PM", "LFID": 16107502, "PFID": 181013, "org": "DOH", "dest": "DXB", "depDate": "2025-06-10T20:50:00", "depTime": "2025-06-10T20:50:00", "arrTime": "2025-06-10T23:05:00", "carrier": "FZ", "flightNum": "006", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "006", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:20:14 PM"}, {"key": "16107502:181507:2025-06-11T01:45:00 AM", "LFID": 16107502, "PFID": 181507, "org": "DXB", "dest": "ZNZ", "depDate": "2025-06-11T01:45:00", "depTime": "2025-06-11T01:45:00", "arrTime": "2025-06-11T06:10:00", "carrier": "FZ", "flightNum": "1259", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1259", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:20:14 PM"}, {"key": "16107502:181013:2025-07-01T08:50:00 PM", "LFID": 16107502, "PFID": 181013, "org": "DOH", "dest": "DXB", "depDate": "2025-07-01T20:50:00", "depTime": "2025-07-01T20:50:00", "arrTime": "2025-07-01T23:05:00", "carrier": "FZ", "flightNum": "006", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "006", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:20:14 PM"}, {"key": "16107502:181507:2025-07-02T01:45:00 AM", "LFID": 16107502, "PFID": 181507, "org": "DXB", "dest": "ZNZ", "depDate": "2025-07-02T01:45:00", "depTime": "2025-07-02T01:45:00", "arrTime": "2025-07-02T06:10:00", "carrier": "FZ", "flightNum": "1259", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1259", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:20:14 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1366298821, "codeType": "INSU", "taxChargeID": 1366298786, "amt": -35.45, "curr": "QAR", "originalAmt": -35.45, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "INSU", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080762, "paymentMap": [{"key": "1366298821:*********", "paymentID": *********, "amt": -35.45, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-28T17:29:20"}, {"chargeID": 1366080762, "codeType": "INSU", "amt": 35.45, "curr": "QAR", "originalAmt": 35.45, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080762:*********", "paymentID": *********, "amt": 35.45, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1366298816, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366298786, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Passenger Facilities Charge.", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080754, "paymentMap": [{"key": "1366298816:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1366298819, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1366298786, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Passenger safety and security fees (PSSF)", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080752, "paymentMap": [{"key": "1366298819:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1366298827, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1366298786, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Passenger Facility Charge.", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080750, "paymentMap": [{"key": "1366298827:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1366298832, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366298786, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Advanced passenger information fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080755, "paymentMap": [{"key": "1366298832:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1366298840, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1366298786, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Passenger Service Charge", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080753, "paymentMap": [{"key": "1366298840:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1366298850, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1366298786, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Airport Fee.", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080751, "paymentMap": [{"key": "1366298850:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1366298860, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366298786, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "YQ - DUMMY", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080756, "paymentMap": [{"key": "1366298860:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1366080754, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366080749, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080754:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1366080752, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1366080749, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080752:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366080750, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1366080749, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080750:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1366080756, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366080749, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080756:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1366080753, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1366080749, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080753:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366080751, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1366080749, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080751:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1366080755, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366080749, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080755:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366298786, "codeType": "AIR", "amt": -1020, "curr": "QAR", "originalAmt": -1020, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-28T17:29:20", "desc": "WEB:AIR", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080749, "paymentMap": [{"key": "1366298786:*********", "paymentID": *********, "amt": -1020, "approveCode": 0}]}, {"chargeID": 1366080749, "codeType": "AIR", "amt": 1020, "curr": "QAR", "originalAmt": 1020, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-28T15:02:56", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366080749:*********", "paymentID": *********, "amt": 1020, "approveCode": 0}]}, {"chargeID": 1366084717, "codeType": "PMNT", "amt": 43.36, "curr": "QAR", "originalAmt": 43.36, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-28T15:03:26", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366084717:*********", "paymentID": *********, "amt": 43.36, "approveCode": 0}]}, {"chargeID": 1366298857, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1366298786, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Included seat", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080758, "paymentMap": []}, {"chargeID": 1366080758, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1366080749, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1366298852, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1366298786, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "40kg BAG INCLUDED IN FARE", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080757, "paymentMap": []}, {"chargeID": 1366080757, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1366080749, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1366298836, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1366298786, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Standard meal", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080763, "paymentMap": [], "PFID": "181507"}, {"chargeID": 1366298846, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1366298786, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T17:29:20", "desc": "Standard meal", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366080764, "paymentMap": [], "PFID": "181013"}, {"chargeID": 1366080763, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181507"}, {"chargeID": 1366080764, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-28T15:02:56", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181013"}]}, {"recNum": 2, "charges": [{"chargeID": 1366298910, "codeType": "INSU", "amt": 35.45, "curr": "QAR", "originalAmt": 35.45, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:25:55", "billDate": "2025-05-28T17:29:20", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298910:*********", "paymentID": *********, "amt": 35.45, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-28T17:29:20"}, {"chargeID": 1366298868, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1366298864, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "G4: Passenger Facility Charge.", "comment": "G4: Passenger Facility Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298868:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1366298869, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1366298864, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "QA: Airport Fee.", "comment": "QA: Airport Fee.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298869:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1366298874, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1366298864, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "R9: Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298874:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366298878, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1366298864, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298878:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366298879, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366298864, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298879:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1366298883, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366298864, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298883:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366298890, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366298864, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298890:*********", "paymentID": *********, "amt": 45.45, "approveCode": 0}, {"key": "1366298890:*********", "paymentID": *********, "amt": 144.55, "approveCode": 0}]}, {"chargeID": 1366298864, "codeType": "AIR", "amt": 1200, "curr": "QAR", "originalAmt": 1200, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "FZ 006 DOH-DXB 10Jun2025 Tue 20:50 23:05\r\nFZ 1259 DXB-ZNZ 11Jun2025 Wed 01:45 06:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298864:*********", "paymentID": *********, "amt": 1200, "approveCode": 0}]}, {"chargeID": 1366302879, "codeType": "PMNT", "amt": 7.25, "curr": "QAR", "originalAmt": 7.25, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-28T17:31:58", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366302879:*********", "paymentID": *********, "amt": 7.25, "approveCode": 0}]}, {"chargeID": 1366298904, "codeType": "MFEE", "amt": 61.68, "curr": "QAR", "originalAmt": 61.68, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366298904:*********", "paymentID": *********, "amt": 61.68, "approveCode": 0}]}, {"chargeID": 1366298898, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1366298864, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1366298891, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1366298864, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1366298896, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1366298864, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181013"}, {"chargeID": 1366298897, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1366298864, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-28T17:29:20", "billDate": "2025-05-28T17:29:20", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181507"}]}], "parentPNRs": [], "childPNRs": []}