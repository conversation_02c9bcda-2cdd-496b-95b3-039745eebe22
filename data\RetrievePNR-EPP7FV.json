{"seriesNum": "299", "PNR": "EPP7FV", "bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "IATA": "091120DP", "resCurrency": "AED", "PNRPin": "82943337", "bookDate": "2025-05-19T08:28:46", "modifyDate": "2025-06-02T14:37:30", "resType": "STANDARD", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 2, "activeSegCount": 2, "webBookingID": "", "securityGUID": "", "lastLoadGUID": "3696701BDCFB028EE0630A57380A4CBB", "isAsyncPNR": false, "MasterPNR": "EPP7FV", "segments": [{"segKey": "16087793:16087793:6/27/2025 10:45:00 PM", "LFID": 16087793, "depDate": "2025-06-27T00:00:00", "flightGroupId": "16087793", "org": "DXB", "dest": "FRU", "depTime": "2025-06-27T22:45:00", "depTimeGMT": "2025-06-27T18:45:00", "arrTime": "2025-06-28T04:40:00", "operCarrier": "FZ", "operFlightNum": "1689", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "persons": [{"recNum": 6, "status": 1}, {"recNum": 5, "status": 1}], "legDetails": [{"PFID": 181530, "depDate": "2025-06-27T22:45:00", "legKey": "16087793:181530:6/27/2025 10:45:00 PM", "customerKey": "22D386385232AC6DBE9849779255D450DD05CF96B20A6101CAEC20C54EEBBD00"}], "active": true}, {"segKey": "16087793:16087793:6/28/2025 10:45:00 PM", "LFID": 16087793, "depDate": "2025-06-28T00:00:00", "flightGroupId": "16087793", "org": "DXB", "dest": "FRU", "depTime": "2025-06-28T22:45:00", "depTimeGMT": "2025-06-28T18:45:00", "arrTime": "2025-06-29T04:40:00", "operCarrier": "FZ", "operFlightNum": "1689", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "persons": [{"recNum": 3, "status": 0}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181530, "depDate": "2025-06-28T22:45:00", "legKey": "16087793:181530:6/28/2025 10:45:00 PM", "customerKey": "871585F3F6BA745127AD2020E5166408EB4F83AD696393F5ACAC6E22E64CA643"}], "active": true}, {"segKey": "16087815:16087815:7/5/2025 5:50:00 AM", "LFID": 16087815, "depDate": "2025-07-05T00:00:00", "flightGroupId": "16087815", "org": "FRU", "dest": "DXB", "depTime": "2025-07-05T05:50:00", "depTimeGMT": "2025-07-04T23:50:00", "arrTime": "2025-07-05T08:10:00", "operCarrier": "FZ", "operFlightNum": "1690", "mrktCarrier": "FZ", "mrktFlightNum": "1690", "persons": [{"recNum": 4, "status": 1}, {"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181591, "depDate": "2025-07-05T05:50:00", "legKey": "16087815:181591:7/5/2025 5:50:00 AM", "customerKey": "986F79FA6728AE6DACC772626387E38031EF683C41BDD6EC1847BFC71E6E4E26"}], "active": true}], "persons": [{"paxID": 269431126, "fName": "AYESHA", "lName": "AHAMAD", "title": "MRS", "PTCID": 1, "gender": "F", "recNum": [3, 4, 6]}, {"paxID": 269431125, "fName": "DARWISH", "lName": "SHEROOK", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 5]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/19/2025 8:29:37 AM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "S7SLJ-YDZYN-INS/7579082d-9872-4a0a-b5ca-f9f0bc7443b4", "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682aea2c000778000000163f#1#1#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-19T08:28:46"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/19/2025 8:38:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "JMHUE-2TLB4-INS", "insuTransID": "JMHUE-2TLB4-INS/9542e66e-3f7d-46f0-b2e4-d83b6d740636", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682aeda000077800000019c2#269431125#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-19T08:28:46"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/19/2025 8:29:37 AM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "S7SLJ-YDZYN-INS/7579082d-9872-4a0a-b5ca-f9f0bc7443b4", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682aea2c000778000000163f#2#1#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-19T08:28:46"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/19/2025 8:38:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "JMHUE-2TLB4-INS", "insuTransID": "JMHUE-2TLB4-INS/9542e66e-3f7d-46f0-b2e4-d83b6d740636", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682aeda000077800000019c2#269431126#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-19T08:28:46"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/19/2025 8:38:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "JMHUE-2TLB4-INS", "insuTransID": "JMHUE-2TLB4-INS/9542e66e-3f7d-46f0-b2e4-d83b6d740636", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682aeda000077800000019c2#269431125#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-19T08:38:34"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "ab<PERSON><PERSON><PERSON>iqbal", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/19/2025 8:38:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "JMHUE-2TLB4-INS", "insuTransID": "JMHUE-2TLB4-INS/9542e66e-3f7d-46f0-b2e4-d83b6d740636", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682aeda000077800000019c2#269431126#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-19T08:38:34"}]}], "payments": [{"paymentID": *********, "paxID": 269433979, "method": "RPOS", "status": "1", "paidDate": "2025-05-19T08:47:53", "paidCurr": "AED", "paidAmt": 3100.3, "baseCurr": "AED", "baseAmt": 3100.3, "userID": "cashier.nib1", "channelID": 1, "paymentComment": "23285", "authCode": "362198", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "16087793:181530:2025-06-27T10:45:00 PM", "LFID": 16087793, "PFID": 181530, "org": "DXB", "dest": "FRU", "depDate": "2025-06-27T22:45:00", "depTime": "2025-06-27T22:45:00", "arrTime": "2025-06-28T04:40:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": true}, {"key": "16087793:181530:2025-06-28T10:45:00 PM", "LFID": 16087793, "PFID": 181530, "org": "DXB", "dest": "FRU", "depDate": "2025-06-28T22:45:00", "depTime": "2025-06-28T22:45:00", "arrTime": "2025-06-29T04:40:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": true}, {"key": "16087815:181591:2025-07-05T05:50:00 AM", "LFID": 16087815, "PFID": 181591, "org": "FRU", "dest": "DXB", "depDate": "2025-07-05T05:50:00", "depTime": "2025-07-05T05:50:00", "arrTime": "2025-07-05T08:10:00", "carrier": "FZ", "flightNum": "1690", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1690", "flightStatus": "OPEN", "originMetroGroup": "FRU", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 15600, "reaccomChangeAlert": false, "originName": "Bishkek", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": 1352203431, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:29:30", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352203431:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-19T08:29:30"}, {"chargeID": 1352222417, "codeType": "INSU", "taxChargeID": 1352222416, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:38:36", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352203431, "paymentMap": [{"key": "1352222417:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1352222422, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352222416, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201509, "paymentMap": [{"key": "1352222422:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1352222423, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352222416, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201508, "paymentMap": [{"key": "1352222423:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352222424, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352222416, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201507, "paymentMap": [{"key": "1352222424:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352222425, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352222416, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201506, "paymentMap": [{"key": "1352222425:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1352222426, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352222416, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201505, "paymentMap": [{"key": "1352222426:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1352201505, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352201504, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201505:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352201506, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352201504, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201506:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352201507, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352201504, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201507:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352201508, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352201504, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201508:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352201509, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352201504, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201509:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352222416, "codeType": "AIR", "amt": -260, "curr": "AED", "originalAmt": -260, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "FZ 1689 DXB-FRU 28Jun2025 Sat 22:45 04:40\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201504, "paymentMap": [{"key": "1352222416:*********", "paymentID": *********, "amt": -260, "approveCode": 0}]}, {"chargeID": 1352201504, "codeType": "AIR", "amt": 260, "curr": "AED", "originalAmt": 260, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "FZ 1689 DXB-FRU 28Jun2025 Sat 22:45 04:40\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201504:*********", "paymentID": *********, "amt": 260, "approveCode": 0}]}, {"chargeID": 1352222418, "codeType": "BCHG", "taxChargeID": 1352222416, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:22:24", "billDate": "2025-05-19T08:38:36", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201512, "paymentMap": [{"key": "1352222418:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1352201512, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:22:24", "billDate": "2025-05-19T08:28:47", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201512:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352222421, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352222416, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201510, "paymentMap": []}, {"chargeID": 1352201510, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352201504, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352222419, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352222416, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:36", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201511, "paymentMap": [], "PFID": "181530"}, {"chargeID": 1352201511, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352201504, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}, {"recNum": 4, "charges": [{"chargeID": 1352203432, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:29:31", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352203432:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-19T08:29:31"}, {"chargeID": 1352222427, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:37:06", "billDate": "2025-05-19T08:38:36", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222427:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1352222449, "codeType": "INSU", "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:38:36", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352203432, "paymentMap": [{"key": "1352222449:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1352201514, "codeType": "TAX", "taxID": 12489, "taxCode": "KG", "taxChargeID": 1352201513, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "KG: Passenger Service Charge", "comment": "KG: Passenger Service Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201514:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1352201515, "codeType": "TAX", "taxID": 12491, "taxCode": "Q3", "taxChargeID": 1352201513, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "Q3: Passenger Terminal Use Charge", "comment": "Q3: Passenger Terminal Use Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201515:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1352201516, "codeType": "TAX", "taxID": 8685, "taxCode": "HL", "taxChargeID": 1352201513, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "HL: Airport Development Tax", "comment": "HL: Airport Development Tax", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201516:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352201517, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352201513, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201517:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352201518, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352201513, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201518:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352201513, "codeType": "AIR", "amt": 260, "curr": "AED", "originalAmt": 260, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "FZ 1690 FRU-DXB 05Jul2025 Sat 05:50 08:10\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201513:*********", "paymentID": *********, "amt": 260, "approveCode": 0}]}, {"chargeID": 1352201521, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:22:24", "billDate": "2025-05-19T08:28:47", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201521:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352201519, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352201513, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352201520, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352201513, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181591"}]}, {"recNum": 6, "charges": [{"chargeID": 1352222459, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:37:06", "billDate": "2025-05-19T08:38:37", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222459:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-19T08:38:37"}, {"chargeID": 1352222451, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352222450, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222451:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352222452, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352222450, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222452:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352222453, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352222450, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:37", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222453:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352222454, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352222450, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:37", "billDate": "2025-05-19T08:38:37", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222454:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352222455, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352222450, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:37", "billDate": "2025-05-19T08:38:37", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222455:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352222450, "codeType": "AIR", "amt": 270, "curr": "AED", "originalAmt": 270, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "FZ 1689 DXB-FRU 27Jun2025 Fri 22:45 04:40\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222450:*********", "paymentID": *********, "amt": 270, "approveCode": 0}]}, {"chargeID": 1352222460, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:05", "billDate": "2025-05-19T08:38:37", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222460:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352222457, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352222450, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:37", "billDate": "2025-05-19T08:38:37", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352222458, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352222450, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:37", "billDate": "2025-05-19T08:38:37", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}, {"recNum": 1, "charges": [{"chargeID": 1352203429, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:29:30", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352203429:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-19T08:29:30"}, {"chargeID": 1352222377, "codeType": "INSU", "taxChargeID": 1352222328, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:38:35", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352203429, "paymentMap": [{"key": "1352222377:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1352222371, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352222328, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201491, "paymentMap": [{"key": "1352222371:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1352222372, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352222328, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201490, "paymentMap": [{"key": "1352222372:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352222375, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352222328, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201388, "paymentMap": [{"key": "1352222375:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1352222376, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352222328, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201387, "paymentMap": [{"key": "1352222376:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1352222378, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352222328, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201489, "paymentMap": [{"key": "1352222378:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352201387, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352201386, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201387:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352201388, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352201386, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201388:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352201489, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352201386, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201489:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352201490, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352201386, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201490:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352201491, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352201386, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201491:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352222328, "codeType": "AIR", "amt": -260, "curr": "AED", "originalAmt": -260, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "FZ 1689 DXB-FRU 28Jun2025 Sat 22:45 04:40\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201386, "paymentMap": [{"key": "1352222328:*********", "paymentID": *********, "amt": -260, "approveCode": 0}]}, {"chargeID": 1352201386, "codeType": "AIR", "amt": 260, "curr": "AED", "originalAmt": 260, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "FZ 1689 DXB-FRU 28Jun2025 Sat 22:45 04:40\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201386:*********", "paymentID": *********, "amt": 260, "approveCode": 0}]}, {"chargeID": 1352222373, "codeType": "BCHG", "taxChargeID": 1352222328, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:22:24", "billDate": "2025-05-19T08:38:35", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201494, "paymentMap": [{"key": "1352222373:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1352201494, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:22:24", "billDate": "2025-05-19T08:28:47", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201494:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352222370, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352222328, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201492, "paymentMap": []}, {"chargeID": 1352201492, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352201386, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352222369, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352222328, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:38:35", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352201493, "paymentMap": [], "PFID": "181530"}, {"chargeID": 1352201493, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352201386, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}, {"recNum": 2, "charges": [{"chargeID": 1352203430, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:29:30", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352203430:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-19T08:29:30"}, {"chargeID": 1352222383, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:37:06", "billDate": "2025-05-19T08:38:35", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222383:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1352222385, "codeType": "INSU", "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-19T08:29:09", "billDate": "2025-05-19T08:38:35", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352203430, "paymentMap": [{"key": "1352222385:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1352201496, "codeType": "TAX", "taxID": 12489, "taxCode": "KG", "taxChargeID": 1352201495, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "KG: Passenger Service Charge", "comment": "KG: Passenger Service Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201496:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1352201497, "codeType": "TAX", "taxID": 12491, "taxCode": "Q3", "taxChargeID": 1352201495, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "Q3: Passenger Terminal Use Charge", "comment": "Q3: Passenger Terminal Use Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201497:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1352201498, "codeType": "TAX", "taxID": 8685, "taxCode": "HL", "taxChargeID": 1352201495, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "HL: Airport Development Tax", "comment": "HL: Airport Development Tax", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201498:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352201499, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352201495, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201499:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352201500, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352201495, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201500:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352201495, "codeType": "AIR", "amt": 260, "curr": "AED", "originalAmt": 260, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "FZ 1690 FRU-DXB 05Jul2025 Sat 05:50 08:10\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201495:*********", "paymentID": *********, "amt": 260, "approveCode": 0}]}, {"chargeID": 1352201503, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:22:24", "billDate": "2025-05-19T08:28:47", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352201503:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352201501, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352201495, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352201502, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352201495, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:28:46", "billDate": "2025-05-19T08:28:47", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181591"}]}, {"recNum": 5, "charges": [{"chargeID": 1352222414, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:37:06", "billDate": "2025-05-19T08:38:36", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222414:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.59\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-19T08:38:36"}, {"chargeID": 1352222387, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352222386, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:35", "billDate": "2025-05-19T08:38:35", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222387:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352222388, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352222386, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:35", "billDate": "2025-05-19T08:38:35", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222388:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352222409, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352222386, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222409:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352222410, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352222386, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222410:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352222411, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352222386, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222411:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352222386, "codeType": "AIR", "amt": 270, "curr": "AED", "originalAmt": 270, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:35", "billDate": "2025-05-19T08:38:35", "desc": "FZ 1689 DXB-FRU 27Jun2025 Fri 22:45 04:40\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222386:*********", "paymentID": *********, "amt": 270, "approveCode": 0}]}, {"chargeID": 1352222415, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:05", "billDate": "2025-05-19T08:38:36", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352222415:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352222412, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352222386, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352222413, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352222386, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T08:38:36", "billDate": "2025-05-19T08:38:36", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}], "parentPNRs": [], "childPNRs": []}