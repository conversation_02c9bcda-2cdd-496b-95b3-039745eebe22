{"seriesNum": "299", "PNR": "VQ3J2E", "bookAgent": "96007746layla", "IATA": "96007746", "resCurrency": "AED", "PNRPin": "83181198", "bookDate": "2025-05-27T15:15:09", "modifyDate": "2025-05-28T10:42:07", "resType": "TA", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "b3abaa4226c6480rba0dk1k3s4rcu3w4q76anfj84624", "securityGUID": "b3abaa4226c6480rba0dk1k3s4rcu3w4q76anfj84624", "lastLoadGUID": "3f72c041-5ee0-4cb0-b574-3196c8ed1c81", "isAsyncPNR": false, "MasterPNR": "VQ3J2E", "segments": [{"segKey": "16087749:16087749:6/5/2025 10:05:00 AM", "LFID": 16087749, "depDate": "2025-06-05T00:00:00", "flightGroupId": "16087749", "org": "DXB", "dest": "ZNZ", "depTime": "2025-06-05T10:05:00", "depTimeGMT": "2025-06-05T06:05:00", "arrTime": "2025-06-05T14:30:00", "operCarrier": "FZ", "operFlightNum": "1687", "mrktCarrier": "FZ ", "mrktFlightNum": "1687", "persons": [{"recNum": 1, "status": 1}], "legDetails": [{"PFID": 181499, "depDate": "2025-06-05T10:05:00", "legKey": "16087749:181499:6/5/2025 10:05:00 AM", "customerKey": "A69A03BE8EE75A4C968428F83C8626856B6931647FD62B13EEA091713748D7E1"}], "active": true, "changeType": "TK"}, {"segKey": "16087777:16087777:6/8/2025 9:20:00 PM", "LFID": 16087777, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16087777", "org": "ZNZ", "dest": "DXB", "depTime": "2025-06-08T21:20:00", "depTimeGMT": "2025-06-08T18:20:00", "arrTime": "2025-06-09T03:55:00", "operCarrier": "FZ", "operFlightNum": "1688", "mrktCarrier": "FZ ", "mrktFlightNum": "1688", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181515, "depDate": "2025-06-08T21:20:00", "legKey": "16087777:181515:6/8/2025 9:20:00 PM", "customerKey": "A849DAE5F1BA232E56FE2BE8A3E23AB90A7B5A74D5EB18DAAD0D3AC3203BD57E"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270344100, "fName": "KHALID", "lName": "ALHASSAWI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "96007746layla", "statusReasonID": 0, "markFareClass": "Y", "insuPurchasedate": "5/27/2025 3:15:09 PM", "provider": "<PERSON>", "status": 1, "fareClass": "Y", "operFareClass": "Y", "FBC": "YRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "A2Q4A-C29ZZ-INS/be2d25d9-6719-43aa-b5ba-c04b7998b79a", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835d472000778000000449a#1#1#TA#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 16, "bookDate": "2025-05-27T15:15:09"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "96007746layla", "statusReasonID": 0, "markFareClass": "Y", "insuPurchasedate": "5/27/2025 3:15:09 PM", "provider": "<PERSON>", "status": 1, "fareClass": "Y", "operFareClass": "Y", "FBC": "YRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "A2Q4A-C29ZZ-INS/be2d25d9-6719-43aa-b5ba-c04b7998b79a", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835d472000778000000449a#1#2#TA#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 16, "bookDate": "2025-05-27T15:15:09"}]}], "payments": [{"paymentID": *********, "paxID": 124447871, "method": "INVC", "status": "1", "paidDate": "2025-05-28T10:42:05", "IATANum": "96007746", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 8072.7, "baseCurr": "AED", "baseAmt": 8072.7, "userID": "96007746moham1", "channelID": 16, "authCode": "110821933", "reference": "A5448226", "externalReference": "A5448226", "tranId": "21681150", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21681150}], "OAFlights": null, "physicalFlights": [{"key": "16087749:181499:2025-06-05T10:05:00 AM", "LFID": 16087749, "PFID": 181499, "org": "DXB", "dest": "ZNZ", "depDate": "2025-06-05T10:05:00", "depTime": "2025-06-05T10:05:00", "arrTime": "2025-06-05T14:30:00", "carrier": "FZ", "flightNum": "1687", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": true, "changeType": "TK", "flightChangeTime": "3/14/2025 5:52:42 PM"}, {"key": "16087777:181515:2025-06-08T09:20:00 PM", "LFID": 16087777, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-06-08T21:20:00", "depTime": "2025-06-08T21:20:00", "arrTime": "2025-06-09T03:55:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "OPEN", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20100, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:39:13 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1364432961, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "INSU", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432961:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-27T15:15:08"}, {"chargeID": 1364432950, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364432924, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432950:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364432949, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364432924, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432949:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364432927, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364432924, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432927:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364432925, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364432924, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432925:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364432928, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364432924, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432928:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364432924, "codeType": "AIR", "amt": 3521, "curr": "AED", "originalAmt": 3521, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T15:15:09", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432924:*********", "paymentID": *********, "amt": 3521, "approveCode": 0}]}, {"chargeID": 1365587000, "codeType": "TFEE", "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T10:42:07", "desc": "Transaction Fee", "comment": "Transaction Fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365587000:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1364432926, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1364432924, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364432964, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}]}, {"recNum": 2, "charges": [{"chargeID": 1364432963, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "INSU", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432963:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-27T15:15:08"}, {"chargeID": 1364432955, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1364432952, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Aviation Safety Fee", "comment": "Aviation Safety Fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432955:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1364432957, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1364432952, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432957:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1364432958, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1364432952, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Airport Security Fee (International)", "comment": "Airport Security Fee (International)", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432958:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1364432956, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364432952, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432956:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1364432953, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364432952, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432953:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364432952, "codeType": "AIR", "amt": 3591, "curr": "AED", "originalAmt": 3591, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T15:15:09", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364432952:*********", "paymentID": *********, "amt": 3591, "approveCode": 0}]}, {"chargeID": 1365587001, "codeType": "TFEE", "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T10:42:07", "desc": "Transaction Fee", "comment": "Transaction Fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365587001:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1364432954, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1364432952, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364432965, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T15:15:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181515"}]}], "parentPNRs": [], "childPNRs": []}