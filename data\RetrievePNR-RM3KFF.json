{"seriesNum": "299", "PNR": "RM3KFF", "bookAgent": "WEB2_LIVE", "resCurrency": "KWD", "PNRPin": "83321528", "bookDate": "2025-06-01T18:43:20", "modifyDate": "2025-06-01T18:43:55", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "e5c7g2caq3t3ubw6f86us1v4gd5f8483ab8ae30f1a9c", "securityGUID": "e5c7g2caq3t3ubw6f86us1v4gd5f8483ab8ae30f1a9c", "lastLoadGUID": "4a90502a-4f98-4162-9e51-ce5c642af473", "isAsyncPNR": false, "MasterPNR": "RM3KFF", "segments": [{"segKey": "16130822:16130822:6/26/2025 7:10:00 PM", "LFID": 16130822, "depDate": "2025-06-26T00:00:00", "flightGroupId": "16130822", "org": "KWI", "dest": "ZNZ", "depTime": "2025-06-26T19:10:00", "depTimeGMT": "2025-06-26T16:10:00", "arrTime": "2025-06-27T04:25:00", "operCarrier": "FZ", "operFlightNum": "064/1685", "mrktCarrier": "FZ ", "mrktFlightNum": "064/1685", "persons": [{"recNum": 1, "status": 1}], "legDetails": [{"PFID": 181072, "depDate": "2025-06-26T19:10:00", "legKey": "16130822:181072:6/26/2025 7:10:00 PM", "customerKey": "E161980F3525B9B4CB012C5AFD879EFF5F3BF4551EB972D90F9D85E97D2FAE87"}, {"PFID": 181587, "depDate": "2025-06-27T00:05:00", "legKey": "16130822:181587:6/27/2025 12:05:00 AM", "customerKey": "29988CD30B76ABC7FEAB2A07375E5BCF935C05CD2756CBB2BBB49B793701E905"}], "active": true, "changeType": "AC"}, {"segKey": "16727092:16727092:6/30/2025 9:20:00 PM", "LFID": 16727092, "depDate": "2025-06-30T00:00:00", "flightGroupId": "16727092", "org": "ZNZ", "dest": "KWI", "depTime": "2025-06-30T21:20:00", "depTimeGMT": "2025-06-30T18:20:00", "arrTime": "2025-07-01T07:05:00", "operCarrier": "FZ", "operFlightNum": "1688/053", "mrktCarrier": "FZ ", "mrktFlightNum": "1688/053", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181515, "depDate": "2025-06-30T21:20:00", "legKey": "16727092:181515:6/30/2025 9:20:00 PM", "customerKey": "696E5E7E46AED8BE3B6C66B0C48FE0723D826E025297BB11D23BC2465BC87F02"}, {"PFID": 181068, "depDate": "2025-07-01T06:15:00", "legKey": "16727092:181068:7/1/2025 6:15:00 AM", "customerKey": "6720A0610C2EC72A419A8D8D1777F4AD5DA7605AA762FC04AD46B384B176CF05"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 270895688, "fName": "OMAR", "lName": "RADWAN", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "6/1/2025 6:43:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL7KW2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "ZEW33-PYQBN-INS/800ee2c4-277b-47f6-911b-13f9b6b35b57", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683c9d5a0007770000024634#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-06-01T18:43:20"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "6/1/2025 6:43:21 PM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL7KW2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "ZEW33-PYQBN-INS/800ee2c4-277b-47f6-911b-13f9b6b35b57", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683c9d5a0007770000024634#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-06-01T18:43:20"}]}], "payments": [{"paymentID": *********, "paxID": 270895732, "method": "MSCD", "status": "1", "paidDate": "2025-06-01T18:43:53", "cardNum": "************9252", "gateway": "EPS", "paidCurr": "KWD", "paidAmt": 176.151, "baseCurr": "KWD", "baseAmt": 176.151, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "301650", "reference": "23391714", "externalReference": "23391714", "tranId": "21771150", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEKWD", "exchangeRate": "1", "resExternalPaymentID": 21771150}], "OAFlights": null, "physicalFlights": [{"key": "16130822:181072:2025-06-26T07:10:00 PM", "LFID": 16130822, "PFID": 181072, "org": "KWI", "dest": "DXB", "depDate": "2025-06-26T19:10:00", "depTime": "2025-06-26T19:10:00", "arrTime": "2025-06-26T21:50:00", "carrier": "FZ", "flightNum": "064", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "064", "flightStatus": "OPEN", "originMetroGroup": "KWI", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 6000, "reaccomChangeAlert": false, "originName": "Kuwait International Airport", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/19/2025 9:05:36 AM"}, {"key": "16130822:181587:2025-06-27T12:05:00 AM", "LFID": 16130822, "PFID": 181587, "org": "DXB", "dest": "ZNZ", "depDate": "2025-06-27T00:05:00", "depTime": "2025-06-27T00:05:00", "arrTime": "2025-06-27T04:25:00", "carrier": "FZ", "flightNum": "1685", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1685", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": true, "changeType": "AC", "flightChangeTime": "3/19/2025 9:05:36 AM"}, {"key": "16727092:181515:2025-06-30T09:20:00 PM", "LFID": 16727092, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-06-30T21:20:00", "depTime": "2025-06-30T21:20:00", "arrTime": "2025-07-01T03:55:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "OPEN", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20100, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/26/2025 5:36:07 AM"}, {"key": "16727092:181068:2025-07-01T06:15:00 AM", "LFID": 16727092, "PFID": 181068, "org": "DXB", "dest": "KWI", "depDate": "2025-07-01T06:15:00", "depTime": "2025-07-01T06:15:00", "arrTime": "2025-07-01T07:05:00", "carrier": "FZ", "flightNum": "053", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "053", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "KWI", "operatingCarrier": "FZ", "flightDuration": 6600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kuwait International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/26/2025 5:36:07 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1371975468, "codeType": "INSU", "amt": 1.49, "curr": "KWD", "originalAmt": 1.49, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975468:*********", "paymentID": *********, "amt": 1.49, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"3.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-06-01T18:43:20"}, {"chargeID": 1371975451, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371975446, "amt": 3.8, "curr": "KWD", "originalAmt": 3.8, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975451:*********", "paymentID": *********, "amt": 3.8, "approveCode": 0}]}, {"chargeID": 1371975453, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371975446, "amt": 15.45, "curr": "KWD", "originalAmt": 15.45, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975453:*********", "paymentID": *********, "amt": 15.45, "approveCode": 0}]}, {"chargeID": 1371975447, "codeType": "TAX", "taxID": 10406, "taxCode": "GZ", "taxChargeID": 1371975446, "amt": 1, "curr": "KWD", "originalAmt": 1, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975447:*********", "paymentID": *********, "amt": 1, "approveCode": 0}]}, {"chargeID": 1371975449, "codeType": "TAX", "taxID": 13031, "taxCode": "KW", "taxChargeID": 1371975446, "amt": 2, "curr": "KWD", "originalAmt": 2, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Airport and Passenger Service Charge", "comment": "Airport and Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975449:*********", "paymentID": *********, "amt": 2, "approveCode": 0}]}, {"chargeID": 1371975452, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371975446, "amt": 0.45, "curr": "KWD", "originalAmt": 0.45, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975452:*********", "paymentID": *********, "amt": 0.45, "approveCode": 0}]}, {"chargeID": 1371975448, "codeType": "TAX", "taxID": 5784, "taxCode": "YX", "taxChargeID": 1371975446, "amt": 0.25, "curr": "KWD", "originalAmt": 0.25, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975448:*********", "paymentID": *********, "amt": 0.25, "approveCode": 0}]}, {"chargeID": 1371975450, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1371975446, "amt": 3, "curr": "KWD", "originalAmt": 3, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Arrival and Departure Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975450:*********", "paymentID": *********, "amt": 3, "approveCode": 0}]}, {"chargeID": 1371975446, "codeType": "AIR", "amt": 48.55, "curr": "KWD", "originalAmt": 48.55, "originalCurr": "KWD", "status": 1, "billDate": "2025-06-01T18:43:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975446:*********", "paymentID": *********, "amt": 48.55, "approveCode": 0}]}, {"chargeID": 1371980409, "codeType": "PMNT", "amt": 5.131, "curr": "KWD", "originalAmt": 5.131, "originalCurr": "KWD", "status": 1, "billDate": "2025-06-01T18:43:55", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371980409:*********", "paymentID": *********, "amt": 5.131, "approveCode": 0}]}, {"chargeID": 1371975470, "codeType": "NSST", "amt": 2.95, "curr": "KWD", "originalAmt": 2.95, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "NSST", "comment": "FLXID:73X_NSST_ZONE3_WIN_AIS::181587", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975470:*********", "paymentID": *********, "amt": 2.95, "approveCode": 0}], "PFID": "181587"}, {"chargeID": 1371975469, "codeType": "SPST", "amt": 3.65, "curr": "KWD", "originalAmt": 3.65, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "SPST", "comment": "FLXID:SPST_ZONE2_WIN_AIS::181072", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975469:*********", "paymentID": *********, "amt": 3.65, "approveCode": 0}], "PFID": "181072"}, {"chargeID": 1371975454, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371975446, "amt": 0, "curr": "KWD", "originalAmt": 0, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371975474, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KWD", "originalAmt": 0, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181072"}, {"chargeID": 1371975467, "codeType": "FPML", "amt": 0, "curr": "KWD", "originalAmt": 0, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "FPML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181587"}]}, {"recNum": 2, "charges": [{"chargeID": 1371975473, "codeType": "INSU", "amt": 1.48, "curr": "KWD", "originalAmt": 1.48, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975473:*********", "paymentID": *********, "amt": 1.48, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"3.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-06-01T18:43:20"}, {"chargeID": 1371975458, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371975456, "amt": 3.8, "curr": "KWD", "originalAmt": 3.8, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975458:*********", "paymentID": *********, "amt": 3.8, "approveCode": 0}]}, {"chargeID": 1371975463, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1371975456, "amt": 2.8, "curr": "KWD", "originalAmt": 2.8, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Aviation Safety Fee", "comment": "Aviation Safety Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975463:*********", "paymentID": *********, "amt": 2.8, "approveCode": 0}]}, {"chargeID": 1371975460, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371975456, "amt": 15.45, "curr": "KWD", "originalAmt": 15.45, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975460:*********", "paymentID": *********, "amt": 15.45, "approveCode": 0}]}, {"chargeID": 1371975457, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1371975456, "amt": 2, "curr": "KWD", "originalAmt": 2, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Arrival and Departure Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975457:*********", "paymentID": *********, "amt": 2, "approveCode": 0}]}, {"chargeID": 1371975461, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1371975456, "amt": 12.35, "curr": "KWD", "originalAmt": 12.35, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975461:*********", "paymentID": *********, "amt": 12.35, "approveCode": 0}]}, {"chargeID": 1371975462, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1371975456, "amt": 1.55, "curr": "KWD", "originalAmt": 1.55, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Airport Security Fee (International)", "comment": "Airport Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975462:*********", "paymentID": *********, "amt": 1.55, "approveCode": 0}]}, {"chargeID": 1371975459, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371975456, "amt": 0.45, "curr": "KWD", "originalAmt": 0.45, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975459:*********", "paymentID": *********, "amt": 0.45, "approveCode": 0}]}, {"chargeID": 1371975456, "codeType": "AIR", "amt": 48.55, "curr": "KWD", "originalAmt": 48.55, "originalCurr": "KWD", "status": 1, "billDate": "2025-06-01T18:43:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371975456:*********", "paymentID": *********, "amt": 48.55, "approveCode": 0}]}, {"chargeID": 1371975464, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371975456, "amt": 0, "curr": "KWD", "originalAmt": 0, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371975476, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KWD", "originalAmt": 0, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181068"}, {"chargeID": 1371975475, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KWD", "originalAmt": 0, "originalCurr": "KWD", "status": 1, "exchRate": 0, "billDate": "2025-06-01T18:43:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181515"}]}], "parentPNRs": [], "childPNRs": []}