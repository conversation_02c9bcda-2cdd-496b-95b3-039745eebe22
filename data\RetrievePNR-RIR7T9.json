{"seriesNum": "299", "PNR": "RIR7T9", "bookAgent": "dalila.hasan", "resCurrency": "AED", "PNRPin": "82754919", "bookDate": "2025-05-12T11:49:04", "modifyDate": "2025-05-19T10:48:00", "resType": "STANDARD", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 7, "activeSegCount": 1, "webBookingID": "", "securityGUID": "", "lastLoadGUID": "357BC0796EB62868E0631F206F0A19D3", "isAsyncPNR": false, "MasterPNR": "RIR7T9", "segments": [{"segKey": "16087487:16087487:5/22/2025 12:10:00 PM", "LFID": 16087487, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16087487", "org": "DXB", "dest": "TBS", "depTime": "2025-05-22T12:10:00", "depTimeGMT": "2025-05-22T08:10:00", "arrTime": "2025-05-22T15:35:00", "operCarrier": "FZ", "operFlightNum": "711", "mrktCarrier": "FZ", "mrktFlightNum": "711", "persons": [{"recNum": 1, "status": 5}, {"recNum": 13, "status": 5}, {"recNum": 9, "status": 5}, {"recNum": 3, "status": 5}, {"recNum": 5, "status": 5}, {"recNum": 7, "status": 5}, {"recNum": 11, "status": 5}], "legDetails": [{"PFID": 181218, "depDate": "2025-05-22T12:10:00", "legKey": "16087487:181218:5/22/2025 12:10:00 PM", "customerKey": "9B70160601A0B0C14AD201CEF2746306CD8188BC95207D4E85CB6D6AB75AB457"}], "active": true, "changeType": "TK"}, {"segKey": "16087476:16087476:6/30/2025 4:35:00 PM", "LFID": 16087476, "depDate": "2025-06-30T00:00:00", "flightGroupId": "16087476", "org": "TBS", "dest": "DXB", "depTime": "2025-06-30T16:35:00", "depTimeGMT": "2025-06-30T12:35:00", "arrTime": "2025-06-30T19:45:00", "operCarrier": "FZ", "operFlightNum": "712", "mrktCarrier": "FZ", "mrktFlightNum": "712", "persons": [{"recNum": 2, "status": 1}, {"recNum": 14, "status": 1}, {"recNum": 10, "status": 1}, {"recNum": 4, "status": 1}, {"recNum": 6, "status": 1}, {"recNum": 8, "status": 1}, {"recNum": 12, "status": 1}], "legDetails": [{"PFID": 181237, "depDate": "2025-06-30T16:35:00", "legKey": "16087476:181237:6/30/2025 4:35:00 PM", "customerKey": "F12E35252D2E40D36A783D9E0319097DD264EE551706EA2F7DBB2A4DD49F93CF"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": *********, "fName": "AYAT ALHUDA", "lName": "AL SAHLANEE", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}, {"paxID": 268702345, "fName": "NAWAL", "lName": "AL WASTEY", "title": "MISS", "PTCID": 5, "gender": "F", "DOB": "2023-11-22T00:00:00", "recNum": [13, 14]}, {"paxID": 268702343, "fName": "SALMAN", "lName": "AL WASTEY", "title": "MSTR", "PTCID": 6, "gender": "M", "DOB": "2020-02-09T00:00:00", "recNum": [9, 10]}, {"paxID": 268702340, "fName": "MARIA", "lName": "VIRAY", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [3, 4]}, {"paxID": 268702341, "fName": "ROMMALINE", "lName": "VIRAY", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [5, 6]}, {"paxID": 268702342, "fName": "ZAYED", "lName": "AL WASTEY", "title": "MSTR", "PTCID": 6, "gender": "M", "DOB": "2022-01-26T00:00:00", "recNum": [7, 8]}, {"paxID": 268702344, "fName": "ROSE", "lName": "ALWASTEY", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2018-12-11T00:00:00", "recNum": [11, 12]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821d8ce0007780000000d94#1#1#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821d8ce0007780000000d94#1#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#2#1#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#2#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#3#1#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#3#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#4#1#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#4#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#5#1#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#5#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 11, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#6#1#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 12, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/12/2025 11:49:06 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "DH29N-SG6AD-INS/3c0357b6-5a1b-481a-83da-c8fb1c630610", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#6#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 13, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "K", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#7#1#ENT#VAYANT#CREATE", "travelsWithPaxID": "*********", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}, {"recNum": 14, "recordDetails": [{"bookAgent": "dalila.hasan", "statusReasonID": 0, "markFareClass": "U", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6821d8ce0007780000000d94#7#2#ENT#VAYANT#CREATE", "travelsWithPaxID": "*********", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-12T11:49:04"}]}], "payments": [{"paymentID": *********, "paxID": *********, "method": "VISA", "status": "1", "paidDate": "2025-05-12T11:59:01", "cardNum": "************8617", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 12292.23, "baseCurr": "AED", "baseAmt": 12292.23, "userID": "IVR", "channelID": 1, "cardHolderName": "SHAMIM SAADOU", "authCode": "022442", "reference": "22989192", "externalReference": "22989192", "tranId": "21359894", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallmotoaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21359894}], "OAFlights": null, "physicalFlights": [{"key": "16087487:181218:2025-05-22T12:10:00 PM", "LFID": 16087487, "PFID": 181218, "org": "DXB", "dest": "TBS", "depDate": "2025-05-22T12:10:00", "depTime": "2025-05-22T12:10:00", "arrTime": "2025-05-22T15:35:00", "carrier": "FZ", "flightNum": "711", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "711", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "4/25/2025 12:37:22 PM"}, {"key": "16087476:181237:2025-06-30T04:35:00 PM", "LFID": 16087476, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-06-30T16:35:00", "depTime": "2025-06-30T16:35:00", "arrTime": "2025-06-30T19:45:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "OPEN", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "4/25/2025 12:37:37 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1342610323, "codeType": "INFT", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:47:08", "billDate": "2025-05-12T11:49:05", "comment": "INFANT UNDER 2 YEARS", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "isSSR": true, "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610322, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610322:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}"}, {"chargeID": 1342610313, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342610311, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610313:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342610314, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342610311, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610314:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342610315, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342610311, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610315:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610316, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610311, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610316:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610317, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610311, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610317:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610311, "codeType": "AIR", "amt": 470, "curr": "AED", "originalAmt": 470, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610311:*********", "paymentID": *********, "amt": 470, "approveCode": 0}]}, {"chargeID": 1342630838, "codeType": "PMNT", "amt": 358.03, "curr": "AED", "originalAmt": 358.03, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T11:59:08", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342630838:*********", "paymentID": *********, "amt": 358.03, "approveCode": 0}]}, {"chargeID": 1342610312, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610312:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610321, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:31:53", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-8D", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1342610320, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610311, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610318, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610311, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610319, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610311, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}]}, {"recNum": 2, "charges": [{"chargeID": 1342610335, "codeType": "INFT", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:47:09", "billDate": "2025-05-12T11:49:05", "comment": "INFANT UNDER 2 YEARS", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "isSSR": true, "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610334, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610334:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}"}, {"chargeID": 1342610326, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1342610324, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610326:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1342610327, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1342610324, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610327:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1342610328, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610324, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610328:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610329, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610324, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610329:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610324, "codeType": "AIR", "amt": 565, "curr": "AED", "originalAmt": 565, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610324:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1342610325, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610325:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610333, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:32:17", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-7D", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1342610332, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610324, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610330, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610324, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610331, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610324, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 13, "charges": [{"chargeID": 1342610451, "codeType": "AIR", "amt": 85, "curr": "AED", "originalAmt": 85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610451:*********", "paymentID": *********, "amt": 85, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610452, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610452:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610453, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1342610451, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGI: 10kg baggage INCLUDED in fare", "comment": "BAGI: 10kg baggage INCLUDED in fare", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 14, "charges": [{"chargeID": 1342610454, "codeType": "AIR", "amt": 85, "curr": "AED", "originalAmt": 85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610454:*********", "paymentID": *********, "amt": 85, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610455, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610455:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610456, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1342610454, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGI: 10kg baggage INCLUDED in fare", "comment": "BAGI: 10kg baggage INCLUDED in fare", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 9, "charges": [{"chargeID": 1342610415, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610415:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610407, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342610405, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610407:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342610408, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342610405, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610408:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342610409, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342610405, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610409:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610410, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610405, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610410:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610411, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610405, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610411:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610405, "codeType": "AIR", "amt": 470, "curr": "AED", "originalAmt": 470, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610405:*********", "paymentID": *********, "amt": 470, "approveCode": 0}]}, {"chargeID": 1342610406, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610406:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610416, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:48:28", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-9E", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1342610414, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610405, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610412, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610405, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610413, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610405, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}]}, {"recNum": 10, "charges": [{"chargeID": 1342610426, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610426:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610419, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1342610417, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610419:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1342610420, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1342610417, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610420:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1342610421, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610417, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610421:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610422, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610417, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610422:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610417, "codeType": "AIR", "amt": 565, "curr": "AED", "originalAmt": 565, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610417:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1342610418, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610418:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610427, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:48:55", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-8E", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1342610425, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610417, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610423, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610417, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610424, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610417, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 3, "charges": [{"chargeID": 1342610346, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610346:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610338, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342610336, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610338:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342610339, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342610336, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610339:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342610340, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342610336, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610340:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610341, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610336, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610341:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610342, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610336, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610342:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610336, "codeType": "AIR", "amt": 470, "curr": "AED", "originalAmt": 470, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610336:*********", "paymentID": *********, "amt": 470, "approveCode": 0}]}, {"chargeID": 1342610337, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610337:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610347, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:48:26", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-9D", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1342610345, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610336, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610343, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610336, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610344, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610336, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}]}, {"recNum": 4, "charges": [{"chargeID": 1342610357, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610357:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610350, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1342610348, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610350:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1342610351, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1342610348, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610351:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1342610352, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610348, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610352:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610353, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610348, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610353:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610348, "codeType": "AIR", "amt": 565, "curr": "AED", "originalAmt": 565, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610348:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1342610349, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610349:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610358, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:48:50", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-8D", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1342610356, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610348, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610354, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610348, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610355, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610348, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 5, "charges": [{"chargeID": 1342610370, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610370:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610361, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342610359, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610361:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342610362, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342610359, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610362:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342610363, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342610359, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610363:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610364, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610359, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610364:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610365, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610359, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610365:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610359, "codeType": "AIR", "amt": 470, "curr": "AED", "originalAmt": 470, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610359:*********", "paymentID": *********, "amt": 470, "approveCode": 0}]}, {"chargeID": 1342610360, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610360:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610369, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:31:57", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-8E", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1342610368, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610359, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610366, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610359, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610367, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610359, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}]}, {"recNum": 6, "charges": [{"chargeID": 1342610381, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610381:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610373, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1342610371, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610373:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1342610374, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1342610371, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610374:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1342610375, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610371, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610375:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610376, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610371, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610376:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610371, "codeType": "AIR", "amt": 565, "curr": "AED", "originalAmt": 565, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610371:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1342610372, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610372:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610380, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:32:20", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-7E", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1342610379, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610371, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610377, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610371, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610378, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610371, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 7, "charges": [{"chargeID": 1342610393, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610393:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610384, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342610382, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610384:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342610385, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342610382, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610385:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342610386, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342610382, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610386:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610387, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610382, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610387:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610388, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610382, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610388:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610382, "codeType": "AIR", "amt": 470, "curr": "AED", "originalAmt": 470, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610382:*********", "paymentID": *********, "amt": 470, "approveCode": 0}]}, {"chargeID": 1342610383, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610383:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610392, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:31:58", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-8F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1342610391, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610382, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610389, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610382, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610390, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610382, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}]}, {"recNum": 8, "charges": [{"chargeID": 1342610404, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610404:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610396, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1342610394, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610396:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1342610397, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1342610394, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610397:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1342610398, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610394, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610398:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610399, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610394, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610399:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610394, "codeType": "AIR", "amt": 565, "curr": "AED", "originalAmt": 565, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610394:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1342610395, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610395:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610403, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:32:22", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-7F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1342610402, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610394, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610400, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610394, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610401, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610394, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 11, "charges": [{"chargeID": 1342610438, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610438:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610430, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342610428, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610430:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342610431, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342610428, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610431:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342610432, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342610428, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610432:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610433, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610428, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610433:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610434, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610428, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610434:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610428, "codeType": "AIR", "amt": 470, "curr": "AED", "originalAmt": 470, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 711 DXB-TBS 22May2025 Thu 12:10 15:35\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610428:*********", "paymentID": *********, "amt": 470, "approveCode": 0}]}, {"chargeID": 1342610429, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610429:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610439, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:48:31", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-9F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1342610437, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610428, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610435, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610428, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610436, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610428, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}]}, {"recNum": 12, "charges": [{"chargeID": 1342610449, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:43:21", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610449:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"229.84\",\"Tax\":\"10.94\",\"Currency\":\"USD\",\"SegPaxCount\":\"12\"}", "ChargeBookDate": "2025-05-12T11:49:05"}, {"chargeID": 1342610442, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1342610440, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610442:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1342610443, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1342610440, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610443:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1342610444, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342610440, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610444:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342610445, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342610440, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610445:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1342610440, "codeType": "AIR", "amt": 565, "curr": "AED", "originalAmt": 565, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "FZ 712 TBS-DXB 30Jun2025 Mon 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610440:*********", "paymentID": *********, "amt": 565, "approveCode": 0}]}, {"chargeID": 1342610441, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342610441:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342610450, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:48:56", "billDate": "2025-05-12T11:49:05", "desc": "Special Service Request:FRST-8F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1342610448, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342610440, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610446, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342610440, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342610447, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1342610440, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T11:49:03", "billDate": "2025-05-12T11:49:05", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}], "parentPNRs": [], "childPNRs": []}