{"pnr": "S7NPG0", "policyId": "V7X27-HAWXH-INS", "timestamp": "2025-06-04T06:52:40.487Z", "requestDetails": {"url": "https://api.xcover.com/x/partners/ZDTIY/bookings/V7X27-HAWXH-INS", "method": "POST", "headers": {"Content-Type": "application/json"}, "payload": {"quotes": [{"id": "V7X27-HAWXH", "insured": [{"first_name": "HALA", "last_name": "ABOUSHADY"}]}], "policyholder": {"first_name": "HALA", "last_name": "ABOUSHADY", "email": "<EMAIL>", "country": "EG"}}}, "curlCommand": "curl --location 'https://api.xcover.com/x/partners/ZDTIY/bookings/V7X27-HAWXH-INS' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n  \"quotes\": [\n    {\n      \"id\": \"V7X27-HAWXH\",\n      \"insured\": [\n        {\n          \"first_name\": \"HAL<PERSON>\",\n          \"last_name\": \"ABOUSHADY\"\n        }\n      ]\n    }\n  ],\n  \"policyholder\": {\n    \"first_name\": \"HALA\",\n    \"last_name\": \"ABOUSHADY\",\n    \"email\": \"<EMAIL>\",\n    \"country\": \"EG\"\n  }\n}'", "instructions": ["1. Verify the email address in the policyholder section is correct", "2. Verify passenger names are correct", "3. Confirm the country code is appropriate", "4. Execute the curl command manually", "5. Save the response for record keeping"], "notes": {"extractedPassengers": 1, "extractedEmail": "<EMAIL>", "primaryPassengerId": 266956374, "firstSegmentOrigin": "SPX", "detectedCountry": "EG", "quoteId": "V7X27-HAWXH", "hasContactInfo": false, "emailFromPrimaryPax": false}}