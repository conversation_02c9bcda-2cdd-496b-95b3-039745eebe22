{"seriesNum": "299", "PNR": "M62AA8", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82935311", "bookDate": "2025-05-19T03:13:11", "modifyDate": "2025-05-19T10:44:46", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "126690n1s0tdu5g9s665rfv4400294cae618b2bdc59a", "securityGUID": "126690n1s0tdu5g9s665rfv4400294cae618b2bdc59a", "lastLoadGUID": "a106391a-96be-4106-a9ef-bd4c088edcfd", "isAsyncPNR": false, "MasterPNR": "M62AA8", "segments": [{"segKey": "16087807:16087807:5/25/2025 9:20:00 AM", "LFID": 16087807, "depDate": "2025-05-25T00:00:00", "flightGroupId": "16087807", "org": "DXB", "dest": "ZAG", "depTime": "2025-05-25T09:20:00", "depTimeGMT": "2025-05-25T05:20:00", "arrTime": "2025-05-25T13:30:00", "operCarrier": "FZ", "operFlightNum": "1793", "mrktCarrier": "FZ ", "mrktFlightNum": "1793", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181583, "depDate": "2025-05-25T09:20:00", "legKey": "16087807:181583:5/25/2025 9:20:00 AM", "customerKey": "8B00607385187F37CB09BEFA2B2CBE138A8535D2EC156C0F51D746AE79130D2C"}], "active": true}, {"segKey": "16087814:16087814:5/30/2025 2:30:00 PM", "LFID": 16087814, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087814", "org": "ZAG", "dest": "DXB", "depTime": "2025-05-30T14:30:00", "depTimeGMT": "2025-05-30T12:30:00", "arrTime": "2025-05-30T22:15:00", "operCarrier": "FZ", "operFlightNum": "1794", "mrktCarrier": "FZ ", "mrktFlightNum": "1794", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181590, "depDate": "2025-05-30T14:30:00", "legKey": "16087814:181590:5/30/2025 2:30:00 PM", "customerKey": "68673C5F7A4052F12D4C61BD9A24F3B5ED186B52C8C08D39E0ABFB184C9A96A4"}], "active": true}, {"segKey": "16087807:16087807:6/8/2025 9:20:00 AM", "LFID": 16087807, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16087807", "org": "DXB", "dest": "ZAG", "depTime": "2025-06-08T09:20:00", "depTimeGMT": "2025-06-08T05:20:00", "arrTime": "2025-06-08T13:30:00", "operCarrier": "FZ", "operFlightNum": "1793", "mrktCarrier": "FZ", "mrktFlightNum": "1793", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181583, "depDate": "2025-06-08T09:20:00", "legKey": "16087807:181583:6/8/2025 9:20:00 AM", "customerKey": "2E1BFCCDE5CE9C655D445737D716B31F483C767E54C10063B0F6A78E548EB025"}], "active": true}, {"segKey": "16087814:16087814:6/15/2025 2:30:00 PM", "LFID": 16087814, "depDate": "2025-06-15T00:00:00", "flightGroupId": "16087814", "org": "ZAG", "dest": "DXB", "depTime": "2025-06-15T14:30:00", "depTimeGMT": "2025-06-15T12:30:00", "arrTime": "2025-06-15T22:15:00", "operCarrier": "FZ", "operFlightNum": "1794", "mrktCarrier": "FZ", "mrktFlightNum": "1794", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181590, "depDate": "2025-06-15T14:30:00", "legKey": "16087814:181590:6/15/2025 2:30:00 PM", "customerKey": "DED92855A94D7AB7A4984019C134304EBAB1A51CF30FBB682ED55C1555724748"}], "active": true}], "persons": [{"paxID": 269403127, "fName": "CHUKWUKA PINAH", "lName": "FIDELIX", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/19/2025 3:13:11 AM", "provider": "<PERSON>", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "TD4KU-M7XUB-INS", "insuTransID": "TD4KU-M7XUB-INS/7be2d87a-6d25-4a2f-afa0-5e8701262d0d", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682aa0d400077700000003ea#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-19T03:13:11"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/19/2025 3:13:11 AM", "provider": "<PERSON>", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "TD4KU-M7XUB-INS", "insuTransID": "TD4KU-M7XUB-INS/7be2d87a-6d25-4a2f-afa0-5e8701262d0d", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682aa0d400077700000003ea#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-19T03:13:11"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "muhammed.v", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/19/2025 3:13:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "TD4KU-M7XUB-INS/7be2d87a-6d25-4a2f-afa0-5e8701262d0d", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682b09df0007780000002134#269403127#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-19T10:40:46"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "muhammed.v", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/19/2025 3:13:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "TD4KU-M7XUB-INS/7be2d87a-6d25-4a2f-afa0-5e8701262d0d", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682b09df0007780000002134#269403127#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-19T10:40:46"}]}], "payments": [{"paymentID": *********, "paxID": 269403150, "method": "IPAY", "status": "1", "paidDate": "2025-05-19T03:13:15", "cardNum": "************1663", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2802.32, "baseCurr": "AED", "baseAmt": 2802.32, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "042507", "reference": "23117049", "externalReference": "23117049", "tranId": "21489100", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21489100}, {"paymentID": *********, "paxID": 269450207, "method": "IPAY", "status": "1", "paidDate": "2025-05-19T10:44:40", "cardNum": "************1663", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 452.63, "baseCurr": "AED", "baseAmt": 452.63, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON> ", "authCode": "070474", "reference": "23123508", "externalReference": "23123508", "tranId": "21497685", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21497685}], "OAFlights": null, "physicalFlights": [{"key": "16087807:181583:2025-05-25T09:20:00 AM", "LFID": 16087807, "PFID": 181583, "org": "DXB", "dest": "ZAG", "depDate": "2025-05-25T09:20:00", "depTime": "2025-05-25T09:20:00", "arrTime": "2025-05-25T13:30:00", "carrier": "FZ", "flightNum": "1793", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1793", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ZAG", "operatingCarrier": "FZ", "flightDuration": 22200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zagreb", "isActive": false}, {"key": "16087814:181590:2025-05-30T02:30:00 PM", "LFID": 16087814, "PFID": 181590, "org": "ZAG", "dest": "DXB", "depDate": "2025-05-30T14:30:00", "depTime": "2025-05-30T14:30:00", "arrTime": "2025-05-30T22:15:00", "carrier": "FZ", "flightNum": "1794", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1794", "flightStatus": "CLOSED", "originMetroGroup": "ZAG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "Zagreb", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087807:181583:2025-06-08T09:20:00 AM", "LFID": 16087807, "PFID": 181583, "org": "DXB", "dest": "ZAG", "depDate": "2025-06-08T09:20:00", "depTime": "2025-06-08T09:20:00", "arrTime": "2025-06-08T13:30:00", "carrier": "FZ", "flightNum": "1793", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1793", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "ZAG", "operatingCarrier": "FZ", "flightDuration": 22200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zagreb", "isActive": true}, {"key": "16087814:181590:2025-06-15T02:30:00 PM", "LFID": 16087814, "PFID": 181590, "org": "ZAG", "dest": "DXB", "depDate": "2025-06-15T14:30:00", "depTime": "2025-06-15T14:30:00", "arrTime": "2025-06-15T22:15:00", "carrier": "FZ", "flightNum": "1794", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1794", "flightStatus": "OPEN", "originMetroGroup": "ZAG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "Zagreb", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1351851985, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851985:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-19T03:13:10"}, {"chargeID": 1352450030, "codeType": "INSU", "taxChargeID": 1352450023, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851985, "paymentMap": [{"key": "1352450030:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1351851971, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1351851969, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851971:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1351851974, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1351851969, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851974:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1351851972, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1351851969, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851972:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1351851973, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1351851969, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851973:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1351851975, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1351851969, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851975:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352450026, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352450023, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851975, "paymentMap": [{"key": "1352450026:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352450027, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352450023, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851973, "paymentMap": [{"key": "1352450027:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1352450034, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352450023, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851972, "paymentMap": [{"key": "1352450034:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1352450037, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352450023, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851974, "paymentMap": [{"key": "1352450037:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1352450038, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352450023, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851971, "paymentMap": [{"key": "1352450038:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1351851969, "codeType": "AIR", "amt": 925, "curr": "AED", "originalAmt": 925, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T03:13:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851969:*********", "paymentID": *********, "amt": 925, "approveCode": 0}]}, {"chargeID": 1352450023, "codeType": "AIR", "amt": -925, "curr": "AED", "originalAmt": -925, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T10:40:47", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851969, "paymentMap": [{"key": "1352450023:*********", "paymentID": *********, "amt": -925, "approveCode": 0}]}, {"chargeID": 1351852559, "codeType": "PMNT", "amt": 81.62, "curr": "AED", "originalAmt": 81.62, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T03:13:19", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351852559:*********", "paymentID": *********, "amt": 81.62, "approveCode": 0}]}, {"chargeID": 1352450039, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:47", "billDate": "2025-05-19T10:40:47", "desc": "CancelNoRefund FZ 1793 DXB - ZAG 25.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450039:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1351851970, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1351851969, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352450024, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352450023, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851970, "paymentMap": []}, {"chargeID": 1351851988, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181583"}, {"chargeID": 1352450025, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352450023, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:47", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851988, "paymentMap": [], "PFID": "181583"}]}, {"recNum": 2, "charges": [{"chargeID": 1351851987, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851987:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-19T03:13:10"}, {"chargeID": 1352450048, "codeType": "INSU", "taxChargeID": 1352450040, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851987, "paymentMap": [{"key": "1352450048:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1351851980, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1351851977, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851980:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1351851978, "codeType": "TAX", "taxID": 7184, "taxCode": "MI", "taxChargeID": 1351851977, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Civil Aviation Authority (CCAA) Tax (International)", "comment": "Civil Aviation Authority (CCAA) Tax (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851978:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1351851981, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1351851977, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851981:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1351851982, "codeType": "TAX", "taxID": 11246, "taxCode": "HR", "taxChargeID": 1351851977, "amt": 130, "curr": "AED", "originalAmt": 130, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Passenger Service and Security Charge", "comment": "Passenger Service and Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851982:*********", "paymentID": *********, "amt": 130, "approveCode": 0}]}, {"chargeID": 1352450042, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352450040, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851981, "paymentMap": [{"key": "1352450042:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1352450044, "codeType": "TAX", "taxID": 7184, "taxCode": "MI", "taxChargeID": 1352450040, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "Civil Aviation Authority (CCAA) Tax (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851978, "paymentMap": [{"key": "1352450044:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1352450045, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352450040, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851980, "paymentMap": [{"key": "1352450045:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1352450046, "codeType": "TAX", "taxID": 11246, "taxCode": "HR", "taxChargeID": 1352450040, "amt": -130, "curr": "AED", "originalAmt": -130, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "Passenger Service and Security Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851982, "paymentMap": [{"key": "1352450046:*********", "paymentID": *********, "amt": -130, "approveCode": 0}]}, {"chargeID": 1351851977, "codeType": "AIR", "amt": 925, "curr": "AED", "originalAmt": 925, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T03:13:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351851977:*********", "paymentID": *********, "amt": 925, "approveCode": 0}]}, {"chargeID": 1352450040, "codeType": "AIR", "amt": -925, "curr": "AED", "originalAmt": -925, "originalCurr": "AED", "status": 0, "billDate": "2025-05-19T10:40:47", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851977, "paymentMap": [{"key": "1352450040:*********", "paymentID": *********, "amt": -925, "approveCode": 0}]}, {"chargeID": 1352450069, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "CancelNoRefund FZ 1794 ZAG - DXB 30.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450069:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1351851979, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1351851977, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352450041, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352450040, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851979, "paymentMap": []}, {"chargeID": 1351851989, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T03:13:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181590"}, {"chargeID": 1352450043, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352450040, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-19T10:40:48", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351851989, "paymentMap": [], "PFID": "181590"}]}, {"recNum": 3, "charges": [{"chargeID": 1352450079, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:37:23", "billDate": "2025-05-19T10:40:48", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450079:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-19T10:40:48"}, {"chargeID": 1352450071, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352450070, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450071:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352450072, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352450070, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450072:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352450073, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352450070, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450073:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352450074, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352450070, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450074:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352450075, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352450070, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450075:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352450070, "codeType": "AIR", "amt": 925, "curr": "AED", "originalAmt": 925, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "FZ 1793 DXB-ZAG 08Jun2025 Sun 09:20 13:30\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450070:*********", "paymentID": *********, "amt": 925, "approveCode": 0}]}, {"chargeID": 1352457035, "codeType": "PMNT", "amt": 13.18, "curr": "AED", "originalAmt": 13.18, "originalCurr": "AED", "status": 1, "billDate": "2025-05-19T10:44:46", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352457035:*********", "paymentID": *********, "amt": 13.18, "approveCode": 0}]}, {"chargeID": 1352450078, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450078:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352450076, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352450070, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352450077, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352450070, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181583"}]}, {"recNum": 4, "charges": [{"chargeID": 1352450088, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:37:23", "billDate": "2025-05-19T10:40:49", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450088:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-19T10:40:49"}, {"chargeID": 1352450081, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352450080, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450081:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352450082, "codeType": "TAX", "taxID": 11246, "taxCode": "HR", "taxChargeID": 1352450080, "amt": 130, "curr": "AED", "originalAmt": 130, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "HR: Passenger Service and Security Charge", "comment": "HR: Passenger Service and Security Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450082:*********", "paymentID": *********, "amt": 6.87, "approveCode": 0}, {"key": "1352450082:*********", "paymentID": *********, "amt": 123.13, "approveCode": 0}]}, {"chargeID": 1352450083, "codeType": "TAX", "taxID": 7184, "taxCode": "MI", "taxChargeID": 1352450080, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "MI: Civil Aviation Authority (CCAA) Tax (International)", "comment": "MI: Civil Aviation Authority (CCAA) Tax (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450083:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1352450084, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352450080, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:49", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450084:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1352450080, "codeType": "AIR", "amt": 935, "curr": "AED", "originalAmt": 935, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:48", "billDate": "2025-05-19T10:40:48", "desc": "FZ 1794 ZAG-DXB 15Jun2025 Sun 14:30 22:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450080:*********", "paymentID": *********, "amt": 935, "approveCode": 0}]}, {"chargeID": 1352450087, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:49", "billDate": "2025-05-19T10:40:49", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352450087:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352450085, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1352450080, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:49", "billDate": "2025-05-19T10:40:49", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352450086, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352450080, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T10:40:49", "billDate": "2025-05-19T10:40:49", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181590"}]}], "parentPNRs": [], "childPNRs": []}