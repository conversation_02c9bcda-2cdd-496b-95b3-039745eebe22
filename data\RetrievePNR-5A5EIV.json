{"seriesNum": "299", "PNR": "5A5EIV", "bookAgent": "WEB2_LIVE", "resCurrency": "BHD", "PNRPin": "83189556", "bookDate": "2025-05-27T22:46:40", "modifyDate": "2025-05-27T22:52:13", "resType": "WEB", "resBalance": 0, "timeLimitGMT": "2025-05-28T22:46:39", "timeLimitODT": "2025-05-29T01:46:39", "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 4, "activeSegCount": 2, "webBookingID": "68e79a7a3fxf3b04kfu8o1tg6bt9q54dd947fb6b0fa7", "securityGUID": "68e79a7a3fxf3b04kfu8o1tg6bt9q54dd947fb6b0fa7", "lastLoadGUID": "f3aec5f6-16f3-4ad9-9bb4-78904a5a9377", "isAsyncPNR": false, "MasterPNR": "5A5EIV", "segments": [{"segKey": "17142113:17142113:6/7/2025 10:00:00 PM", "LFID": 17142113, "depDate": "2025-06-07T00:00:00", "flightGroupId": "17142113", "org": "BAH", "dest": "TBS", "depTime": "2025-06-07T22:00:00", "depTimeGMT": "2025-06-07T19:00:00", "arrTime": "2025-06-08T08:25:00", "operCarrier": "FZ", "operFlightNum": "026/8835", "mrktCarrier": "FZ ", "mrktFlightNum": "026/8835", "persons": [{"recNum": 2, "status": 1}, {"recNum": 3, "status": 1}, {"recNum": 1, "status": 1}, {"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181045, "depDate": "2025-06-07T22:00:00", "legKey": "17142113:181045:6/7/2025 10:00:00 PM", "customerKey": "CE9E80C6CE918EE00DA1623AF19146BBE3E574BEE6A04BD02A7D1AC72C8CF566"}, {"PFID": 188099, "depDate": "2025-06-08T04:55:00", "legKey": "17142113:188099:6/8/2025 4:55:00 AM", "customerKey": "940AC165FC30AB1A064B3061F441E485999A8463E4483B7854471D5DC79FE15F"}], "active": true}, {"segKey": "17166127:17166127:6/12/2025 9:40:00 AM", "LFID": 17166127, "depDate": "2025-06-12T00:00:00", "flightGroupId": "17166127", "org": "TBS", "dest": "BAH", "depTime": "2025-06-12T09:40:00", "depTimeGMT": "2025-06-12T05:40:00", "arrTime": "2025-06-12T14:50:00", "operCarrier": "FZ", "operFlightNum": "8842/021", "mrktCarrier": "FZ ", "mrktFlightNum": "8842/021", "persons": [{"recNum": 8, "status": 1}, {"recNum": 5, "status": 1}, {"recNum": 6, "status": 1}, {"recNum": 7, "status": 1}], "legDetails": [{"PFID": 188230, "depDate": "2025-06-12T09:40:00", "legKey": "17166127:188230:6/12/2025 9:40:00 AM", "customerKey": "A0F3153163263AEB357FF4155B021B1AB6EFE629DE57DA74108450623DBCD30B"}, {"PFID": 181017, "depDate": "2025-06-12T14:35:00", "legKey": "17166127:181017:6/12/2025 2:35:00 PM", "customerKey": "A5C9C391F266486F3F7EDC7B9F58A61C50120605F95B4379FBA5980F54987270"}], "active": true}], "persons": [{"paxID": 270377700, "fName": "NABIHA", "lName": "RAHMAN", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2014-10-09T00:00:00", "recNum": [2, 8]}, {"paxID": 270377699, "fName": "NUDRAT", "lName": "RAHMAN", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [3, 5]}, {"paxID": 270377698, "fName": "MAHPARA", "lName": "RESHMI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 6]}, {"paxID": 270377697, "fName": "AKILUR", "lName": "RAHMAN", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [4, 7]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68363e4e0007770000000386#2#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68363e4e0007770000000386#4#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68363e4e0007770000000386#3#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68363e4e0007770000000386#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68363e4e0007770000000386#3#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68363e4e0007770000000386#2#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68363e4e0007770000000386#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/27/2025 10:46:40 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JL63C-<PERSON><PERSON><PERSON><PERSON>-INS/ef1ad4e9-786a-421c-b931-ccb661a82b39", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68363e4e0007770000000386#4#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-27T22:46:40"}]}], "payments": [{"paymentID": *********, "paxID": 270378026, "method": "VISA", "status": "1", "paidDate": "2025-05-27T22:52:10", "cardNum": "************5326", "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 656.151, "baseCurr": "BHD", "baseAmt": 656.151, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "authCode": "173625", "reference": "23290621", "externalReference": "23290621", "tranId": "21671344", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEBHD", "exchangeRate": "1", "resExternalPaymentID": 21671344}], "OAFlights": null, "physicalFlights": [{"key": "17142113:181045:2025-06-07T10:00:00 PM", "LFID": 17142113, "PFID": 181045, "org": "BAH", "dest": "DXB", "depDate": "2025-06-07T22:00:00", "depTime": "2025-06-07T22:00:00", "arrTime": "2025-06-08T00:20:00", "carrier": "FZ", "flightNum": "026", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "026", "flightStatus": "OPEN", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "17142113:188099:2025-06-08T04:55:00 AM", "LFID": 17142113, "PFID": 188099, "org": "DXB", "dest": "TBS", "depDate": "2025-06-08T04:55:00", "depTime": "2025-06-08T04:55:00", "arrTime": "2025-06-08T08:25:00", "carrier": "FZ", "flightNum": "8835", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "8835", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": true}, {"key": "17166127:188230:2025-06-12T09:40:00 AM", "LFID": 17166127, "PFID": 188230, "org": "TBS", "dest": "DXB", "depDate": "2025-06-12T09:40:00", "depTime": "2025-06-12T09:40:00", "arrTime": "2025-06-12T12:50:00", "carrier": "FZ", "flightNum": "8842", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "8842", "flightStatus": "OPEN", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "17166127:181017:2025-06-12T02:35:00 PM", "LFID": 17166127, "PFID": 181017, "org": "DXB", "dest": "BAH", "depDate": "2025-06-12T14:35:00", "depTime": "2025-06-12T14:35:00", "arrTime": "2025-06-12T14:50:00", "carrier": "FZ", "flightNum": "021", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "021", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "BAH", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bahrain", "isActive": true}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1364900783, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900783:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900751, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900747, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900751:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900748, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1364900747, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900748:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900749, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1364900747, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900749:*********", "paymentID": *********, "amt": 10, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900750, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900747, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900750:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900752, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900747, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900752:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900747, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900747:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900753, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900747, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900800, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181045"}, {"chargeID": 1364900799, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188099"}]}, {"recNum": 8, "charges": [{"chargeID": 1364900786, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900786:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900756, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900755, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900756:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900758, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900755, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900758:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900759, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1364900755, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900759:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900760, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1364900755, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900760:*********", "paymentID": *********, "amt": 9.4, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900757, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900755, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900757:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900755, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900755:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900761, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900755, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900801, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181017"}, {"chargeID": 1364900802, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188230"}]}, {"recNum": 3, "charges": [{"chargeID": 1364900777, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900777:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900734, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900731, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900734:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900732, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1364900731, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900732:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900735, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900731, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900735:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900733, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1364900731, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900733:*********", "paymentID": *********, "amt": 10, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900736, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900731, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900736:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900731, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900731:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900737, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900731, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900796, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181045"}, {"chargeID": 1364900795, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188099"}]}, {"recNum": 5, "charges": [{"chargeID": 1364900780, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900780:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900743, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1364900739, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900743:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900740, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900739, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900740:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900741, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900739, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900741:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900744, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1364900739, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900744:*********", "paymentID": *********, "amt": 9.4, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900742, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900739, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900742:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900739, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900739:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900745, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900739, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900798, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188230"}, {"chargeID": 1364900797, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181017"}]}, {"recNum": 1, "charges": [{"chargeID": 1364900771, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900771:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900719, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900715, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900719:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900718, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900715, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900718:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900720, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900715, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900720:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900716, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1364900715, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900716:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900717, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1364900715, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900717:*********", "paymentID": *********, "amt": 10, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900715, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900715:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900721, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900715, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900791, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188099"}, {"chargeID": 1364900792, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181045"}]}, {"recNum": 6, "charges": [{"chargeID": 1364900774, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900774:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900724, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900723, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900724:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900725, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900723, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900725:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900726, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900723, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900726:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900728, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1364900723, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900728:*********", "paymentID": *********, "amt": 9.4, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900727, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1364900723, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900727:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900723, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900723:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900729, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900723, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900794, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188230"}, {"chargeID": 1364900793, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181017"}]}, {"recNum": 4, "charges": [{"chargeID": 1364900765, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900765:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900704, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900699, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900704:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900702, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900699, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900702:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900703, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900699, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900703:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900701, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1364900699, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900701:*********", "paymentID": *********, "amt": 10, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900700, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1364900699, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900700:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900699, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900699:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364907175, "codeType": "PMNT", "amt": 19.111, "curr": "BHD", "originalAmt": 19.111, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:52:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364907175:*********", "paymentID": *********, "amt": 19.111, "approveCode": 0}]}, {"chargeID": 1364900705, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900699, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900788, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181045"}, {"chargeID": 1364900787, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188099"}]}, {"recNum": 7, "charges": [{"chargeID": 1364900768, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900768:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"38.88\",\r\n  \"Tax\": \"1.86\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-05-27T22:46:39"}, {"chargeID": 1364900712, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1364900707, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900712:*********", "paymentID": *********, "amt": 9.4, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900708, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364900707, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900708:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900710, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364900707, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900710:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900711, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1364900707, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900711:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900709, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364900707, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900709:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900707, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-27T22:46:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364900707:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364900713, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364900707, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364900790, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "188230"}, {"chargeID": 1364900789, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-05-27T22:46:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181017"}]}], "parentPNRs": [], "childPNRs": []}