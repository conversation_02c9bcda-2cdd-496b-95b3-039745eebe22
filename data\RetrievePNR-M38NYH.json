{"seriesNum": "299", "PNR": "M38NYH", "bookAgent": "saja.abdo", "resCurrency": "AED", "PNRPin": "82702214", "bookDate": "2025-05-10T11:20:38", "modifyDate": "2025-05-22T03:00:07", "resType": "STANDARD", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "", "securityGUID": "", "lastLoadGUID": "95218f05-150f-4c8e-9981-bb27951d673a", "isAsyncPNR": false, "MasterPNR": "M38NYH", "segments": [{"segKey": "16087456:16087456:5/13/2025 1:40:00 AM", "LFID": 16087456, "depDate": "2025-05-13T00:00:00", "flightGroupId": "16087456", "org": "DXB", "dest": "TBS", "depTime": "2025-05-13T01:40:00", "depTimeGMT": "2025-05-12T21:40:00", "arrTime": "2025-05-13T05:05:00", "operCarrier": "FZ", "operFlightNum": "713", "mrktCarrier": "FZ", "mrktFlightNum": "713", "persons": [{"recNum": 3, "status": 0}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181207, "depDate": "2025-05-13T01:40:00", "legKey": "16087456:181207:5/13/2025 1:40:00 AM", "customerKey": "C65FEF2ABB280A83A8F6EB201512BBB3D0A5D417A4F0A512C8BD02D2F2378ECC"}], "active": true, "changeType": "TK"}, {"segKey": "16087476:16087476:5/21/2025 4:35:00 PM", "LFID": 16087476, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087476", "org": "TBS", "dest": "DXB", "depTime": "2025-05-21T16:35:00", "depTimeGMT": "2025-05-21T12:35:00", "arrTime": "2025-05-21T19:50:00", "operCarrier": "FZ", "operFlightNum": "712", "mrktCarrier": "FZ", "mrktFlightNum": "712", "persons": [{"recNum": 4, "status": 0}, {"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181237, "depDate": "2025-05-21T16:35:00", "legKey": "16087476:181237:5/21/2025 4:35:00 PM", "customerKey": "64B7C6FD95123361A8092F93E4273A110AFF21D858A2FF7EB00BF9743559E4E5"}], "active": true, "changeType": "TK"}, {"segKey": "16087456:16087456:5/17/2025 1:40:00 AM", "LFID": 16087456, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087456", "org": "DXB", "dest": "TBS", "depTime": "2025-05-17T01:40:00", "depTimeGMT": "2025-05-16T21:40:00", "arrTime": "2025-05-17T05:05:00", "operCarrier": "FZ", "operFlightNum": "713", "mrktCarrier": "FZ", "mrktFlightNum": "713", "persons": [{"recNum": 5, "status": 0}, {"recNum": 7, "status": 0}], "legDetails": [{"PFID": 181207, "depDate": "2025-05-17T01:40:00", "legKey": "16087456:181207:5/17/2025 1:40:00 AM", "customerKey": "89B62C5AB44D7BB2448B8962144A815CDCC0074983C079B166F59243C7B3B1FD"}], "active": true, "changeType": "TK"}, {"segKey": "16087474:16087474:5/26/2025 6:05:00 AM", "LFID": 16087474, "depDate": "2025-05-26T00:00:00", "flightGroupId": "16087474", "org": "TBS", "dest": "DXB", "depTime": "2025-05-26T06:05:00", "depTimeGMT": "2025-05-26T02:05:00", "arrTime": "2025-05-26T09:15:00", "operCarrier": "FZ", "operFlightNum": "714", "mrktCarrier": "FZ", "mrktFlightNum": "714", "persons": [{"recNum": 6, "status": 0}, {"recNum": 8, "status": 0}], "legDetails": [{"PFID": 181235, "depDate": "2025-05-26T06:05:00", "legKey": "16087474:181235:5/26/2025 6:05:00 AM", "customerKey": "0965D12BFF8936A26492EA1E48067BF759A4DEA53862BBA4E7CEAD61DFB2E8F7"}], "active": true, "changeType": "TK"}, {"segKey": "16087456:16087456:5/14/2025 1:40:00 AM", "LFID": 16087456, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087456", "org": "DXB", "dest": "TBS", "depTime": "2025-05-14T01:40:00", "depTimeGMT": "2025-05-13T21:40:00", "arrTime": "2025-05-14T05:05:00", "operCarrier": "FZ", "operFlightNum": "713", "mrktCarrier": "FZ", "mrktFlightNum": "713", "persons": [{"recNum": 9, "status": 5}, {"recNum": 11, "status": 5}], "legDetails": [{"PFID": 181207, "depDate": "2025-05-14T01:40:00", "legKey": "16087456:181207:5/14/2025 1:40:00 AM", "customerKey": "9F4806F82D262E3799D45FA6DAD03576993AFB5626254172156D3545C33819C5"}], "active": true, "changeType": "TK"}, {"segKey": "16087474:16087474:5/23/2025 6:05:00 AM", "LFID": 16087474, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087474", "org": "TBS", "dest": "DXB", "depTime": "2025-05-23T06:05:00", "depTimeGMT": "2025-05-23T02:05:00", "arrTime": "2025-05-23T09:15:00", "operCarrier": "FZ", "operFlightNum": "714", "mrktCarrier": "FZ", "mrktFlightNum": "714", "persons": [{"recNum": 10, "status": 5}, {"recNum": 12, "status": 5}], "legDetails": [{"PFID": 181235, "depDate": "2025-05-23T06:05:00", "legKey": "16087474:181235:5/23/2025 6:05:00 AM", "customerKey": "41FAECED4921428C66E9E257FCC3B1BD67203E7FB6BF5B77A7CED3DB64DE33B7"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268501430, "fName": "RANIA", "lName": "GHANNAM", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [3, 4, 5, 6, 9, 10]}, {"paxID": 268501431, "fName": "SAMER", "lName": "ABO TOUQ ALREFAEI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 7, 8, 11, 12]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "saja.abdo", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 7, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f33a8000778000000932c#1#1#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-10T11:20:38"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "saja.abdo", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 8, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f33a8000778000000932c#1#2#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-10T11:20:38"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "saja.abdo", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f33a8000778000000932c#2#1#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-10T11:20:38"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "saja.abdo", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KRB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681f33a8000778000000932c#2#2#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-10T11:20:38"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "enas.abasher", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 9, "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6820d785000777000000e590#268501430#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-11T17:02:05"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "enas.abasher", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 10, "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6820d785000777000000e590#268501430#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-11T17:02:05"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "enas.abasher", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 11, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6820d785000777000000e590#268501431#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-11T17:02:05"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "enas.abasher", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "toRecNum": 12, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6820d785000777000000e590#268501431#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-11T17:02:05"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "ash<PERSON><PERSON><PERSON>a<PERSON>a", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "fromRecNum": 5, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68224b20000778000000366c#268501430#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-12T19:30:08"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "ash<PERSON><PERSON><PERSON>a<PERSON>a", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "fromRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68224b20000778000000366c#268501430#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-12T19:30:08"}]}, {"recNum": 11, "recordDetails": [{"bookAgent": "ash<PERSON><PERSON><PERSON>a<PERSON>a", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "fromRecNum": 7, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68224b20000778000000366c#268501431#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-12T19:30:08"}]}, {"recNum": 12, "recordDetails": [{"bookAgent": "ash<PERSON><PERSON><PERSON>a<PERSON>a", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/10/2025 12:44:59 PM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "BBBW7-AZT9H-INS", "insuTransID": "BBBW7-AZT9H-INS/f65a9215-2a03-44a7-833f-53c56db50b90", "fromRecNum": 8, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68224b20000778000000366c#268501431#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-12T19:30:08"}]}], "payments": [{"paymentID": *********, "paxID": 269762128, "method": "IPAY", "status": "1", "paidDate": "2025-05-22T02:59:57", "cardNum": "************0861", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 128.75, "baseCurr": "AED", "baseAmt": 128.75, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "authCode": "575196", "reference": "23175699", "externalReference": "23175699", "tranId": "21555012", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21555012}, {"paymentID": *********, "paxID": 268508883, "method": "VISA", "status": "1", "paidDate": "2025-05-10T12:45:38", "cardNum": "************4372", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 350.51, "baseCurr": "AED", "baseAmt": 350.51, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "SAMER ABO TOUQ ALREFAEI", "authCode": "571071", "reference": "22951414", "externalReference": "22951414", "tranId": "21322824", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21322824}, {"paymentID": *********, "paxID": 268509826, "method": "VISA", "status": "1", "paidDate": "2025-05-10T12:55:17", "cardNum": "************4372", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 36.05, "baseCurr": "AED", "baseAmt": 36.05, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "SAMER ABO TOUQ", "authCode": "362111", "reference": "22951534", "externalReference": "22951534", "tranId": "21323025", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21323025}, {"paymentID": *********, "paxID": 268621734, "method": "IPAY", "status": "1", "paidDate": "2025-05-11T17:26:18", "cardNum": "************0861", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1050.6, "baseCurr": "AED", "baseAmt": 1050.6, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "747178", "reference": "22976439", "externalReference": "22976439", "tranId": "21345120", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21345120}, {"paymentID": *********, "paxID": 268759948, "method": "VISA", "status": "1", "paidDate": "2025-05-12T21:30:53", "cardNum": "************4372", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 63.86, "baseCurr": "AED", "baseAmt": 63.86, "userID": "olci", "channelID": 20, "cardHolderName": "SAMER ABO TOUQ ALREFAEI", "authCode": "021013", "reference": "22998671", "externalReference": "22998671", "tranId": "21370178", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21370178}, {"paymentID": *********, "paxID": 268507017, "method": "IPAY", "status": "1", "paidDate": "2025-05-10T12:23:43", "cardNum": "************0861", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 3110.6, "baseCurr": "AED", "baseAmt": 3110.6, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "409022", "reference": "22951186", "externalReference": "22951186", "tranId": "21322500", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21322500}, {"paymentID": *********, "paxID": 268501431, "method": "APOS", "status": "1", "paidDate": "2025-05-12T19:43:04", "paidCurr": "AED", "paidAmt": 530, "baseCurr": "AED", "baseAmt": 530, "userID": "cashier.nib4", "channelID": 1, "paymentComment": "20591", "authCode": "305336", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "16087456:181207:2025-05-13T01:40:00 AM", "LFID": 16087456, "PFID": 181207, "org": "DXB", "dest": "TBS", "depDate": "2025-05-13T01:40:00", "depTime": "2025-05-13T01:40:00", "arrTime": "2025-05-13T05:05:00", "carrier": "FZ", "flightNum": "713", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "713", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:49 PM"}, {"key": "16087456:181207:2025-05-14T01:40:00 AM", "LFID": 16087456, "PFID": 181207, "org": "DXB", "dest": "TBS", "depDate": "2025-05-14T01:40:00", "depTime": "2025-05-14T01:40:00", "arrTime": "2025-05-14T05:05:00", "carrier": "FZ", "flightNum": "713", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "713", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:49 PM"}, {"key": "16087456:181207:2025-05-17T01:40:00 AM", "LFID": 16087456, "PFID": 181207, "org": "DXB", "dest": "TBS", "depDate": "2025-05-17T01:40:00", "depTime": "2025-05-17T01:40:00", "arrTime": "2025-05-17T05:05:00", "carrier": "FZ", "flightNum": "713", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "713", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:49 PM"}, {"key": "16087476:181237:2025-05-21T04:35:00 PM", "LFID": 16087476, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-05-21T16:35:00", "depTime": "2025-05-21T16:35:00", "arrTime": "2025-05-21T19:50:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/16/2025 12:53:38 PM"}, {"key": "16087474:181235:2025-05-23T06:05:00 AM", "LFID": 16087474, "PFID": 181235, "org": "TBS", "dest": "DXB", "depDate": "2025-05-23T06:05:00", "depTime": "2025-05-23T06:05:00", "arrTime": "2025-05-23T09:15:00", "carrier": "FZ", "flightNum": "714", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "714", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:26 PM"}, {"key": "16087474:181235:2025-05-26T06:05:00 AM", "LFID": 16087474, "PFID": 181235, "org": "TBS", "dest": "DXB", "depDate": "2025-05-26T06:05:00", "depTime": "2025-05-26T06:05:00", "arrTime": "2025-05-26T09:15:00", "carrier": "FZ", "flightNum": "714", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "714", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:26 PM"}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": 1340052508, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:58", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052508:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T12:44:58"}, {"chargeID": 1341537968, "codeType": "INSU", "taxChargeID": 1341537965, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:06", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052508, "paymentMap": [{"key": "1341537968:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1339953374, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1339953372, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953374:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1339953376, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1339953372, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953376:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339953377, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1339953372, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953377:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339953378, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1339953372, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953378:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1339953375, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1339953372, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953375:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1341537966, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1341537965, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:06", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953375, "paymentMap": [{"key": "1341537966:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1341538030, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1341537965, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:06", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953374, "paymentMap": [{"key": "1341538030:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1341538031, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1341537965, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953376, "paymentMap": [{"key": "1341538031:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341538032, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341537965, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953377, "paymentMap": [{"key": "1341538032:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341538033, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341537965, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953378, "paymentMap": [{"key": "1341538033:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1339953372, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "FZ 713 DXB-TBS 13May2025 Tue 01:40 05:05\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953372:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": 1341537965, "codeType": "AIR", "amt": -415, "curr": "AED", "originalAmt": -415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:06", "desc": "FZ 713 DXB-TBS 13May2025 Tue 01:40 05:05\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953372, "paymentMap": [{"key": "1341537965:*********", "paymentID": *********, "amt": -415, "approveCode": 0}]}, {"chargeID": 1339953373, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953373:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340052616, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:58", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181207", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052616:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1341538029, "codeType": "FRST", "taxChargeID": 1341537965, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:06", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052616, "paymentMap": [{"key": "1341538029:*********", "paymentID": *********, "amt": -60, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:07", "billDate": "2025-05-11T17:02:07", "desc": "CancelNoRefund FZ 713 DXB - TBS 13.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1339953379, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1339953372, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341538034, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341537965, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953379, "paymentMap": []}, {"chargeID": 1339953380, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1339953372, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181207"}, {"chargeID": 1341537967, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341537965, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:06", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953380, "paymentMap": [], "PFID": "181207"}]}, {"recNum": 4, "charges": [{"chargeID": 1340052710, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:59", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052710:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T12:44:59"}, {"chargeID": 1341538037, "codeType": "INSU", "taxChargeID": 1341538036, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:07", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052710, "paymentMap": [{"key": "1341538037:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1339953383, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1339953381, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953383:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1339953384, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1339953381, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953384:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1339953385, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1339953381, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953385:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339953386, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1339953381, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953386:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1341538038, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1341538036, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953383, "paymentMap": [{"key": "1341538038:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1341538039, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1341538036, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "GE: Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953384, "paymentMap": [{"key": "1341538039:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1341538041, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341538036, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953386, "paymentMap": [{"key": "1341538041:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1341538045, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341538036, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953385, "paymentMap": [{"key": "1341538045:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1339953381, "codeType": "AIR", "amt": 330, "curr": "AED", "originalAmt": 330, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "FZ 712 TBS-DXB 21May2025 Wed 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953381:*********", "paymentID": *********, "amt": 330, "approveCode": 0}]}, {"chargeID": 1341538036, "codeType": "AIR", "amt": -330, "curr": "AED", "originalAmt": -330, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "FZ 712 TBS-DXB 21May2025 Wed 16:35 19:45\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953381, "paymentMap": [{"key": "1341538036:*********", "paymentID": *********, "amt": -330, "approveCode": 0}]}, {"chargeID": 1339953382, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953382:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340052618, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:59", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181237", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052618:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181237"}, {"chargeID": 1341538044, "codeType": "FRST", "taxChargeID": 1341538036, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:07", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052618, "paymentMap": [{"key": "1341538044:*********", "paymentID": *********, "amt": -60, "approveCode": 0}], "PFID": "181237"}, {"chargeID": 1341538046, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:07", "billDate": "2025-05-11T17:02:07", "desc": "CancelNoRefund FZ 712 TBS - DXB 21.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538046:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1340070490, "codeType": "BUPL", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:55:11", "desc": "BUPL", "comment": "FLXID:GCC-AE DXB-GYD/TBS/TURKEYZONE/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070490:*********", "paymentID": *********, "amt": 35, "approveCode": 0}]}, {"chargeID": 1341538040, "codeType": "BUPL", "taxChargeID": 1341538036, "amt": -35, "curr": "AED", "originalAmt": -35, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:07", "desc": "BUPL", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340070490, "paymentMap": [{"key": "1341538040:*********", "paymentID": *********, "amt": -35, "approveCode": 0}]}, {"chargeID": 1339953387, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1339953381, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341538042, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341538036, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953387, "paymentMap": []}, {"chargeID": 1339953388, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1339953381, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1341538043, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341538036, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:07", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953388, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 5, "charges": [{"chargeID": 1343292971, "codeType": "INSU", "taxChargeID": 1343292948, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:21", "billDate": "2025-05-12T19:30:09", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********7, "paymentMap": [{"key": "1343292971:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:09"}, {"chargeID": *********7, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:21", "billDate": "2025-05-11T17:02:08", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********7:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1343292974, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343292948, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********2, "paymentMap": [{"key": "1343292974:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1343292975, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343292948, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********1, "paymentMap": [{"key": "1343292975:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343292976, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343292948, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********0, "paymentMap": [{"key": "1343292976:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343292977, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343292948, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538049, "paymentMap": [{"key": "1343292977:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1343292978, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343292948, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:07", "billDate": "2025-05-12T19:30:09", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538048, "paymentMap": [{"key": "1343292978:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1341538048, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1341538047, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:07", "billDate": "2025-05-11T17:02:08", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538048:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1341538049, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1341538047, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538049:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": *********0, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1341538047, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********0:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********1, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341538047, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********1:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********2, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341538047, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********2:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343292948, "codeType": "AIR", "amt": -415, "curr": "AED", "originalAmt": -415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:07", "billDate": "2025-05-12T19:30:09", "desc": "FZ 713 DXB-TBS 17May2025 Sat 01:40 05:05\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538047, "paymentMap": [{"key": "1343292948:*********", "paymentID": *********, "amt": -415, "approveCode": 0}]}, {"chargeID": 1341538047, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:07", "billDate": "2025-05-11T17:02:07", "desc": "FZ 713 DXB-TBS 17May2025 Sat 01:40 05:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538047:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": *********5, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********5:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343292970, "codeType": "FRST", "taxChargeID": 1343292948, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:01", "billDate": "2025-05-12T19:30:09", "desc": "Special Service Request:FRST-8F", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********6, "paymentMap": [{"key": "1343292970:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181207"}, {"chargeID": *********6, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:01", "billDate": "2025-05-11T17:02:08", "desc": "Special Service Request:FRST-8F", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********6:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:09", "billDate": "2025-05-12T19:30:09", "desc": "CancelNoRefund FZ 713 DXB - TBS 17.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343292973, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343292948, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********3, "paymentMap": []}, {"chargeID": *********3, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341538047, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343292972, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343292948, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********4, "paymentMap": [], "PFID": "181207"}, {"chargeID": *********4, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341538047, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181207"}]}, {"recNum": 6, "charges": [{"chargeID": 1343292982, "codeType": "INSU", "taxChargeID": 1343292981, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:21", "billDate": "2025-05-12T19:30:10", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********8, "paymentMap": [{"key": "1343292982:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:10"}, {"chargeID": *********8, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:21", "billDate": "2025-05-11T17:02:08", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********8:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1343292984, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1343292981, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:10", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********9, "paymentMap": [{"key": "1343292984:*********", "paymentID": *********, "amt": -12.58, "approveCode": 0}, {"key": "1343292984:*********", "paymentID": *********, "amt": -17.42, "approveCode": 0}]}, {"chargeID": 1343292985, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1343292981, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:10", "desc": "GE: Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538060, "paymentMap": [{"key": "1343292985:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1343292986, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343292981, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:10", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538061, "paymentMap": [{"key": "1343292986:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343292987, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343292981, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:10", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********3, "paymentMap": [{"key": "1343292987:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": *********9, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": *********8, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********9:*********", "paymentID": *********, "amt": 17.42, "approveCode": 0}, {"key": "*********9:*********", "paymentID": *********, "amt": 12.58, "approveCode": 0}]}, {"chargeID": 1341538060, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": *********8, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538060:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1341538061, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********8, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538061:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": *********3, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********8, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********3:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343292981, "codeType": "AIR", "amt": -420, "curr": "AED", "originalAmt": -420, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:09", "desc": "FZ 714 TBS-DXB 26May2025 Mon 06:05 09:15\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********8, "paymentMap": [{"key": "1343292981:*********", "paymentID": *********, "amt": -267.43, "approveCode": 0}, {"key": "1343292981:*********", "paymentID": *********, "amt": -152.57, "approveCode": 0}]}, {"chargeID": *********8, "codeType": "AIR", "amt": 420, "curr": "AED", "originalAmt": 420, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "FZ 714 TBS-DXB 26May2025 Mon 06:05 09:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********8:*********", "paymentID": *********, "amt": 267.43, "approveCode": 0}, {"key": "*********8:*********", "paymentID": *********, "amt": 152.57, "approveCode": 0}]}, {"chargeID": *********6, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********6:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343292990, "codeType": "FRST", "taxChargeID": 1343292981, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:11", "billDate": "2025-05-12T19:30:10", "desc": "Special Service Request:FRST-7F", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********7, "paymentMap": [{"key": "1343292990:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181235"}, {"chargeID": *********7, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:11", "billDate": "2025-05-11T17:02:08", "desc": "Special Service Request:FRST-7F", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********7:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181235"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "CancelNoRefund FZ 714 TBS - DXB 26.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343292983, "codeType": "BUPL", "taxChargeID": 1343292981, "amt": -35, "curr": "AED", "originalAmt": -35, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:22", "billDate": "2025-05-12T19:30:10", "desc": "BUPL", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********9, "paymentMap": [{"key": "1343292983:*********", "paymentID": *********, "amt": -35, "approveCode": 0}]}, {"chargeID": *********9, "codeType": "BUPL", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:22", "billDate": "2025-05-11T17:02:09", "desc": "BUPL", "comment": "FLXID:GCC-AE DXB-GYD/TBS/TURKEYZONE/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********9:*********", "paymentID": *********, "amt": 35, "approveCode": 0}]}, {"chargeID": 1343292988, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343292981, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:10", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********4, "paymentMap": []}, {"chargeID": *********4, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********8, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343292989, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343292981, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-12T19:30:10", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********5, "paymentMap": [], "PFID": "181235"}, {"chargeID": *********5, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": *********8, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:08", "billDate": "2025-05-11T17:02:08", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181235"}]}, {"recNum": 9, "charges": [{"chargeID": 1343293006, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:25:36", "billDate": "2025-05-12T19:30:10", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293006:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:10"}, {"chargeID": 1343292993, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343292992, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343292993:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1343292994, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343292992, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343292994:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1343292995, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343292992, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343292995:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343292996, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343292992, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343292996:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343292997, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343292992, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343292997:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343293007, "codeType": "AFEE", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:27:48", "billDate": "2025-05-12T19:30:10", "desc": "Special Service Request", "comment": "AIRPORT ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293007:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1343292992, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "FZ 713 DXB-TBS 14May2025 Wed 01:40 05:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343292992:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": 1343373884, "codeType": "NSST", "amt": 32, "curr": "AED", "originalAmt": 32, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T21:28:17", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343373884:*********", "paymentID": *********, "amt": 32, "approveCode": 0}], "PFID": "181207", "ssrCommentId": "*********"}, {"chargeID": 1343293004, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343292992, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343293005, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343292992, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181207"}]}, {"recNum": 10, "charges": [{"chargeID": 1343293015, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:25:36", "billDate": "2025-05-12T19:30:11", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293015:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:11"}, {"chargeID": 1343293009, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1343293008, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293009:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1343293010, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1343293008, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293010:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1343293011, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343293008, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:11", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293011:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343293012, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343293008, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:11", "billDate": "2025-05-12T19:30:11", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293012:*********", "paymentID": *********, "amt": 32.58, "approveCode": 0}, {"key": "1343293012:*********", "paymentID": *********, "amt": 157.42, "approveCode": 0}]}, {"chargeID": 1343293008, "codeType": "AIR", "amt": 430, "curr": "AED", "originalAmt": 430, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:10", "billDate": "2025-05-12T19:30:10", "desc": "FZ 714 TBS-DXB 23May2025 Fri 06:05 09:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293008:*********", "paymentID": *********, "amt": 32.43, "approveCode": 0}, {"key": "1343293008:*********", "paymentID": *********, "amt": 165.15, "approveCode": 0}, {"key": "1343293008:*********", "paymentID": *********, "amt": 232.42, "approveCode": 0}]}, {"chargeID": 1356656959, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "billDate": "2025-05-22T02:59:15", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356656959:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181235", "ssrCommentId": "136395114"}, {"chargeID": 1343293016, "codeType": "BUPL", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:25:36", "billDate": "2025-05-12T19:30:11", "desc": "BUPL", "comment": "FLXID:GCC-AE DXB-GYD/TBS/TURKEYZONE/RUSSIAZONE/FRU/ALA/SJJ:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293016:*********", "paymentID": *********, "amt": 35, "approveCode": 0}]}, {"chargeID": 1343293013, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343293008, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:11", "billDate": "2025-05-12T19:30:11", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343293014, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343293008, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:11", "billDate": "2025-05-12T19:30:11", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181235"}]}, {"recNum": 1, "charges": [{"chargeID": 1340052709, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:58", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052709:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T12:44:58"}, {"chargeID": 1341538088, "codeType": "INSU", "taxChargeID": 1341538080, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:09", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052709, "paymentMap": [{"key": "1341538088:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1339953300, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1339953295, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953300:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339953301, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1339953295, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953301:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1339953297, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1339953295, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953297:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1339953298, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1339953295, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953298:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1339953299, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1339953295, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953299:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341538081, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1341538080, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953299, "paymentMap": [{"key": "1341538081:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341538082, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1341538080, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953298, "paymentMap": [{"key": "1341538082:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1341538083, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1341538080, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953297, "paymentMap": [{"key": "1341538083:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1341538086, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341538080, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953301, "paymentMap": [{"key": "1341538086:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1341538087, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341538080, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953300, "paymentMap": [{"key": "1341538087:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1339953295, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "FZ 713 DXB-TBS 13May2025 Tue 01:40 05:05\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953295:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": 1341538080, "codeType": "AIR", "amt": -415, "curr": "AED", "originalAmt": -415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "FZ 713 DXB-TBS 13May2025 Tue 01:40 05:05\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953295, "paymentMap": [{"key": "1341538080:*********", "paymentID": *********, "amt": -415, "approveCode": 0}]}, {"chargeID": 1340030843, "codeType": "PMNT", "amt": 90.6, "curr": "AED", "originalAmt": 90.6, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:23:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340030843:*********", "paymentID": *********, "amt": 90.6, "approveCode": 0}]}, {"chargeID": 1340059974, "codeType": "PMNT", "amt": 10.21, "curr": "AED", "originalAmt": 10.21, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:45:44", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340059974:*********", "paymentID": *********, "amt": 10.21, "approveCode": 0}]}, {"chargeID": 1340073409, "codeType": "PMNT", "amt": 1.05, "curr": "AED", "originalAmt": 1.05, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:55:23", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340073409:*********", "paymentID": *********, "amt": 1.05, "approveCode": 0}]}, {"chargeID": 1339953296, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953296:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340052615, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:58", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181207", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052615:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1341538089, "codeType": "FRST", "taxChargeID": 1341538080, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:09", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052615, "paymentMap": [{"key": "1341538089:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1341538090, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:09", "billDate": "2025-05-11T17:02:09", "desc": "CancelNoRefund FZ 713 DXB - TBS 13.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538090:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1339953302, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1339953295, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341538085, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341538080, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953302, "paymentMap": []}, {"chargeID": 1339953303, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1339953295, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181207"}, {"chargeID": 1341538084, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341538080, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953303, "paymentMap": [], "PFID": "181207"}]}, {"recNum": 2, "charges": [{"chargeID": 1340052711, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:58", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052711:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-10T12:44:58"}, {"chargeID": 1341538099, "codeType": "INSU", "taxChargeID": 1341538091, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:10", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052711, "paymentMap": [{"key": "1341538099:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1339953306, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1339953304, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953306:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1339953307, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1339953304, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953307:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1339953308, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1339953304, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953308:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1339953369, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1339953304, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953369:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1341538094, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341538091, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:10", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953369, "paymentMap": [{"key": "1341538094:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1341538095, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341538091, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:10", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953308, "paymentMap": [{"key": "1341538095:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1341538096, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1341538091, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:10", "desc": "GE: Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953307, "paymentMap": [{"key": "1341538096:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1341538100, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1341538091, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:10", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953306, "paymentMap": [{"key": "1341538100:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1339953304, "codeType": "AIR", "amt": 330, "curr": "AED", "originalAmt": 330, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "FZ 712 TBS-DXB 21May2025 Wed 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953304:*********", "paymentID": *********, "amt": 330, "approveCode": 0}]}, {"chargeID": 1341538091, "codeType": "AIR", "amt": -330, "curr": "AED", "originalAmt": -330, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:09", "desc": "FZ 712 TBS-DXB 21May2025 Wed 16:35 19:45\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953304, "paymentMap": [{"key": "1341538091:*********", "paymentID": *********, "amt": -330, "approveCode": 0}]}, {"chargeID": 1339953305, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1339953305:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340052617, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-10T12:44:58", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181237", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340052617:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181237"}, {"chargeID": 1341538098, "codeType": "FRST", "taxChargeID": 1341538091, "amt": -65, "curr": "AED", "originalAmt": -65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-11T17:02:10", "desc": "FRST", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340052617, "paymentMap": [{"key": "1341538098:*********", "paymentID": *********, "amt": -65, "approveCode": 0}], "PFID": "181237"}, {"chargeID": 1341538101, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "CancelNoRefund FZ 712 TBS - DXB 21.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538101:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1339953370, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1339953304, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1341538093, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341538091, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:10", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953370, "paymentMap": []}, {"chargeID": 1339953371, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1339953304, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-10T11:20:39", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1341538097, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341538091, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-10T11:20:38", "billDate": "2025-05-11T17:02:10", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1339953371, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 7, "charges": [{"chargeID": 1343293018, "codeType": "INSU", "taxChargeID": 1343293017, "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:22", "billDate": "2025-05-12T19:30:11", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538113, "paymentMap": [{"key": "1343293018:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:11"}, {"chargeID": 1341538113, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:22", "billDate": "2025-05-11T17:02:11", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538113:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1343293022, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343293017, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538107, "paymentMap": [{"key": "1343293022:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343293023, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343293017, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538106, "paymentMap": [{"key": "1343293023:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343293024, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343293017, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538105, "paymentMap": [{"key": "1343293024:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1343293026, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343293017, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538103, "paymentMap": [{"key": "1343293026:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1343293027, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343293017, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538108, "paymentMap": [{"key": "1343293027:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1341538103, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1341538102, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538103:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1341538105, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1341538102, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538105:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1341538106, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1341538102, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538106:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341538107, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341538102, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538107:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341538108, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341538102, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538108:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343293017, "codeType": "AIR", "amt": -415, "curr": "AED", "originalAmt": -415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "FZ 713 DXB-TBS 17May2025 Sat 01:40 05:05\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538102, "paymentMap": [{"key": "1343293017:*********", "paymentID": *********, "amt": -415, "approveCode": 0}]}, {"chargeID": 1341538102, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "FZ 713 DXB-TBS 17May2025 Sat 01:40 05:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538102:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": 1341561522, "codeType": "PMNT", "amt": 30.6, "curr": "AED", "originalAmt": 30.6, "originalCurr": "AED", "status": 0, "billDate": "2025-05-11T17:26:26", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341561522:*********", "paymentID": *********, "amt": 30.6, "approveCode": 0}]}, {"chargeID": 1341538111, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538111:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343293019, "codeType": "FRST", "taxChargeID": 1343293017, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:05", "billDate": "2025-05-12T19:30:11", "desc": "Special Service Request:FRST-8E", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538112, "paymentMap": [{"key": "1343293019:*********", "paymentID": *********, "amt": -60, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1341538112, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:05", "billDate": "2025-05-11T17:02:10", "desc": "Special Service Request:FRST-8E", "comment": "FLXID:FRST_Zone3_73B_73M_MID:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538112:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181207"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:11", "billDate": "2025-05-12T19:30:11", "desc": "CancelNoRefund FZ 713 DXB - TBS 17.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343293021, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343293017, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538109, "paymentMap": []}, {"chargeID": 1341538109, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341538102, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343293020, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343293017, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-12T19:30:11", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538110, "paymentMap": [], "PFID": "181207"}, {"chargeID": 1341538110, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341538102, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:10", "billDate": "2025-05-11T17:02:10", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181207"}]}, {"recNum": 8, "charges": [{"chargeID": 1343293053, "codeType": "INSU", "taxChargeID": 1343293049, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:22", "billDate": "2025-05-12T19:30:12", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538123, "paymentMap": [{"key": "1343293053:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:12"}, {"chargeID": 1341538123, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:22", "billDate": "2025-05-11T17:02:11", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538123:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}"}, {"chargeID": 1343293050, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1343293049, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:12", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538115, "paymentMap": [{"key": "1343293050:*********", "paymentID": *********, "amt": -27.58, "approveCode": 0}, {"key": "1343293050:*********", "paymentID": *********, "amt": -2.42, "approveCode": 0}]}, {"chargeID": 1343293051, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1343293049, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:12", "desc": "GE: Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538116, "paymentMap": [{"key": "1343293051:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1343293052, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343293049, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:12", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538117, "paymentMap": [{"key": "1343293052:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343293057, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343293049, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:12", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538118, "paymentMap": [{"key": "1343293057:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1341538115, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1341538114, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538115:*********", "paymentID": *********, "amt": 2.42, "approveCode": 0}, {"key": "1341538115:*********", "paymentID": *********, "amt": 27.58, "approveCode": 0}]}, {"chargeID": 1341538116, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1341538114, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538116:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1341538117, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1341538114, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538117:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1341538118, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1341538114, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538118:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343293049, "codeType": "AIR", "amt": -420, "curr": "AED", "originalAmt": -420, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:11", "desc": "FZ 714 TBS-DXB 26May2025 Mon 06:05 09:15\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538114, "paymentMap": [{"key": "1343293049:*********", "paymentID": *********, "amt": -272.43, "approveCode": 0}, {"key": "1343293049:*********", "paymentID": *********, "amt": -147.57, "approveCode": 0}]}, {"chargeID": 1341538114, "codeType": "AIR", "amt": 420, "curr": "AED", "originalAmt": 420, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "FZ 714 TBS-DXB 26May2025 Mon 06:05 09:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538114:*********", "paymentID": *********, "amt": 272.43, "approveCode": 0}, {"key": "1341538114:*********", "paymentID": *********, "amt": 147.57, "approveCode": 0}]}, {"chargeID": 1341538121, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538121:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343293056, "codeType": "FRST", "taxChargeID": 1343293049, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:14", "billDate": "2025-05-12T19:30:12", "desc": "Special Service Request:FRST-7E", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538122, "paymentMap": [{"key": "1343293056:*********", "paymentID": *********, "amt": -60, "approveCode": 0}], "PFID": "181235"}, {"chargeID": 1341538122, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:00:14", "billDate": "2025-05-11T17:02:11", "desc": "Special Service Request:FRST-7E", "comment": "FLXID:FRST_Zone3_73B_73M_MID:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1341538122:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181235"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "CancelNoRefund FZ 714 TBS - DXB 26.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1343293054, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343293049, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:12", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538119, "paymentMap": []}, {"chargeID": 1341538119, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1341538114, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343293055, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343293049, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-12T19:30:12", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1341538120, "paymentMap": [], "PFID": "181235"}, {"chargeID": 1341538120, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1341538114, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-11T17:02:11", "billDate": "2025-05-11T17:02:11", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181235"}]}, {"recNum": 11, "charges": [{"chargeID": 1343293067, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:25:36", "billDate": "2025-05-12T19:30:12", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293067:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:12"}, {"chargeID": 1343293060, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1343293059, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293060:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1343293061, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1343293059, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293061:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1343293062, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1343293059, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293062:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343293063, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343293059, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293063:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343293064, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343293059, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293064:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343293068, "codeType": "AFEE", "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:28:34", "billDate": "2025-05-12T19:30:12", "desc": "Special Service Request", "comment": "AIRPORT ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293068:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1343293059, "codeType": "AIR", "amt": 415, "curr": "AED", "originalAmt": 415, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "FZ 713 DXB-TBS 14May2025 Wed 01:40 05:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293059:*********", "paymentID": *********, "amt": 415, "approveCode": 0}]}, {"chargeID": 1343375523, "codeType": "PMNT", "amt": 1.86, "curr": "AED", "originalAmt": 1.86, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T21:31:03", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343375523:*********", "paymentID": *********, "amt": 1.86, "approveCode": 0}]}, {"chargeID": 1343373883, "codeType": "NSST", "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T21:28:17", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343373883:*********", "paymentID": *********, "amt": 30, "approveCode": 0}], "PFID": "181207", "ssrCommentId": "*********"}, {"chargeID": 1343293065, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343293059, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343293066, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343293059, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181207"}]}, {"recNum": 12, "charges": [{"chargeID": 1343293076, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:25:36", "billDate": "2025-05-12T19:30:13", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293076:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"24.58\",\"Tax\":\"1.17\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-12T19:30:13"}, {"chargeID": 1343293070, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1343293069, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293070:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1343293071, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1343293069, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293071:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1343293072, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343293069, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293072:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343293073, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343293069, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293073:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}, {"key": "1343293073:*********", "paymentID": *********, "amt": 167.42, "approveCode": 0}]}, {"chargeID": 1343293069, "codeType": "AIR", "amt": 430, "curr": "AED", "originalAmt": 430, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "FZ 714 TBS-DXB 23May2025 Fri 06:05 09:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343293069:*********", "paymentID": *********, "amt": 32.43, "approveCode": 0}, {"key": "1343293069:*********", "paymentID": *********, "amt": 175.15, "approveCode": 0}, {"key": "1343293069:*********", "paymentID": *********, "amt": 222.42, "approveCode": 0}]}, {"chargeID": 1356657215, "codeType": "PMNT", "amt": 3.75, "curr": "AED", "originalAmt": 3.75, "originalCurr": "AED", "status": 1, "billDate": "2025-05-22T03:00:07", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356657215:*********", "paymentID": *********, "amt": 3.75, "approveCode": 0}]}, {"chargeID": 1356656958, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "billDate": "2025-05-22T02:59:15", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356656958:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181235", "ssrCommentId": "*********"}, {"chargeID": 1343293074, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1343293069, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:12", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343293075, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343293069, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-12T19:30:12", "billDate": "2025-05-12T19:30:13", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181235"}]}], "parentPNRs": [], "childPNRs": []}