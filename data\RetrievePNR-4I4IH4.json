{"seriesNum": "299", "PNR": "4I4IH4", "bookAgent": "MOBILE_APP", "resCurrency": "EUR", "PNRPin": "82943107", "bookDate": "2025-05-19T08:22:58", "modifyDate": "2025-05-26T09:45:00", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "158bk2uc4dw9ga6dw5e3d6u493248a933476cd67def1", "securityGUID": "158bk2uc4dw9ga6dw5e3d6u493248a933476cd67def1", "lastLoadGUID": "ac4aff34-7422-431a-82c2-305831dbc3d0", "isAsyncPNR": false, "MasterPNR": "4I4IH4", "segments": [{"segKey": "16087525:16087525:6/8/2025 8:10:00 AM", "LFID": 16087525, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16087525", "org": "DXB", "dest": "TIA", "depTime": "2025-06-08T08:10:00", "depTimeGMT": "2025-06-08T04:10:00", "arrTime": "2025-06-08T11:55:00", "operCarrier": "FZ", "operFlightNum": "745", "mrktCarrier": "FZ ", "mrktFlightNum": "745", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181256, "depDate": "2025-06-08T08:10:00", "legKey": "16087525:181256:6/8/2025 8:10:00 AM", "customerKey": "726D2ABDD51C102D0763C5BD925835F2EB04BCC97C84E2BA65AE6BEF76D17730"}], "active": true, "changeType": "TK"}, {"segKey": "16087473:16087473:5/21/2025 12:55:00 PM", "LFID": 16087473, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087473", "org": "TIA", "dest": "DXB", "depTime": "2025-05-21T12:55:00", "depTimeGMT": "2025-05-21T10:55:00", "arrTime": "2025-05-21T20:15:00", "operCarrier": "FZ", "operFlightNum": "746", "mrktCarrier": "FZ ", "mrktFlightNum": "746", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181234, "depDate": "2025-05-21T12:55:00", "legKey": "16087473:181234:5/21/2025 12:55:00 PM", "customerKey": "FC167CE839B1A0023503C1C187998E7202CC699DF36301E841CCF7A9F352DE7F"}], "active": true, "changeType": "TK"}, {"segKey": "16087525:16087525:5/30/2025 8:10:00 AM", "LFID": 16087525, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087525", "org": "DXB", "dest": "TIA", "depTime": "2025-05-30T08:10:00", "depTimeGMT": "2025-05-30T04:10:00", "arrTime": "2025-05-30T11:55:00", "operCarrier": "FZ", "operFlightNum": "745", "mrktCarrier": "FZ ", "mrktFlightNum": "745", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181256, "depDate": "2025-05-30T08:10:00", "legKey": "16087525:181256:5/30/2025 8:10:00 AM", "customerKey": "FE35DB863F2CD9870ED2A41AB7601CA48D8C15FC5D30F6BBC24782E45954104F"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 269430255, "fName": "MARSELA", "lName": "LALA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "N", "status": 5, "fareClass": "N", "operFareClass": "N", "FBC": "NRL8AL2", "fareBrand": "Flex", "cabin": "ECONOMY", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682aea100007780000001630#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-05-19T08:22:58"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "M", "status": 0, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AL2", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682aea100007780000001630#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-19T08:22:58"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/26/2025 9:44:47 AM", "provider": "<PERSON>", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NRL8AL2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "6XUTM-H27YB-INS/250d8894-47f1-4bb9-bcf8-416c5d6158c8", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68343719000777000001f3c5#269430255#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 12, "bookDate": "2025-05-26T09:44:46"}]}], "payments": [{"paymentID": *********, "paxID": 270184140, "method": "IPAY", "status": "1", "paidDate": "2025-05-26T09:44:54", "cardNum": "************6351", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 7.66, "baseCurr": "EUR", "baseAmt": 7.66, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "393618", "reference": "23258908", "externalReference": "23258908", "tranId": "21634994", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "ALBMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21634994}, {"paymentID": *********, "paxID": 269430288, "method": "IPAY", "status": "1", "paidDate": "2025-05-19T08:23:04", "cardNum": "************6351", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 736.1, "baseCurr": "EUR", "baseAmt": 736.1, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "307092", "reference": "23121926", "externalReference": "23121926", "tranId": "21493953", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "ALBMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21493953}], "OAFlights": null, "physicalFlights": [{"key": "16087473:181234:2025-05-21T12:55:00 PM", "LFID": 16087473, "PFID": 181234, "org": "TIA", "dest": "DXB", "depDate": "2025-05-21T12:55:00", "depTime": "2025-05-21T12:55:00", "arrTime": "2025-05-21T20:15:00", "carrier": "FZ", "flightNum": "746", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "746", "flightStatus": "CLOSED", "originMetroGroup": "TIA", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Tirana", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "11/29/2024 4:33:52 AM"}, {"key": "16087525:181256:2025-05-30T08:10:00 AM", "LFID": 16087525, "PFID": 181256, "org": "DXB", "dest": "TIA", "depDate": "2025-05-30T08:10:00", "depTime": "2025-05-30T08:10:00", "arrTime": "2025-05-30T11:55:00", "carrier": "FZ", "flightNum": "745", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "745", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TIA", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tirana", "isActive": false, "changeType": "TK", "flightChangeTime": "11/29/2024 4:33:48 AM"}, {"key": "16087525:181256:2025-06-08T08:10:00 AM", "LFID": 16087525, "PFID": 181256, "org": "DXB", "dest": "TIA", "depDate": "2025-06-08T08:10:00", "depTime": "2025-06-08T08:10:00", "arrTime": "2025-06-08T11:55:00", "carrier": "FZ", "flightNum": "745", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "745", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "TIA", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tirana", "isActive": true, "changeType": "TK", "flightChangeTime": "11/29/2024 4:33:48 AM"}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": 1362260558, "codeType": "INSU", "amt": 8.54, "curr": "EUR", "originalAmt": 8.54, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-26T09:44:47", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362260558:*********", "paymentID": *********, "amt": 8.54, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.14\",\"Premium\":\"9.72\",\"Tax\":\"0.47\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-26T09:44:47"}, {"chargeID": 1362255191, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1362255190, "amt": 10.95, "curr": "EUR", "originalAmt": 10.95, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362255191:*********", "paymentID": *********, "amt": 10.95, "approveCode": 0}]}, {"chargeID": 1362255195, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1362255190, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362255195:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1362255193, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1362255190, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362255193:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1362255194, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1362255190, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362255194:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1362255192, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1362255190, "amt": 18.25, "curr": "EUR", "originalAmt": 18.25, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362255192:*********", "paymentID": *********, "amt": 18.25, "approveCode": 0}]}, {"chargeID": 1362255190, "codeType": "AIR", "amt": 237, "curr": "EUR", "originalAmt": 237, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-26T09:44:47", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362255190:*********", "paymentID": *********, "amt": 237, "approveCode": 0}]}, {"chargeID": 1362262807, "codeType": "PMNT", "amt": 0.22, "curr": "EUR", "originalAmt": 0.22, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-26T09:45:00", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362262807:*********", "paymentID": *********, "amt": 0.22, "approveCode": 0}]}, {"chargeID": 1362260588, "codeType": "XLGR", "amt": 42.64, "curr": "EUR", "originalAmt": 42.64, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-26T09:44:47", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181256", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1362260588:*********", "paymentID": *********, "amt": 35.2, "approveCode": 0}, {"key": "1362260588:*********", "paymentID": *********, "amt": 7.44, "approveCode": 0}], "PFID": "181256"}, {"chargeID": 1362255197, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1362255190, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1362255196, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1362255190, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1362255203, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181256"}]}, {"recNum": 1, "charges": [{"chargeID": 1352188733, "codeType": "TAX", "taxID": 12793, "taxCode": "HA", "taxChargeID": 1352188707, "amt": 4, "curr": "EUR", "originalAmt": 4, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Tirana Security Charge (International)", "comment": "Tirana Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188733:*********", "paymentID": *********, "amt": 4, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-19T08:22:57"}, {"chargeID": 1352188730, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352188707, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188730:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352188731, "codeType": "TAX", "taxID": 12790, "taxCode": "IX", "taxChargeID": 1352188707, "amt": 1, "curr": "EUR", "originalAmt": 1, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Border Crossing Tax (International)", "comment": "Border Crossing Tax (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188731:*********", "paymentID": *********, "amt": 1, "approveCode": 0}]}, {"chargeID": 1352188708, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352188707, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188708:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1352188732, "codeType": "TAX", "taxID": 12792, "taxCode": "AL", "taxChargeID": 1352188707, "amt": 10, "curr": "EUR", "originalAmt": 10, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188732:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1352188707, "codeType": "AIR", "amt": 193, "curr": "EUR", "originalAmt": 193, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-19T08:22:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188707:*********", "paymentID": *********, "amt": 193, "approveCode": 0}]}, {"chargeID": 1352189572, "codeType": "PMNT", "amt": 21.44, "curr": "EUR", "originalAmt": 21.44, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-19T08:23:08", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352189572:*********", "paymentID": *********, "amt": 21.44, "approveCode": 0}]}, {"chargeID": 1352188746, "codeType": "XLGR", "amt": 43.06, "curr": "EUR", "originalAmt": 43.06, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181234", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188746:*********", "paymentID": *********, "amt": 43.06, "approveCode": 0}], "PFID": "181234"}, {"chargeID": 1352188734, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352188707, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352188729, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1352188707, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352188749, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181234"}]}, {"recNum": 2, "charges": [{"chargeID": 1362255126, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352188736, "amt": -1.22, "curr": "EUR", "originalAmt": -1.22, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-26T09:44:46", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188737, "paymentMap": [{"key": "1362255126:*********", "paymentID": *********, "amt": -1.22, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-26T09:44:46"}, {"chargeID": 1362255125, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352188736, "amt": -10.95, "curr": "EUR", "originalAmt": -10.95, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-26T09:44:46", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188741, "paymentMap": [{"key": "1362255125:*********", "paymentID": *********, "amt": -10.95, "approveCode": 0}]}, {"chargeID": 1362255127, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352188736, "amt": -1.22, "curr": "EUR", "originalAmt": -1.22, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-26T09:44:46", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188742, "paymentMap": [{"key": "1362255127:*********", "paymentID": *********, "amt": -1.22, "approveCode": 0}]}, {"chargeID": 1362255189, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352188736, "amt": -75, "curr": "EUR", "originalAmt": -75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-26T09:44:46", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188739, "paymentMap": [{"key": "1362255189:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1362255128, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352188736, "amt": -18.25, "curr": "EUR", "originalAmt": -18.25, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-26T09:44:46", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188740, "paymentMap": [{"key": "1362255128:*********", "paymentID": *********, "amt": -18.25, "approveCode": 0}]}, {"chargeID": 1352188741, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352188736, "amt": 10.95, "curr": "EUR", "originalAmt": 10.95, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188741:*********", "paymentID": *********, "amt": 10.95, "approveCode": 0}]}, {"chargeID": 1352188737, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352188736, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188737:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1352188742, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352188736, "amt": 1.22, "curr": "EUR", "originalAmt": 1.22, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188742:*********", "paymentID": *********, "amt": 1.22, "approveCode": 0}]}, {"chargeID": 1352188740, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352188736, "amt": 18.25, "curr": "EUR", "originalAmt": 18.25, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188740:*********", "paymentID": *********, "amt": 18.25, "approveCode": 0}]}, {"chargeID": 1352188739, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352188736, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188739:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1362255124, "codeType": "AIR", "amt": -235, "curr": "EUR", "originalAmt": -235, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-26T09:44:46", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188736, "paymentMap": [{"key": "1362255124:*********", "paymentID": *********, "amt": -235, "approveCode": 0}]}, {"chargeID": 1352188736, "codeType": "AIR", "amt": 235, "curr": "EUR", "originalAmt": 235, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-19T08:22:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188736:*********", "paymentID": *********, "amt": 235, "approveCode": 0}]}, {"chargeID": 1362255123, "codeType": "XLGR", "amt": -45.74, "curr": "EUR", "originalAmt": -45.74, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-26T09:44:47", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352188748, "paymentMap": [{"key": "1362255123:*********", "paymentID": *********, "amt": -45.74, "approveCode": 0}], "PFID": "181256"}, {"chargeID": 1352188748, "codeType": "XLGR", "amt": 45.74, "curr": "EUR", "originalAmt": 45.74, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181256", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352188748:*********", "paymentID": *********, "amt": 45.74, "approveCode": 0}], "PFID": "181256"}, {"chargeID": 1352188743, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352188736, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352188738, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1352188736, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352188750, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T08:22:58", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181256"}]}], "parentPNRs": [], "childPNRs": []}