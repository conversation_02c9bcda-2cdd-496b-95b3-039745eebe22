{"id": "QFFRK-C4GFJ-INS", "status": "CONFIRMED", "currency": "AED", "total_price": 35.7, "total_price_formatted": "AED35.70", "partner_transaction_id": null, "created_at": "2025-05-23T15:36:38.225337Z", "updated_at": "2025-05-24T15:52:58.118556Z", "pds_url": "https://www.xcover.com/en/pds/QFFRK-C4GFJ-INS", "security_token": "iNVoe-EHDef-tYBqk-bUxSC", "quotes": [{"id": "22c817f0-7dba-4a00-a74e-2ba387b6e71a", "policy_start_date": "2025-05-23T15:36:38.215530+00:00", "policy_end_date": "2025-05-30T21:50:00+04:00", "status": "CONFIRMED", "price": 35.7, "price_formatted": "AED35.70", "policy": {"policy_type": "comprehensive_travel_insurance", "policy_type_version": "9", "policy_type_slug": "comprehensive_travel_insurance_v9", "policy_type_group_name": "travel", "policy_name": "FlyDubai - Comprehensive (ROW)", "policy_code": "00000000", "policy_version": "ee55df79-2855-4150-8ac8-bb9bfe56fd2b", "category": "comprehensive_travel_insurance", "content": {"title": "Comprehensive Travel Insurance", "header": null, "description": "n/a", "optout_msg": "", "inclusions": [], "exclusions": [], "disclaimer": "This policy is underwritten by Dubai Insurance Company and powered by XCover.com. By purchasing a policy, you agree that you are 84 years of age or below and that you have read and agree to the Terms & Conditions.", "disclaimer_html": "<p data-block-key=\"flhma\">This policy is underwritten by Dubai Insurance Company and powered by XCover.com. By purchasing a policy, you agree that you are 84 years of age or below and that you have read and agree to the <a href=\"https://www.xcover.com/en/pds/flydubai-comprehensive-travel-insurance\" rel=\"noopener noreferrer\" target=\"_blank\">Terms & Conditions</a>.</p>", "payment_disclaimer": "", "in_path_disclaimer": "", "extra_content": {}}, "underwriter": {"disclaimer": "Dubai Insurance Company", "name": "Dubai Insurance Company"}, "claim_selector_id": "eeafddeb-75a6-4381-beb5-8a7bd9731f38", "policy_currency": "AED"}, "insured": [{"id": "37b24e07-892d-4467-8843-fe076244f0c7", "first_name": "AISHA", "last_name": "JANY", "region": null}], "tax": {"total_tax": 1.7, "total_amount_without_tax": 34, "total_tax_formatted": "AED1.70", "total_amount_without_tax_formatted": "AED34.00", "taxes": [{"tax_amount": 1.7, "tax_code": "VAT", "tax_amount_formatted": "AED1.70"}]}, "duration": "7 02:13:21.784470", "benefits": [{"benefit_content_id": "e70c0627-e64b-4de3-8042-d0d10dcafab7", "description": "Medical Expenses Including Transportation, Evacuation and Repatriation of Mortal Remains*", "limit_description": "50,000 USD", "excess_description": "100 USD"}, {"benefit_content_id": "c3407f6c-3219-4497-96a3-5fe188ee91f2", "description": "Trip Cancellation", "limit_description": "1,000 USD"}, {"benefit_content_id": "39b97cf8-00cd-4040-b1b1-ba13e929b167", "description": "Trip Curtailment", "limit_description": "1,000 USD"}, {"benefit_content_id": "d80cd3f0-73fa-44b0-b5b0-f0c899439ae5", "description": "<PERSON> (Beyond 4 Hours)", "limit_description": "500 USD Max Limit, 50 USD per Hour Delay"}, {"benefit_content_id": "fd360a35-9367-4607-b059-f1cb7f80dfd8", "description": "Personal Accident - Accidental Death, Permanent Total Disability, Permanent Partial Disability", "limit_description": "25,000 USD"}, {"benefit_content_id": "ff5a1288-5120-48ed-a9e8-b8faaa82438d", "description": "Personal Liability", "limit_description": "50,000 USD"}, {"benefit_content_id": "d4dad855-d2ca-4ef5-a0bb-e41c5af60593", "description": "Compassionate Visit", "limit_description": "Return economy class airfare and accomodation for one accompanying person"}, {"benefit_content_id": "944ddfe8-a745-48c8-9911-29791c0111f6", "description": "Loss of Baggage, Personal Effects & Travel Documents", "limit_description": "1,000 USD"}, {"benefit_content_id": "5e2b5cc1-3e84-4ca4-9df5-9981be08650e", "description": "Single Item Limit", "parent_benefit_content_id": "944ddfe8-a745-48c8-9911-29791c0111f6", "parent_description": "Loss of Baggage, Personal Effects & Travel Documents", "limit_description": "250 USD"}, {"benefit_content_id": "6f5dc062-4552-4d6d-9c72-0ed7c0d977ca", "description": "Valuables limit", "parent_benefit_content_id": "944ddfe8-a745-48c8-9911-29791c0111f6", "parent_description": "Loss of Baggage, Personal Effects & Travel Documents", "limit_description": "500 USD"}, {"benefit_content_id": "10ab54b8-fda8-4277-99d8-be0f981d54cc", "description": "Baggage Arrival <PERSON> (Beyond 4 Hours)", "limit_description": "150 USD"}, {"benefit_content_id": "0e16a10a-2891-430d-91b3-37f68cca9de5", "description": "Missed Departure", "limit_description": "150 USD"}, {"benefit_content_id": "a994b70b-4d25-4a0e-9df7-9dedbf03082f", "description": "Legal Fees", "limit_description": "2,000 USD"}, {"benefit_content_id": "ff970ab2-d20e-429f-8df5-876f8d4361f7", "description": "Accidental Dental", "limit_description": "500 USD", "excess_description": "50 USD"}], "commission": {"partner_commission": 19.04, "partner_commission_formatted": "AED19.04", "surcharge_commission": 0, "surcharge_commission_formatted": "AED0.00", "total_commission": 19.04, "total_commission_formatted": "AED19.04"}, "created_at": "2025-05-23T15:36:38.215530Z", "confirmed_at": "2025-05-23T15:40:55.244744Z", "updated_at": "2025-05-23T15:41:09.773279Z", "cancelled_at": null, "is_renewable": false, "is_pricebeat_enabled": null, "cover_amount": null, "cover_amount_formatted": null, "pds_url": "https://www.xcover.com/en/pds/QFFRK-C4GFJ-INS?policy_type=comprehensive_travel_insurance_v9", "attachments": [], "files": [], "custom_documents": null, "extra_fields": {"trip_duration": 6, "usd": {"fx": "0.27", "tax": "0.46", "premium": "9.72"}}, "surcharge": {"total_amount": null, "total_amount_formatted": null, "surcharges": null}, "parent_quote_status": null, "experiment": null, "next_renewal": null, "can_be_cancelled": false, "third_party_admins": [], "ombudsman_list": []}], "coi": {"url": "https://www.xcover.com/en/coi/QFFRK-C4GFJ-INS?security_token=iNVoe-EHDef-tYBqk-bUxSC", "pdf": "https://www.xcover.com/en/coi/QFFRK-C4GFJ-INS.pdf?security_token=iNVoe-EHDef-tYBqk-bUxSC"}, "account_url": "https://www.xcover.com/en/account?id=fafe2b7d-273d-473e-8d11-f21a210ba53e&signup_token=5FALF-ct97f-MpiMc-bz5Yo&region=eu-central-1", "sign_up_url": "https://www.xcover.com/en/account?id=fafe2b7d-273d-473e-8d11-f21a210ba53e&signup_token=5FALF-ct97f-MpiMc-bz5Yo&region=eu-central-1", "policyholder": {"first_name": "AISHA", "last_name": "JANY", "email": "<EMAIL>", "phone": null, "address1": null, "address2": null, "postcode": null, "company": null, "company_reg_id": null, "middle_name": null, "country": "AE", "age": null, "city": null, "region": null, "secondary_email": null, "birth_date": null, "allow_updates": true, "fields_allowed_to_update": ["phone", "company", "last_name", "secondary_email", "address1", "first_name", "region", "city", "age", "birth_date", "middle_name", "address2", "tax_payer_id", "email", "company_reg_id", "postcode"]}, "total_tax": 1.7, "total_tax_formatted": "AED1.70", "total_premium": 34, "total_premium_formatted": "AED34.00", "fnol_link": "https://www.xcover.com/en/account/claims/fnol?bookingID=QFFRK-C4GFJ-INS&security_token=iNVoe-EHDef-tYBqk-bUxSC", "booking_agent": null, "partner": {"id": "ZDTIY", "slug": "fly<PERSON><PERSON><PERSON>", "name": "fly<PERSON><PERSON><PERSON>", "title": "fly<PERSON><PERSON><PERSON>", "logo": "https://static.xcover.com/media/partnerlogos/2025/03/25/image_35_new_2.png", "contact_url": "https://www.flydubai.com/en/", "partner_url": null, "help_center_url": null, "updated_at": "2025-04-30T22:48:25.329631Z", "xpay_payment_enabled": false, "xpay_b2c_payment_enabled": false, "xpay_refund_enabled": false, "automatic_refund_by_xcore": false, "allow_policy_modifications_on_xcover": false, "emails": [{"id": "0deb181b-44d6-457c-979e-c62706256f85", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-modification-flydubai", "email_version": 1, "event_name": 1, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "65856876-e1a2-406b-80ad-476fa8567da0", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-cancellation-flydubai", "email_version": 1, "event_name": 2, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "e7a41d55-8263-403d-bb0f-142874d75eea", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-confirmation-flydu<PERSON><PERSON>", "email_version": 1, "event_name": 0, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}], "attributes": {}, "signup_method_on_xcover": null, "use_standard_region": null, "allow_payout_customer": true, "subsidiary": {"id": "ZDTIY", "slug": "fly<PERSON><PERSON><PERSON>", "name": "fly<PERSON><PERSON><PERSON>", "title": "fly<PERSON><PERSON><PERSON>", "logo": "https://static.xcover.com/media/partnerlogos/2025/03/25/image_35_new_2.png", "contact_url": "https://www.flydubai.com/en/", "partner_url": null, "help_center_url": null, "updated_at": "2025-04-30T22:48:25.329631Z", "xpay_payment_enabled": false, "xpay_b2c_payment_enabled": false, "xpay_refund_enabled": false, "automatic_refund_by_xcore": false, "allow_policy_modifications_on_xcover": false, "emails": [{"id": "0deb181b-44d6-457c-979e-c62706256f85", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-modification-flydubai", "email_version": 1, "event_name": 1, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "65856876-e1a2-406b-80ad-476fa8567da0", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-cancellation-flydubai", "email_version": 1, "event_name": 2, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "e7a41d55-8263-403d-bb0f-142874d75eea", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-confirmation-flydu<PERSON><PERSON>", "email_version": 1, "event_name": 0, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}], "attributes": {}, "signup_method_on_xcover": null, "use_standard_region": null, "allow_payout_customer": true}}, "partner_metadata": {}, "customer_language": "en"}