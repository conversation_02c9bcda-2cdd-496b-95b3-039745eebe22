{"seriesNum": "299", "PNR": "5LZN92", "bookAgent": "WEB2_LIVE", "resCurrency": "EUR", "PNRPin": "82322255", "bookDate": "2025-04-26T09:50:40", "modifyDate": "2025-05-31T00:52:29", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "08a46d7414y7u7q6k4ubp0846ae1r9fdcf4e9ac42651", "securityGUID": "08a46d7414y7u7q6k4ubp0846ae1r9fdcf4e9ac42651", "lastLoadGUID": "46165101-347e-410e-a83a-c0454ce38d0f", "isAsyncPNR": false, "MasterPNR": "5LZN92", "segments": [{"segKey": "16087655:16087655:5/24/2025 1:10:00 PM", "LFID": 16087655, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16087655", "org": "PSA", "dest": "DXB", "depTime": "2025-05-24T13:10:00", "depTimeGMT": "2025-05-24T11:10:00", "arrTime": "2025-05-24T21:30:00", "operCarrier": "FZ", "operFlightNum": "1296", "mrktCarrier": "FZ ", "mrktFlightNum": "1296", "persons": [{"recNum": 2, "status": 5}, {"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181426, "depDate": "2025-05-24T13:10:00", "legKey": "16087655:181426:5/24/2025 1:10:00 PM", "customerKey": "944CF36D36CA9F107ECCBBE03491D8ABA07A746DF16ACE45422BCA55D3702C6B"}], "active": true, "changeType": "TK"}, {"segKey": "16087739:16087739:5/31/2025 7:25:00 AM", "LFID": 16087739, "depDate": "2025-05-31T00:00:00", "flightGroupId": "16087739", "org": "DXB", "dest": "PSA", "depTime": "2025-05-31T07:25:00", "depTimeGMT": "2025-05-31T03:25:00", "arrTime": "2025-05-31T12:10:00", "operCarrier": "FZ", "operFlightNum": "1295", "mrktCarrier": "FZ ", "mrktFlightNum": "1295", "persons": [{"recNum": 4, "status": 5}, {"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181478, "depDate": "2025-05-31T07:25:00", "legKey": "16087739:181478:5/31/2025 7:25:00 AM", "customerKey": "3F4ABE0BDB49A15829536FC20CEE2A6EB64F7AF46A78F4D15BC46B2D126DB6F0"}], "active": true}], "persons": [{"paxID": 267030862, "fName": "LAURA", "lName": "MASSAI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [2, 4]}, {"paxID": 267030861, "fName": "ANDREA", "lName": "GALEOTTI", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1975-01-19T00:00:00", "nationality": "380", "FFNum": "540064324", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8IT5", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 269847611, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680ca8b00007780000006b85#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-04-26T09:50:40"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8IT5", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 269847612, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680ca8b00007780000006b85#2#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-04-26T09:50:40"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/23/2025 1:55:24 PM", "provider": "<PERSON>", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TRL8IT2", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "EY6VR-9USCX-INS/0caf1211-7f5e-40f4-bfb2-d09b431bc962", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680ca8b00007780000006b85#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-26T09:50:40"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/23/2025 1:55:24 PM", "provider": "<PERSON>", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TRL8IT2", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "EY6VR-9USCX-INS/0caf1211-7f5e-40f4-bfb2-d09b431bc962", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680ca8b00007780000006b85#2#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-26T09:50:40"}]}], "payments": [{"paymentID": *********, "paxID": 267034051, "method": "MSCD", "status": "1", "paidDate": "2025-04-26T10:24:45", "cardNum": "************1549", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 1291.17, "baseCurr": "EUR", "baseAmt": 1291.17, "userID": "paybylink", "channelID": 2, "cardHolderName": "LORIGINALE SRL <PERSON>", "authCode": "644684", "reference": "22661232", "externalReference": "22661232", "tranId": "21037828", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "ITAMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21037828}, {"paymentID": 207021584, "paxID": 267032460, "method": "MSCD", "status": "2", "paidDate": "2025-04-26T09:50:44", "cardNum": "************1549", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 1291.17, "baseCurr": "EUR", "baseAmt": 1291.17, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "loriginale srl andrea gal<PERSON>tti", "reference": "22660833", "externalReference": "22660833", "tranId": "21037157", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "ITAMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21037157}, {"paymentID": *********, "paxID": 269938040, "method": "MSCD", "status": "1", "paidDate": "2025-05-23T13:55:32", "cardNum": "************6350", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 17.65, "baseCurr": "EUR", "baseAmt": 17.65, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "H55477", "reference": "23205675", "externalReference": "23205675", "tranId": "21587827", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "ITAMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21587827}], "OAFlights": null, "physicalFlights": [{"key": "16087655:181426:2025-05-24T01:10:00 PM", "LFID": 16087655, "PFID": 181426, "org": "PSA", "dest": "DXB", "depDate": "2025-05-24T13:10:00", "depTime": "2025-05-24T13:10:00", "arrTime": "2025-05-24T21:30:00", "carrier": "FZ", "flightNum": "1296", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1296", "flightStatus": "CLOSED", "originMetroGroup": "PSA", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 22800, "reaccomChangeAlert": false, "originName": "Pisa", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:59:34 AM"}, {"key": "16087739:181478:2025-05-31T07:25:00 AM", "LFID": 16087739, "PFID": 181478, "org": "DXB", "dest": "PSA", "depDate": "2025-05-31T07:25:00", "depTime": "2025-05-31T07:25:00", "arrTime": "2025-05-31T12:10:00", "carrier": "FZ", "flightNum": "1295", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1295", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "PSA", "operatingCarrier": "FZ", "flightDuration": 24300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Pisa", "isActive": false}], "chargeInfos": [{"recNum": 4, "charges": [{"chargeID": 1358984723, "codeType": "INSU", "amt": 8.57, "curr": "EUR", "originalAmt": 8.57, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T13:55:24", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358984723:*********", "paymentID": *********, "amt": 8.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-23T13:55:24"}, {"chargeID": 1321111960, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1321111955, "amt": 10.78, "curr": "EUR", "originalAmt": 10.78, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111960:*********", "paymentID": *********, "amt": 10.78, "approveCode": 0}]}, {"chargeID": 1321111961, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1321111955, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111961:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1321111956, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1321111955, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111956:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1321111959, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1321111955, "amt": 17.97, "curr": "EUR", "originalAmt": 17.97, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111959:*********", "paymentID": *********, "amt": 17.97, "approveCode": 0}]}, {"chargeID": 1321111958, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1321111955, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111958:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1321111955, "codeType": "AIR", "amt": 269.5, "curr": "EUR", "originalAmt": 269.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-26T09:50:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1321111955:*********", "paymentID": *********, "amt": 269.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111971, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181478", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181478", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111962, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1321111955, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111957, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1321111955, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111975, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181478", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1369684254, "codeType": "CKIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-31T00:52:29", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181478", "ssrCommentId": "*********"}]}, {"recNum": 2, "charges": [{"chargeID": 1321111948, "codeType": "TAX", "taxID": 13690, "taxCode": "HB", "taxChargeID": 1321111944, "amt": 6.5, "curr": "EUR", "originalAmt": 6.5, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Council City Tax (Int'l)", "comment": "Council City Tax (Int'l)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111948:*********", "paymentID": *********, "amt": 6.5, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-26T09:50:39"}, {"chargeID": 1321111947, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1321111944, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111947:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1321111950, "codeType": "TAX", "taxID": 13692, "taxCode": "VT", "taxChargeID": 1321111944, "amt": 2.25, "curr": "EUR", "originalAmt": 2.25, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111950:*********", "paymentID": *********, "amt": 2.25, "approveCode": 0}]}, {"chargeID": 1321111945, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1321111944, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111945:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1321111951, "codeType": "TAX", "taxID": 13693, "taxCode": "EX", "taxChargeID": 1321111944, "amt": 0.73, "curr": "EUR", "originalAmt": 0.73, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Security Bag Charge (International)", "comment": "Security Bag Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111951:*********", "paymentID": *********, "amt": 0.73, "approveCode": 0}]}, {"chargeID": 1321111952, "codeType": "TAX", "taxID": 13695, "taxCode": "IT", "taxChargeID": 1321111944, "amt": 6.86, "curr": "EUR", "originalAmt": 6.86, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Embarkation Tax (International)", "comment": "Embarkation Tax (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111952:*********", "paymentID": *********, "amt": 6.86, "approveCode": 0}]}, {"chargeID": 1321111949, "codeType": "TAX", "taxID": 13691, "taxCode": "MJ", "taxChargeID": 1321111944, "amt": 1.09, "curr": "EUR", "originalAmt": 1.09, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111949:*********", "paymentID": *********, "amt": 1.09, "approveCode": 0}]}, {"chargeID": 1321111944, "codeType": "AIR", "amt": 157.5, "curr": "EUR", "originalAmt": 157.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-26T09:50:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1321111944:*********", "paymentID": *********, "amt": 157.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111969, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181426", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181426", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111953, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1321111944, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111946, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1321111944, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111974, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181426", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1358984722, "codeType": "INSU", "amt": 8.57, "curr": "EUR", "originalAmt": 8.57, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T13:55:24", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358984722:*********", "paymentID": *********, "amt": 8.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-23T13:55:24"}, {"chargeID": 1321111940, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1321111935, "amt": 10.78, "curr": "EUR", "originalAmt": 10.78, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111940:*********", "paymentID": *********, "amt": 10.78, "approveCode": 0}]}, {"chargeID": 1321111941, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1321111935, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111941:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1321111939, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1321111935, "amt": 17.97, "curr": "EUR", "originalAmt": 17.97, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111939:*********", "paymentID": *********, "amt": 17.97, "approveCode": 0}]}, {"chargeID": 1321111936, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1321111935, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111936:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1321111938, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1321111935, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111938:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1321111935, "codeType": "AIR", "amt": 269.5, "curr": "EUR", "originalAmt": 269.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-26T09:50:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1321111935:*********", "paymentID": *********, "amt": 269.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111967, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181478", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181478", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111942, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1321111935, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111937, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1321111935, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111973, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181478", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1369684253, "codeType": "CKIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-31T00:52:29", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181478", "ssrCommentId": "*********"}]}, {"recNum": 1, "charges": [{"chargeID": 1321111927, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1321111924, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111927:*********", "paymentID": *********, "amt": 75, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-26T09:50:39"}, {"chargeID": 1321111929, "codeType": "TAX", "taxID": 13691, "taxCode": "MJ", "taxChargeID": 1321111924, "amt": 1.09, "curr": "EUR", "originalAmt": 1.09, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111929:*********", "paymentID": *********, "amt": 1.09, "approveCode": 0}]}, {"chargeID": 1321111931, "codeType": "TAX", "taxID": 13693, "taxCode": "EX", "taxChargeID": 1321111924, "amt": 0.73, "curr": "EUR", "originalAmt": 0.73, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Security Bag Charge (International)", "comment": "Security Bag Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111931:*********", "paymentID": *********, "amt": 0.73, "approveCode": 0}]}, {"chargeID": 1321111930, "codeType": "TAX", "taxID": 13692, "taxCode": "VT", "taxChargeID": 1321111924, "amt": 2.25, "curr": "EUR", "originalAmt": 2.25, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111930:*********", "paymentID": *********, "amt": 2.25, "approveCode": 0}]}, {"chargeID": 1321111925, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1321111924, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111925:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1321111928, "codeType": "TAX", "taxID": 13690, "taxCode": "HB", "taxChargeID": 1321111924, "amt": 6.5, "curr": "EUR", "originalAmt": 6.5, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Council City Tax (Int'l)", "comment": "Council City Tax (Int'l)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111928:*********", "paymentID": *********, "amt": 6.5, "approveCode": 0}]}, {"chargeID": 1321111932, "codeType": "TAX", "taxID": 13695, "taxCode": "IT", "taxChargeID": 1321111924, "amt": 6.86, "curr": "EUR", "originalAmt": 6.86, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Embarkation Tax (International)", "comment": "Embarkation Tax (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321111932:*********", "paymentID": *********, "amt": 6.86, "approveCode": 0}]}, {"chargeID": 1321111924, "codeType": "AIR", "amt": 157.5, "curr": "EUR", "originalAmt": 157.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-26T09:50:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1321111924:*********", "paymentID": *********, "amt": 157.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321158123, "codeType": "PMNT", "amt": 37.61, "curr": "EUR", "originalAmt": 37.61, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-26T10:24:50", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321158123:*********", "paymentID": *********, "amt": 37.61, "approveCode": 0}]}, {"chargeID": 1358992231, "codeType": "PMNT", "amt": 0.51, "curr": "EUR", "originalAmt": 0.51, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-23T13:55:37", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358992231:*********", "paymentID": *********, "amt": 0.51, "approveCode": 0}]}, {"chargeID": 1321111965, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181426", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181426", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111933, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1321111924, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111926, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1321111924, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321111972, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-26T09:50:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181426", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}