const fs = require('fs');
const path = require('path');
const moment = require('moment');

/**
 * Generates an HTML report of the insurance analysis
 * @param {string} pnrNumber - PNR number
 * @param {Array} insuranceRecords - Array of insurance records
 * @param {moment} policyStartDate - Policy start date as a moment object
 * @param {moment} policyEndDate - Policy end date as a moment object
 * @param {string} policyId - Policy ID
 */
function generateHtmlReport(pnrNumber, insuranceRecords, policyStartDate, policyEndDate, policyId) {
    const missingConfirmations = insuranceRecords.filter(record => !record.hasConfirmation);
    const recordsWithConfirmation = insuranceRecords.filter(record => record.hasConfirmation);
    const dateNow = moment().format('YYYY-MM-DD HH:mm:ss');

    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insurance Coverage Analysis for PNR ${pnrNumber}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .report-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
        }
        .summary-box {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .summary-item {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 200px;
        }
        .summary-item h3 {
            margin-top: 0;
            color: #007bff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #007bff;
            color: white;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status-ok {
            color: green;
            font-weight: bold;
        }
        .status-warning {
            color: orange;
            font-weight: bold;
        }
        .status-error {
            color: red;
            font-weight: bold;
        }
        .policy-period {
            background-color: #e9f7ef;
            padding: 10px;
            border-radius: 5px;
            border-left: 5px solid #27ae60;
            margin-bottom: 20px;
        }
        .sql-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #6c757d;
            margin-bottom: 20px;
        }
        .sql-query {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .copy-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        .copy-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="report-header">
        <h1>Insurance Coverage Analysis</h1>
        <p>PNR: <strong>${pnrNumber}</strong></p>
        <p>Report generated: ${dateNow}</p>
    </div>

    <div class="policy-period">
        <h3>Policy Information</h3>
        <p>Policy ID: <strong>${policyId}</strong></p>
        <p>Policy Period: <strong>${policyStartDate.format('YYYY-MM-DD HH:mm:ss')}</strong> to <strong>${policyEndDate.format('YYYY-MM-DD HH:mm:ss')}</strong></p>
    </div>

    <div class="summary-box">
        <div class="summary-item">
            <h3>Total Records</h3>
            <p style="font-size: 24px;">${insuranceRecords.length}</p>
        </div>
        <div class="summary-item">
            <h3>With Confirmation</h3>
            <p style="font-size: 24px;">${recordsWithConfirmation.length}</p>
        </div>
        <div class="summary-item">
            <h3>Missing Confirmation</h3>
            <p style="font-size: 24px; color: ${missingConfirmations.length > 0 ? 'red' : 'green'};">${missingConfirmations.length}</p>
        </div>
        <div class="summary-item">
            <h3>Within Policy Period</h3>
            <p style="font-size: 24px;">${insuranceRecords.filter(r => r.withinPolicyPeriod === true).length}</p>
        </div>
    </div>

    <h2>Records Missing Confirmation</h2>
    ${missingConfirmations.length === 0 ?
        '<p>No records are missing confirmation numbers.</p>' :
        `<table>
            <thead>
                <tr>
                    <th>RecordNumber</th>
                    <th>Passenger</th>
                    <th>Flight</th>
                    <th>Departure Date</th>
                    <th>Insurance ID</th>
                    <th>Provider</th>
                    <th>Status</th>
                    <th>Channel</th>
                    <th>Purchase Date</th>
                    <th>Within Policy</th>
                </tr>
            </thead>
            <tbody>
                ${missingConfirmations.map((item) => `
                    <tr>
                        <td>${item.recordNumber}</td>
                        <td>${item.passengerName || 'Unknown'}</td>
                        <td>${item.segmentInfo ? `${item.segmentInfo.origin} to ${item.segmentInfo.destination} (${item.segmentInfo.flightNumber})` : 'Unknown'}</td>
                        <td>${item.departureDate ? moment(item.departureDate).format('YYYY-MM-DD HH:mm') : 'Unknown'}</td>
                        <td>${item.insuTransID}</td>
                        <td>${item.provider}</td>
                        <td>${item.statusText || 'Unknown'}</td>
                        <td>${item.channelText || 'Unknown'}</td>
                        <td>${item.insuPurchaseDate || 'Unknown'}</td>
                        <td class="${item.withinPolicyPeriod === true ? 'status-ok' : item.withinPolicyPeriod === false ? 'status-error' : 'status-warning'}">${item.withinPolicyPeriod === true ? 'Yes' : item.withinPolicyPeriod === false ? 'No' : 'Unknown'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>`
    }

    ${missingConfirmations.length > 0 ? `
    <div class="sql-section">
        <h2>SQL Update Queries</h2>
        <p>Copy and execute these SQL queries to update missing insurance confirmation numbers:</p>

        ${missingConfirmations.map((item) => {
            // Extract the policy ID from the insurance transaction ID
            const policyIdFromRecord = item.insuTransID && item.insuTransID.includes('/')
                ? item.insuTransID.split('/')[0]
                : policyId;

            return `
            <div style="margin-bottom: 15px;">
                <strong>Record ${item.recordNumber} - ${item.passengerName || 'Unknown'}:</strong>
                <div class="sql-query">UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${policyIdFromRecord}' WHERE CONFIRMATION_NUM='${pnrNumber}' AND RECORD_NUM=${item.recordNumber};</div>
            </div>`;
        }).join('')}

        <div style="margin-top: 20px; padding: 10px; background-color: #fff3cd; border-left: 5px solid #ffc107; border-radius: 5px;">
            <strong>⚠️ Important Notes:</strong>
            <ul>
                <li>Review each query before execution</li>
                <li>Ensure you have proper database permissions</li>
                <li>Consider testing on a development environment first</li>
                <li>Backup your data before making changes</li>
            </ul>
        </div>
    </div>
    ` : ''}

    <h2>All Insurance Records</h2>
    <table>
        <thead>
            <tr>
                <th>RecordNumber</th>
                <th>Passenger</th>
                <th>Flight</th>
                <th>Departure</th>
                <th>Insurance ID</th>
                <th>Provider</th>
                <th>Status</th>
                <th>Channel</th>
                <th>Purchase Date</th>
                <th>Confirmation</th>
                <th>Within Policy</th>
            </tr>
        </thead>
        <tbody>
            ${insuranceRecords.map((item) => `
                <tr>
                    <td>${item.recordNumber}</td>
                    <td>${item.passengerName || 'Unknown'}</td>
                    <td>${item.segmentInfo ? `${item.segmentInfo.origin} to ${item.segmentInfo.destination} (${item.segmentInfo.flightNumber})` : 'Unknown'}</td>
                    <td>${item.departureDate ? moment(item.departureDate).format('YYYY-MM-DD HH:mm') : 'Unknown'}</td>
                    <td>${item.insuTransID}</td>
                    <td>${item.provider}</td>
                    <td>${item.statusText || 'Unknown'}</td>
                    <td>${item.channelText || 'Unknown'}</td>
                    <td>${item.insuPurchaseDate || 'Unknown'}</td>
                    <td class="${item.hasConfirmation ? 'status-ok' : 'status-error'}">${item.insuConfNum || 'Missing'}</td>
                    <td class="${item.withinPolicyPeriod === true ? 'status-ok' : item.withinPolicyPeriod === false ? 'status-error' : 'status-warning'}">${item.withinPolicyPeriod === true ? 'Yes' : item.withinPolicyPeriod === false ? 'No' : 'Unknown'}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>
</body>
</html>`;

    try {
        // Ensure data directory exists
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Create filename with both PNR and policy ID
        const filename = `insurance-report-${pnrNumber}-${policyId}.html`;
        const filePath = path.join(dataDir, filename);
        fs.writeFileSync(filePath, html);
        console.log(`\nHTML report generated: data/${filename}`);
    } catch (error) {
        console.error('Error generating HTML report:', error.message);
    }
}

module.exports = { generateHtmlReport };
