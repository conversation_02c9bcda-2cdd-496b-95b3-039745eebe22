{"seriesNum": "299", "PNR": "SKYGO5", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82751173", "bookDate": "2025-05-12T10:20:28", "modifyDate": "2025-05-19T09:22:54", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "084683u044mdt569s5g1ifp41c65b2df04baa2bca02a", "securityGUID": "084683u044mdt569s5g1ifp41c65b2df04baa2bca02a", "lastLoadGUID": "7f4a8ccb-2841-44ef-876b-eaa87267b7eb", "isAsyncPNR": false, "MasterPNR": "SKYGO5", "segments": [{"segKey": "16087392:16087392:5/20/2025 1:45:00 AM", "LFID": 16087392, "depDate": "2025-05-20T00:00:00", "flightGroupId": "16087392", "org": "DXB", "dest": "CCJ", "depTime": "2025-05-20T01:45:00", "depTimeGMT": "2025-05-19T21:45:00", "arrTime": "2025-05-20T07:15:00", "operCarrier": "FZ", "operFlightNum": "429", "mrktCarrier": "FZ", "mrktFlightNum": "429", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181143, "depDate": "2025-05-20T01:45:00", "legKey": "16087392:181143:5/20/2025 1:45:00 AM", "customerKey": "C8DF3DF26F758B1AE126FB82BE50DBA3D1F6226A54C028639B2236A8CAB97419"}], "active": true, "changeType": "TK"}, {"segKey": "16087392:16087392:5/24/2025 1:45:00 AM", "LFID": 16087392, "depDate": "2025-05-24T00:00:00", "flightGroupId": "16087392", "org": "DXB", "dest": "CCJ", "depTime": "2025-05-24T01:45:00", "depTimeGMT": "2025-05-23T21:45:00", "arrTime": "2025-05-24T07:15:00", "operCarrier": "FZ", "operFlightNum": "429", "mrktCarrier": "FZ", "mrktFlightNum": "429", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181143, "depDate": "2025-05-24T01:45:00", "legKey": "16087392:181143:5/24/2025 1:45:00 AM", "customerKey": "6818839A35A1D00271633FB0FA0CF7869CB71D64A80CED93B9181CA0EDBDD3B2"}], "active": true, "changeType": "TK"}, {"segKey": "16087392:16087392:5/18/2025 1:45:00 AM", "LFID": 16087392, "depDate": "2025-05-18T00:00:00", "flightGroupId": "16087392", "org": "DXB", "dest": "CCJ", "depTime": "2025-05-18T01:45:00", "depTimeGMT": "2025-05-17T21:45:00", "arrTime": "2025-05-18T07:15:00", "operCarrier": "FZ", "operFlightNum": "429", "mrktCarrier": "FZ ", "mrktFlightNum": "429", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181143, "depDate": "2025-05-18T01:45:00", "legKey": "16087392:181143:5/18/2025 1:45:00 AM", "customerKey": "6FD42E145EA20CA8EF66A55D9AF5D082D903F67BB058F834CDD193571A6E50F6"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268688799, "fName": "FYISAL", "lName": "EDAVALATH MEETHAL", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1983-05-20T00:00:00", "nationality": "784", "FFNum": "787194262", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "N", "insuPurchasedate": "5/12/2025 10:20:28 AM", "provider": "<PERSON>", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NOMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "ZQ6AY-636DK-INS", "insuTransID": "ZQ6AY-636DK-INS/c726515e-40dd-42a8-a4e6-dcbcc8350cdb", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821ca00000777000000127f#1#1#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-12T10:20:28"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ellen.titto", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/12/2025 10:20:28 AM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "ZQ6AY-636DK-INS", "insuTransID": "ZQ6AY-636DK-INS/c726515e-40dd-42a8-a4e6-dcbcc8350cdb", "toRecNum": 3, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6826f2fa0007770000003840#268688799#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-16T08:12:20"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "saood.hossain", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "5/12/2025 10:20:28 AM", "provider": "<PERSON>", "status": 5, "fareClass": "M", "operFareClass": "M", "FBC": "MOMX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ZQ6AY-636DK-INS/c726515e-40dd-42a8-a4e6-dcbcc8350cdb", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682af7120007770000001c46#268688799#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-19T09:20:17"}]}], "payments": [{"paymentID": *********, "paxID": 268688928, "method": "MSCD", "status": "1", "paidDate": "2025-05-12T10:21:08", "cardNum": "************0583", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1344.87, "baseCurr": "AED", "baseAmt": 1344.87, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "FYISAL EDAVALATH", "authCode": "562469", "reference": "22988102", "externalReference": "22988102", "tranId": "21357118", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21357118}, {"paymentID": *********, "paxID": 269439164, "method": "MSCD", "status": "1", "paidDate": "2025-05-19T09:22:48", "cardNum": "************0583", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 381.1, "baseCurr": "AED", "baseAmt": 381.1, "userID": "paybylink", "channelID": 2, "cardHolderName": "FYISAL MEETHAL", "authCode": "562491", "reference": "23122609", "externalReference": "23122609", "tranId": "21495600", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21495600}, {"paymentID": *********, "paxID": 269161816, "method": "MSCD", "status": "1", "paidDate": "2025-05-16T08:14:54", "cardNum": "************0583", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 72.1, "baseCurr": "AED", "baseAmt": 72.1, "userID": "paybylink", "channelID": 2, "cardHolderName": "FYISAL MEETHAL", "authCode": "562482", "reference": "23066525", "externalReference": "23066525", "tranId": "21441379", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21441379}], "OAFlights": null, "physicalFlights": [{"key": "16087392:181143:2025-05-18T01:45:00 AM", "LFID": 16087392, "PFID": 181143, "org": "DXB", "dest": "CCJ", "depDate": "2025-05-18T01:45:00", "depTime": "2025-05-18T01:45:00", "arrTime": "2025-05-18T07:15:00", "carrier": "FZ", "flightNum": "429", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "429", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "CCJ", "operatingCarrier": "FZ", "flightDuration": 14400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kozhikode", "isActive": false, "changeType": "TK", "flightChangeTime": "4/23/2025 10:48:03 AM"}, {"key": "16087392:181143:2025-05-20T01:45:00 AM", "LFID": 16087392, "PFID": 181143, "org": "DXB", "dest": "CCJ", "depDate": "2025-05-20T01:45:00", "depTime": "2025-05-20T01:45:00", "arrTime": "2025-05-20T07:15:00", "carrier": "FZ", "flightNum": "429", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "429", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "CCJ", "operatingCarrier": "FZ", "flightDuration": 14400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kozhikode", "isActive": false, "changeType": "TK", "flightChangeTime": "4/23/2025 10:48:04 AM"}, {"key": "16087392:181143:2025-05-24T01:45:00 AM", "LFID": 16087392, "PFID": 181143, "org": "DXB", "dest": "CCJ", "depDate": "2025-05-24T01:45:00", "depTime": "2025-05-24T01:45:00", "arrTime": "2025-05-24T07:15:00", "carrier": "FZ", "flightNum": "429", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "429", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "CCJ", "operatingCarrier": "FZ", "flightDuration": 14400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kozhikode", "isActive": false, "changeType": "TK", "flightChangeTime": "4/23/2025 10:48:04 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1342423916, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423916:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-12T10:20:28"}, {"chargeID": 1348653529, "codeType": "INSU", "taxChargeID": 1348653519, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "INSU", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423916, "paymentMap": [{"key": "1348653529:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1342423747, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342423746, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423747:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1342423911, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342423746, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423911:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1342423909, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342423746, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423909:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342423910, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342423746, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423910:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342423748, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342423746, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423748:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1348653521, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1348653519, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "Passenger Service Charge (Intl)", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423747, "paymentMap": [{"key": "1348653521:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1348653522, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1348653519, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "YQ - DUMMY", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423911, "paymentMap": [{"key": "1348653522:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1348653523, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1348653519, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "Passengers Security & Safety Service Fees", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423909, "paymentMap": [{"key": "1348653523:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348653525, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1348653519, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "Advanced passenger information fee", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423910, "paymentMap": [{"key": "1348653525:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1348653526, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1348653519, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "Passenger Facilities Charge.", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423748, "paymentMap": [{"key": "1348653526:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1342423746, "codeType": "AIR", "amt": 1050, "curr": "AED", "originalAmt": 1050, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T10:20:28", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342423746:*********", "paymentID": *********, "amt": 1050, "approveCode": 0}]}, {"chargeID": 1348653519, "codeType": "AIR", "amt": -1050, "curr": "AED", "originalAmt": -1050, "originalCurr": "AED", "status": 0, "billDate": "2025-05-16T08:12:21", "desc": "WEB:AIR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423746, "paymentMap": [{"key": "1348653519:*********", "paymentID": *********, "amt": -1050, "approveCode": 0}]}, {"chargeID": 1342431680, "codeType": "PMNT", "amt": 39.17, "curr": "AED", "originalAmt": 39.17, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T10:21:12", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342431680:*********", "paymentID": *********, "amt": 39.17, "approveCode": 0}]}, {"chargeID": 1342423917, "codeType": "NSST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "NSST", "comment": "FLXID:NSST_ZONE1_WIN_AIS::181143", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181143"}, {"chargeID": 1348653527, "codeType": "NSST", "taxChargeID": 1348653519, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "NSST", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423917, "paymentMap": [], "PFID": "181143"}, {"chargeID": 1342423913, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342423746, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1348653520, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1348653519, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:21", "desc": "Included seat", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423913, "paymentMap": []}, {"chargeID": 1342423912, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1342423746, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1348653528, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1348653519, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "40kg BAG INCLUDED IN FARE", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423912, "paymentMap": []}, {"chargeID": 1342423918, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T10:20:28", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181143"}, {"chargeID": 1348653524, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1348653519, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-16T08:12:22", "desc": "Standard meal", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342423918, "paymentMap": [], "PFID": "181143"}]}, {"recNum": 2, "charges": [{"chargeID": 1352302388, "codeType": "INSU", "taxChargeID": 1352302381, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:10:49", "billDate": "2025-05-19T09:20:18", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653541, "paymentMap": [{"key": "1352302388:*********", "paymentID": *********, "amt": -25.7, "approveCode": 0}, {"key": "1352302388:*********", "paymentID": *********, "amt": -10, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-19T09:20:18"}, {"chargeID": 1348653541, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:10:49", "billDate": "2025-05-16T08:12:23", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653541:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "1348653541:*********", "paymentID": *********, "amt": 25.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}"}, {"chargeID": 1352302383, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352302381, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:18", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653531, "paymentMap": [{"key": "1352302383:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": *********0, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352302381, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:18", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653535, "paymentMap": [{"key": "*********0:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": *********1, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352302381, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:18", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653534, "paymentMap": [{"key": "*********1:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": *********3, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352302381, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:18", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653533, "paymentMap": [{"key": "*********3:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": *********4, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352302381, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:18", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653532, "paymentMap": [{"key": "*********4:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1348653531, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1348653530, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653531:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1348653532, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1348653530, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653532:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1348653533, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1348653530, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653533:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1348653534, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1348653530, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653534:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1348653535, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1348653530, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653535:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1352302381, "codeType": "AIR", "amt": -1060, "curr": "AED", "originalAmt": -1060, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:17", "desc": "FZ 429 DXB-CCJ 24May2025 Sat 01:45 07:15\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653530, "paymentMap": [{"key": "1352302381:*********", "paymentID": *********, "amt": -1060, "approveCode": 0}]}, {"chargeID": 1348653530, "codeType": "AIR", "amt": 1060, "curr": "AED", "originalAmt": 1060, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "FZ 429 DXB-CCJ 24May2025 Sat 01:45 07:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653530:*********", "paymentID": *********, "amt": 1060, "approveCode": 0}]}, {"chargeID": 1348660388, "codeType": "PMNT", "amt": 2.1, "curr": "AED", "originalAmt": 2.1, "originalCurr": "AED", "status": 0, "billDate": "2025-05-16T08:14:58", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348660388:*********", "paymentID": *********, "amt": 2.1, "approveCode": 0}]}, {"chargeID": 1348653539, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:23", "billDate": "2025-05-16T08:12:23", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348653539:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352302385, "codeType": "NSST", "taxChargeID": 1352302381, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:10:46", "billDate": "2025-05-19T09:20:18", "desc": "Special Service Request:NSST-22F", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653540, "paymentMap": [], "PFID": "181143"}, {"chargeID": 1348653540, "codeType": "NSST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:10:46", "billDate": "2025-05-16T08:12:23", "desc": "Special Service Request:NSST-22F", "comment": "FLXID:NSST_ZONE1_WIN_AIS:\r\nADVANCED SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181143"}, {"chargeID": 1352302386, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352302381, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:23", "billDate": "2025-05-19T09:20:18", "desc": "INST: Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653538, "paymentMap": []}, {"chargeID": 1348653538, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1348653530, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:23", "billDate": "2025-05-16T08:12:23", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352302429, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1352302381, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-19T09:20:18", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653536, "paymentMap": []}, {"chargeID": 1348653536, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1348653530, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:22", "billDate": "2025-05-16T08:12:22", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********5, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1352302381, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:23", "billDate": "2025-05-19T09:20:18", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1348653537, "paymentMap": [], "PFID": "181143"}, {"chargeID": 1348653537, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1348653530, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-16T08:12:23", "billDate": "2025-05-16T08:12:23", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181143"}]}, {"recNum": 3, "charges": [{"chargeID": 1352302466, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:17:15", "billDate": "2025-05-19T09:20:19", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352302466:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-19T09:20:19"}, {"chargeID": *********7, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": *********6, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********7:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": *********8, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********6, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********8:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352302446, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": *********6, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352302446:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352302452, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********6, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352302452:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352302459, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********6, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352302459:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": *********6, "codeType": "AIR", "amt": 1370, "curr": "AED", "originalAmt": 1370, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:18", "billDate": "2025-05-19T09:20:18", "desc": "FZ 429 DXB-CCJ 20May2025 Tue 01:45 07:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 600, "tierPoints": 600, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "*********6:*********", "paymentID": *********, "amt": 1305.7, "approveCode": 0}, {"key": "*********6:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "*********6:*********", "paymentID": *********, "amt": 54.3, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352306624, "codeType": "PMNT", "amt": 11.1, "curr": "AED", "originalAmt": 11.1, "originalCurr": "AED", "status": 1, "billDate": "2025-05-19T09:22:54", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352306624:*********", "paymentID": *********, "amt": 11.1, "approveCode": 0}]}, {"chargeID": 1352302465, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352302465:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352302467, "codeType": "SPST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:19:09", "billDate": "2025-05-19T09:20:19", "desc": "Special Service Request:SPST-21F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nSPECIAL SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181143", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352302464, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": *********6, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352302460, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": *********6, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1352302462, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": *********6, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-19T09:20:19", "billDate": "2025-05-19T09:20:19", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181143", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}