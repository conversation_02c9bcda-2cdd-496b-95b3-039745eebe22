{"seriesNum": "299", "PNR": "QKCBNH", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82707615", "bookDate": "2025-05-10T15:09:28", "modifyDate": "2025-05-18T15:55:13", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "ec58w141a924ubsud063m1u44eb4b2b3cfff2daaf66e", "securityGUID": "ec58w141a924ubsud063m1u44eb4b2b3cfff2daaf66e", "lastLoadGUID": "8ea33ff5-fa70-4fdd-9928-df0193772328", "isAsyncPNR": false, "MasterPNR": "QKCBNH", "segments": [{"segKey": "16087749:16087749:5/12/2025 10:05:00 AM", "LFID": 16087749, "depDate": "2025-05-12T00:00:00", "flightGroupId": "16087749", "org": "DXB", "dest": "ZNZ", "depTime": "2025-05-12T10:05:00", "depTimeGMT": "2025-05-12T06:05:00", "arrTime": "2025-05-12T14:30:00", "operCarrier": "FZ", "operFlightNum": "1687", "mrktCarrier": "FZ ", "mrktFlightNum": "1687", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181499, "depDate": "2025-05-12T10:05:00", "legKey": "16087749:181499:5/12/2025 10:05:00 AM", "customerKey": "144A6DA7083684DD76A4399AEF19C47D74073254BF608B95E308BDAA16A86A96"}], "active": true, "changeType": "TK"}, {"segKey": "16087777:16087777:5/19/2025 9:20:00 PM", "LFID": 16087777, "depDate": "2025-05-19T00:00:00", "flightGroupId": "16087777", "org": "ZNZ", "dest": "DXB", "depTime": "2025-05-19T21:20:00", "depTimeGMT": "2025-05-19T18:20:00", "arrTime": "2025-05-20T03:45:00", "operCarrier": "FZ", "operFlightNum": "1688", "mrktCarrier": "FZ ", "mrktFlightNum": "1688", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181515, "depDate": "2025-05-19T21:20:00", "legKey": "16087777:181515:5/19/2025 9:20:00 PM", "customerKey": "2BD5793CCDFFE1E3B51F70A17A62F3FEEAE41260AEF6AC011FBCB398ECBF5378"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 268522559, "fName": "FAKHERA", "lName": "ALMANSOORI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/10/2025 3:09:29 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "emergencyContactID": 268524587, "discloseEmergencyContact": 1, "insuTransID": "496NP-37Z6U-INS/f6d34f68-b60e-4fbf-9fd9-0d7826599caa", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f6a5900077800000093da#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-10T15:09:29"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/10/2025 3:09:29 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "496NP-37Z6U-INS/f6d34f68-b60e-4fbf-9fd9-0d7826599caa", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f6a5900077800000093da#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-10T15:09:29"}]}], "payments": [{"paymentID": *********, "paxID": 269376672, "method": "IPAY", "status": "1", "paidDate": "2025-05-18T15:55:08", "cardNum": "************2575", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 182.31, "baseCurr": "AED", "baseAmt": 182.31, "userID": "olci", "channelID": 20, "cardHolderName": "Fak<PERSON>", "authCode": "130854", "reference": "23110113", "externalReference": "23110113", "tranId": "21484149", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21484149}, {"paymentID": 208560607, "paxID": 268522799, "method": "MSCD", "status": "2", "paidDate": "2025-05-10T15:10:09", "cardNum": "************5237", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1646.09, "baseCurr": "AED", "baseAmt": 1646.09, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "reference": "22955559", "externalReference": "22955559", "tranId": "21325553", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21325553}, {"paymentID": *********, "paxID": 268522801, "method": "VISA", "status": "1", "paidDate": "2025-05-10T15:11:55", "cardNum": "************3780", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1646.09, "baseCurr": "AED", "baseAmt": 1646.09, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "authCode": "906526", "reference": "22955597", "externalReference": "22955597", "tranId": "21325553", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21325553}], "OAFlights": null, "physicalFlights": [{"key": "16087749:181499:2025-05-12T10:05:00 AM", "LFID": 16087749, "PFID": 181499, "org": "DXB", "dest": "ZNZ", "depDate": "2025-05-12T10:05:00", "depTime": "2025-05-12T10:05:00", "arrTime": "2025-05-12T14:30:00", "carrier": "FZ", "flightNum": "1687", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1687", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ZNZ", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Zanzibar", "isActive": false, "changeType": "TK", "flightChangeTime": "3/13/2025 11:31:04 AM"}, {"key": "16087777:181515:2025-05-19T09:20:00 PM", "LFID": 16087777, "PFID": 181515, "org": "ZNZ", "dest": "DXB", "depDate": "2025-05-19T21:20:00", "depTime": "2025-05-19T21:20:00", "arrTime": "2025-05-20T03:45:00", "carrier": "FZ", "flightNum": "1688", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1688", "flightStatus": "CLOSED", "originMetroGroup": "ZNZ", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Zanzibar", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/2/2025 7:00:26 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1340245236, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245236:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-10T15:09:28"}, {"chargeID": 1340245204, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340245199, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245204:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1340245202, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340245199, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245202:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1340245205, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1340245199, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245205:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340245203, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1340245199, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245203:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1340245200, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340245199, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245200:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340245199, "codeType": "AIR", "amt": 230, "curr": "AED", "originalAmt": 230, "originalCurr": "AED", "status": 1, "billDate": "2025-05-10T15:09:29", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245199:*********", "paymentID": *********, "amt": 230, "approveCode": 0}]}, {"chargeID": 1340251178, "codeType": "PMNT", "amt": 47.94, "curr": "AED", "originalAmt": 47.94, "originalCurr": "AED", "status": 1, "billDate": "2025-05-10T15:12:03", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340251178:*********", "paymentID": *********, "amt": 47.94, "approveCode": 0}]}, {"chargeID": 1340245237, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181499", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245237:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "181499"}, {"chargeID": 1340245201, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1340245199, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340245240, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181499"}]}, {"recNum": 2, "charges": [{"chargeID": 1340245239, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245239:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-10T15:09:28"}, {"chargeID": 1340245230, "codeType": "TAX", "taxID": 7524, "taxCode": "HY", "taxChargeID": 1340245207, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Aviation Safety Fee", "comment": "Aviation Safety Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245230:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1340245231, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340245207, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245231:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1340245208, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340245207, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245208:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340245233, "codeType": "TAX", "taxID": 12169, "taxCode": "P9", "taxChargeID": 1340245207, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Airport Security Fee (International)", "comment": "Airport Security Fee (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245233:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1340245232, "codeType": "TAX", "taxID": 7544, "taxCode": "NN", "taxChargeID": 1340245207, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245232:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1340245207, "codeType": "AIR", "amt": 230, "curr": "AED", "originalAmt": 230, "originalCurr": "AED", "status": 1, "billDate": "2025-05-10T15:09:29", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340245207:*********", "paymentID": *********, "amt": 230, "approveCode": 0}]}, {"chargeID": 1351497661, "codeType": "PMNT", "amt": 5.31, "curr": "AED", "originalAmt": 5.31, "originalCurr": "AED", "status": 1, "billDate": "2025-05-18T15:55:12", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351497661:*********", "paymentID": *********, "amt": 5.31, "approveCode": 0}]}, {"chargeID": 1351496984, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 1, "billDate": "2025-05-18T15:54:33", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351496984:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181515", "ssrCommentId": "*********"}, {"chargeID": 1340245229, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1340245207, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340245241, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-10T15:09:29", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181515"}]}], "parentPNRs": [], "childPNRs": []}