{"seriesNum": "299", "PNR": "GVV2CG", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "83301322", "bookDate": "2025-05-31T21:20:16", "modifyDate": "2025-05-31T22:24:46", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "61d16f5effb4460598dao8d48046u4p9526cy1xbgace", "securityGUID": "61d16f5effb4460598dao8d48046u4p9526cy1xbgace", "lastLoadGUID": "f6817a4e-3c75-4210-8334-03981c70fa5e", "isAsyncPNR": false, "MasterPNR": "GVV2CG", "segments": [{"segKey": "16087856:16087856:6/8/2025 3:00:00 PM", "LFID": 16087856, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16087856", "org": "DXB", "dest": "WAW", "depTime": "2025-06-08T15:00:00", "depTimeGMT": "2025-06-08T11:00:00", "arrTime": "2025-06-08T19:30:00", "operCarrier": "FZ", "operFlightNum": "1839", "mrktCarrier": "FZ", "mrktFlightNum": "1839", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181606, "depDate": "2025-06-08T15:00:00", "legKey": "16087856:181606:6/8/2025 3:00:00 PM", "customerKey": "CD6B8BD68FC1C7DCF7C07C3C32757A79028E2A0D8776D037AFBAF2D37A161BC2"}], "active": true, "changeType": "TK"}, {"segKey": "16204904:16204904:6/10/2025 9:20:00 AM", "LFID": 16204904, "depDate": "2025-06-10T00:00:00", "flightGroupId": "16204904", "org": "WAW", "dest": "DXB", "depTime": "2025-06-10T09:20:00", "depTimeGMT": "2025-06-10T07:20:00", "arrTime": "2025-06-10T17:20:00", "operCarrier": "FZ", "operFlightNum": "1836", "mrktCarrier": "FZ", "mrktFlightNum": "1836", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 182266, "depDate": "2025-06-10T09:20:00", "legKey": "16204904:182266:6/10/2025 9:20:00 AM", "customerKey": "313D757AE7D79DB823EE67A513661FCD78BD15078A85C3EF892A1B696DA4ED5E"}], "active": true, "changeType": "TK"}, {"segKey": "16087856:16087856:7/8/2025 3:00:00 PM", "LFID": 16087856, "depDate": "2025-07-08T00:00:00", "flightGroupId": "16087856", "org": "DXB", "dest": "WAW", "depTime": "2025-07-08T15:00:00", "depTimeGMT": "2025-07-08T11:00:00", "arrTime": "2025-07-08T19:30:00", "operCarrier": "FZ", "operFlightNum": "1839", "mrktCarrier": "FZ ", "mrktFlightNum": "1839", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181606, "depDate": "2025-07-08T15:00:00", "legKey": "16087856:181606:7/8/2025 3:00:00 PM", "customerKey": "A7BE219FD09DBA0780816FD28E707FA65EED9C9C2887EDB90EC88449B22DA183"}], "active": true, "changeType": "TK"}, {"segKey": "16204904:16204904:7/10/2025 9:20:00 AM", "LFID": 16204904, "depDate": "2025-07-10T00:00:00", "flightGroupId": "16204904", "org": "WAW", "dest": "DXB", "depTime": "2025-07-10T09:20:00", "depTimeGMT": "2025-07-10T07:20:00", "arrTime": "2025-07-10T17:20:00", "operCarrier": "FZ", "operFlightNum": "1836", "mrktCarrier": "FZ ", "mrktFlightNum": "1836", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 182266, "depDate": "2025-07-10T09:20:00", "legKey": "16204904:182266:7/10/2025 9:20:00 AM", "customerKey": "45FDFC3DD8BEDD5352CF8837AF7DF38BE97D31583AF6D581B6082197EA98A9CE"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270816946, "fName": "ELKHAN", "lName": "KARIMOV", "title": "MR", "PTCID": 1, "gender": "M", "FFNum": "705921075", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/31/2025 9:20:16 PM", "provider": "<PERSON>", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "LGAJ6-TC8DX-INS", "insuTransID": "LGAJ6-TC8DX-INS/f1968618-081b-4aec-a995-2a4e8c95eff7", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b6f810007780000017c7f#1#1#WEB#VAYANT#CREATE", "fareTypeID": 11, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-31T21:20:16"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/31/2025 9:20:16 PM", "provider": "<PERSON>", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "LGAJ6-TC8DX-INS", "insuTransID": "LGAJ6-TC8DX-INS/f1968618-081b-4aec-a995-2a4e8c95eff7", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b6f810007780000017c7f#1#2#WEB#VAYANT#CREATE", "fareTypeID": 11, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-31T21:20:16"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "kenan.a", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/31/2025 9:20:16 PM", "provider": "<PERSON>", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "LGAJ6-TC8DX-INS/f1968618-081b-4aec-a995-2a4e8c95eff7", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b7f8500077700000187b5#270816946#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-31T22:19:46"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "kenan.a", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/31/2025 9:20:16 PM", "provider": "<PERSON>", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NRL8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "LGAJ6-TC8DX-INS/f1968618-081b-4aec-a995-2a4e8c95eff7", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b7f8500077700000187b5#270816946#2#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-31T22:19:46"}]}], "payments": [{"paymentID": *********, "paxID": 270816983, "method": "VISA", "status": "1", "paidDate": "2025-05-31T21:20:59", "cardNum": "************1602", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2678.72, "baseCurr": "AED", "baseAmt": 2678.72, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "872666", "reference": "23372166", "externalReference": "23372166", "tranId": "21754542", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21754542}, {"paymentID": *********, "paxID": 270819027, "method": "VISA", "status": "1", "paidDate": "2025-05-31T22:24:44", "cardNum": "************1602", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2544.1, "baseCurr": "AED", "baseAmt": 2544.1, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "349748", "reference": "23374224", "externalReference": "23374224", "tranId": "21755018", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21755018}], "OAFlights": null, "physicalFlights": [{"key": "16087856:181606:2025-06-08T03:00:00 PM", "LFID": 16087856, "PFID": 181606, "org": "DXB", "dest": "WAW", "depDate": "2025-06-08T15:00:00", "depTime": "2025-06-08T15:00:00", "arrTime": "2025-06-08T19:30:00", "carrier": "FZ", "flightNum": "1839", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1839", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "WAW", "operatingCarrier": "FZ", "flightDuration": 23400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Warsaw", "isActive": true, "changeType": "TK", "flightChangeTime": "12/19/2024 8:44:32 PM"}, {"key": "16204904:182266:2025-06-10T09:20:00 AM", "LFID": 16204904, "PFID": 182266, "org": "WAW", "dest": "DXB", "depDate": "2025-06-10T09:20:00", "depTime": "2025-06-10T09:20:00", "arrTime": "2025-06-10T17:20:00", "carrier": "FZ", "flightNum": "1836", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1836", "flightStatus": "OPEN", "originMetroGroup": "WAW", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 21600, "reaccomChangeAlert": false, "originName": "Warsaw", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "12/19/2024 8:46:36 PM"}, {"key": "16087856:181606:2025-07-08T03:00:00 PM", "LFID": 16087856, "PFID": 181606, "org": "DXB", "dest": "WAW", "depDate": "2025-07-08T15:00:00", "depTime": "2025-07-08T15:00:00", "arrTime": "2025-07-08T19:30:00", "carrier": "FZ", "flightNum": "1839", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1839", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "WAW", "operatingCarrier": "FZ", "flightDuration": 23400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Warsaw", "isActive": true, "changeType": "TK", "flightChangeTime": "12/19/2024 8:44:32 PM"}, {"key": "16204904:182266:2025-07-10T09:20:00 AM", "LFID": 16204904, "PFID": 182266, "org": "WAW", "dest": "DXB", "depDate": "2025-07-10T09:20:00", "depTime": "2025-07-10T09:20:00", "arrTime": "2025-07-10T17:20:00", "carrier": "FZ", "flightNum": "1836", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1836", "flightStatus": "OPEN", "originMetroGroup": "WAW", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 21600, "reaccomChangeAlert": false, "originName": "Warsaw", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "12/19/2024 8:46:36 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1370872547, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872547:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-31T21:20:16"}, {"chargeID": 1370905989, "codeType": "INSU", "taxChargeID": 1370905983, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872547, "paymentMap": [{"key": "1370905989:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1370872541, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370872536, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872541:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1370872537, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1370872536, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872537:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1370872539, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1370872536, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872539:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370872538, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370872536, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872538:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1370872540, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370872536, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872540:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370905984, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370905983, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872541, "paymentMap": [{"key": "1370905984:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1370905985, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1370905983, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872537, "paymentMap": [{"key": "1370905985:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1370905986, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1370905983, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872539, "paymentMap": [{"key": "1370905986:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370905987, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370905983, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872538, "paymentMap": [{"key": "1370905987:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1370905988, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370905983, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872540, "paymentMap": [{"key": "1370905988:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370872536, "codeType": "AIR", "amt": 885, "curr": "AED", "originalAmt": 885, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T21:20:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872536:*********", "paymentID": *********, "amt": 885, "approveCode": 0}]}, {"chargeID": 1370905983, "codeType": "AIR", "amt": -885, "curr": "AED", "originalAmt": -885, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T22:19:47", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872536, "paymentMap": [{"key": "1370905983:*********", "paymentID": *********, "amt": -885, "approveCode": 0}]}, {"chargeID": 1370877408, "codeType": "PMNT", "amt": 78.02, "curr": "AED", "originalAmt": 78.02, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T21:21:01", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370877408:*********", "paymentID": *********, "amt": 78.02, "approveCode": 0}]}, {"chargeID": 1370905990, "codeType": "PNLT", "amt": 1165, "curr": "AED", "originalAmt": 1165, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "CancelNoRefund FZ 1839 DXB - WAW 08.07.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370905990:*********", "paymentID": *********, "amt": 1165, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1370872548, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872548:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-31T21:20:16"}, {"chargeID": 1370905993, "codeType": "INSU", "taxChargeID": 1370905991, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872548, "paymentMap": [{"key": "1370905993:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1370872546, "codeType": "TAX", "taxID": 12951, "taxCode": "ND", "taxChargeID": 1370872542, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872546:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1370872545, "codeType": "TAX", "taxID": 12950, "taxCode": "XW", "taxChargeID": 1370872542, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Airport Tax (International)", "comment": "Airport Tax (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872545:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1370872543, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370872542, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872543:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370872544, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370872542, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T21:20:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872544:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1370905992, "codeType": "TAX", "taxID": 12951, "taxCode": "ND", "taxChargeID": 1370905991, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Passenger Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872546, "paymentMap": [{"key": "1370905992:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1370905994, "codeType": "TAX", "taxID": 12950, "taxCode": "XW", "taxChargeID": 1370905991, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Airport Tax (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872545, "paymentMap": [{"key": "1370905994:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1370905995, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370905991, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872543, "paymentMap": [{"key": "1370905995:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370905996, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370905991, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T22:19:47", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872544, "paymentMap": [{"key": "1370905996:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1370872542, "codeType": "AIR", "amt": 885, "curr": "AED", "originalAmt": 885, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T21:20:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370872542:*********", "paymentID": *********, "amt": 885, "approveCode": 0}]}, {"chargeID": 1370905991, "codeType": "AIR", "amt": -885, "curr": "AED", "originalAmt": -885, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T22:19:47", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1370872542, "paymentMap": [{"key": "1370905991:*********", "paymentID": *********, "amt": -885, "approveCode": 0}]}, {"chargeID": 1370905997, "codeType": "PNLT", "amt": 1165, "curr": "AED", "originalAmt": 1165, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "CancelNoRefund FZ 1836 WAW - DXB 10.07.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370905997:*********", "paymentID": *********, "amt": 1165, "approveCode": 0}]}]}, {"recNum": 3, "charges": [{"chargeID": 1370906008, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:15:34", "billDate": "2025-05-31T22:19:47", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906008:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-31T22:19:47"}, {"chargeID": 1370905999, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1370905998, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370905999:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1370906000, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370905998, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906000:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1370906001, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1370905998, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906001:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370906002, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370905998, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906002:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370906003, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370905998, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906003:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1370905998, "codeType": "AIR", "amt": 895, "curr": "AED", "originalAmt": 895, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "FZ 1839 DXB-WAW 08Jun2025 Sun 15:00 19:30\r\n", "reasonID": 2, "channelID": 1, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1370905998:*********", "paymentID": *********, "amt": 270.7, "approveCode": 0}, {"key": "1370905998:*********", "paymentID": *********, "amt": 624.3, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370907571, "codeType": "PMNT", "amt": 74.1, "curr": "AED", "originalAmt": 74.1, "originalCurr": "AED", "status": 1, "billDate": "2025-05-31T22:24:46", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370907571:*********", "paymentID": *********, "amt": 74.1, "approveCode": 0}]}, {"chargeID": 1370906007, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906007:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1370906010, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:16:05", "billDate": "2025-05-31T22:19:47", "desc": "Special Service Request:FRST-9F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181606", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906006, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1370905998, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906004, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1370905998, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906005, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370905998, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181606", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1370906020, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:15:34", "billDate": "2025-05-31T22:19:47", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906020:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-31T22:19:47"}, {"chargeID": 1370906012, "codeType": "TAX", "taxID": 12950, "taxCode": "XW", "taxChargeID": 1370906011, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "XW: Airport Tax (International)", "comment": "XW: Airport Tax (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906012:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1370906013, "codeType": "TAX", "taxID": 12951, "taxCode": "ND", "taxChargeID": 1370906011, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "ND: Passenger Service Charge (International)", "comment": "ND: Passenger Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906013:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1370906014, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370906011, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906014:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370906015, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370906011, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906015:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1370906011, "codeType": "AIR", "amt": 895, "curr": "AED", "originalAmt": 895, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "FZ 1836 WAW-DXB 10Jun2025 Tue 09:20 17:20\r\n", "reasonID": 2, "channelID": 1, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1370906011:*********", "paymentID": *********, "amt": 895, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906019, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370906019:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1370906021, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:16:10", "billDate": "2025-05-31T22:19:47", "desc": "Special Service Request:FRST-8F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "182266", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906018, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1370906011, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906016, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1370906011, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1370906017, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370906011, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T22:19:47", "billDate": "2025-05-31T22:19:47", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "182266", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}