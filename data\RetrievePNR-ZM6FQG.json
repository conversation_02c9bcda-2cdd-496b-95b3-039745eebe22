{"seriesNum": "299", "PNR": "ZM6FQG", "bookAgent": "WEB_MOBILE", "resCurrency": "AED", "PNRPin": "83054147", "bookDate": "2025-05-22T16:01:29", "modifyDate": "2025-05-30T14:50:25", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "afa0ededb3t2e4rfu174y86041ba407585193a232d80", "securityGUID": "afa0ededb3t2e4rfu174y86041ba407585193a232d80", "lastLoadGUID": "c67932ed-48ff-427e-8cb1-2ead1285b7a0", "isAsyncPNR": false, "MasterPNR": "ZM6FQG", "segments": [{"segKey": "16087850:16087850:5/30/2025 8:55:00 PM", "LFID": 16087850, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087850", "org": "DXB", "dest": "TLV", "depTime": "2025-05-30T20:55:00", "depTimeGMT": "2025-05-30T16:55:00", "arrTime": "2025-05-30T23:35:00", "operCarrier": "FZ", "operFlightNum": "1807", "mrktCarrier": "FZ", "mrktFlightNum": "1807", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181580, "depDate": "2025-05-30T20:55:00", "legKey": "16087850:181580:5/30/2025 8:55:00 PM", "customerKey": "E25894D1917EEA82E91DF23050051EC53C3E5707C162A9492C375F94BDDDF8AA"}], "active": true, "changeType": "TK"}, {"segKey": "16659885:16659885:5/26/2025 12:05:00 PM", "LFID": 16659885, "depDate": "2025-05-26T00:00:00", "flightGroupId": "16659885", "org": "DXB", "dest": "TLV", "depTime": "2025-05-26T12:05:00", "depTimeGMT": "2025-05-26T08:05:00", "arrTime": "2025-05-26T14:35:00", "operCarrier": "FZ", "operFlightNum": "1081", "mrktCarrier": "FZ ", "mrktFlightNum": "1081", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 185104, "depDate": "2025-05-26T12:05:00", "legKey": "16659885:185104:5/26/2025 12:05:00 PM", "customerKey": "7A332BEFD216EE9E1DB4FE1DA294523EC58BC38D49ACB2A08CEE914375E532EC"}], "active": true}], "persons": [{"paxID": 269849118, "fName": "SAMAHER", "lName": "NSASSRA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/22/2025 4:01:29 PM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "N3UQG-EW7NH-INS", "insuTransID": "N3UQG-EW7NH-INS/af387401-aae7-4c6d-b515-ee45134f8881", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682f491a000778000000e46a#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-22T16:01:29"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "oday.ali", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/22/2025 4:01:29 PM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "N3UQG-EW7NH-INS/af387401-aae7-4c6d-b515-ee45134f8881", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6833121a000777000001b57e#269849118#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-25T12:52:02"}]}], "payments": [{"paymentID": *********, "paxID": 270105204, "method": "VISA", "status": "1", "paidDate": "2025-05-25T13:09:43", "cardNum": "************4725", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 72.1, "baseCurr": "AED", "baseAmt": 72.1, "userID": "paybylink", "channelID": 2, "cardHolderName": "AHLAM Nsassra", "authCode": "218735", "reference": "23242468", "externalReference": "23242468", "tranId": "21620694", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21620694}, {"paymentID": *********, "paxID": 266416225, "method": "VCHR", "status": "1", "paidDate": "2025-05-22T16:01:32", "voucherNum": 3251412, "gateway": "EPS", "paidCurr": "AED", "paidAmt": 550.03, "baseCurr": "AED", "baseAmt": 550.03, "userID": "WEB_MOBILE", "channelID": 12, "voucherNumFull": "PKKCYY", "authCode": "PKKCYY", "reference": "A5416854", "externalReference": "A5416854", "tranId": "21571132", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21571132}, {"paymentID": *********, "paxID": 269849292, "method": "VISA", "status": "1", "paidDate": "2025-05-22T16:03:05", "cardNum": "************1132", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 685.64, "baseCurr": "AED", "baseAmt": 685.64, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "AISHA NSASSRA", "authCode": "480517", "reference": "23188057", "externalReference": "23188057", "tranId": "21571132", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21571132}], "OAFlights": null, "physicalFlights": [{"key": "16659885:185104:2025-05-26T12:05:00 PM", "LFID": 16659885, "PFID": 185104, "org": "DXB", "dest": "TLV", "depDate": "2025-05-26T12:05:00", "depTime": "2025-05-26T12:05:00", "arrTime": "2025-05-26T14:35:00", "carrier": "FZ", "flightNum": "1081", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "1081", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false}, {"key": "16087850:181580:2025-05-30T08:55:00 PM", "LFID": 16087850, "PFID": 181580, "org": "DXB", "dest": "TLV", "depDate": "2025-05-30T20:55:00", "depTime": "2025-05-30T20:55:00", "arrTime": "2025-05-30T23:35:00", "carrier": "FZ", "flightNum": "1807", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1807", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 13200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 2:47:18 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1357813839, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813839:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-22T16:01:29"}, {"chargeID": 1361198741, "codeType": "INSU", "taxChargeID": 1361198735, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813839, "paymentMap": [{"key": "1361198741:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1357813830, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1357813829, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813830:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1357813833, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1357813829, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813833:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357813834, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1357813829, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813834:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1357813832, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1357813829, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813832:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357813831, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1357813829, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813831:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1361198736, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1361198735, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813830, "paymentMap": [{"key": "1361198736:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1361198738, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361198735, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813833, "paymentMap": [{"key": "1361198738:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1361198740, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361198735, "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813834, "paymentMap": [{"key": "1361198740:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1361198743, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1361198735, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813831, "paymentMap": [{"key": "1361198743:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1361198748, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1361198735, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813832, "paymentMap": [{"key": "1361198748:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1357813829, "codeType": "AIR", "amt": 870, "curr": "AED", "originalAmt": 870, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T16:01:29", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357813829:*********", "paymentID": *********, "amt": 665.67, "approveCode": 0}, {"key": "1357813829:*********", "paymentID": *********, "amt": 204.33, "approveCode": 0}]}, {"chargeID": 1361198735, "codeType": "AIR", "amt": -870, "curr": "AED", "originalAmt": -870, "originalCurr": "AED", "status": 0, "billDate": "2025-05-25T12:52:02", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813829, "paymentMap": [{"key": "1361198735:*********", "paymentID": *********, "amt": -204.33, "approveCode": 0}, {"key": "1361198735:*********", "paymentID": *********, "amt": -665.67, "approveCode": 0}]}, {"chargeID": 1357822298, "codeType": "PMNT", "amt": 19.97, "curr": "AED", "originalAmt": 19.97, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T16:03:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357822298:*********", "paymentID": *********, "amt": 19.97, "approveCode": 0}]}, {"chargeID": 1357813836, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1357813829, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361198737, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1361198735, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813836, "paymentMap": []}, {"chargeID": 1357813835, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1357813829, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361198739, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1361198735, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813835, "paymentMap": []}, {"chargeID": 1357813840, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T16:01:29", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185104"}, {"chargeID": 1361198742, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1361198735, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:52:03", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357813840, "paymentMap": [], "PFID": "185104"}]}, {"recNum": 2, "charges": [{"chargeID": 1361198799, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:50:37", "billDate": "2025-05-25T12:52:03", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198799:*********", "paymentID": *********, "amt": 25.7, "approveCode": 0}, {"key": "1361198799:*********", "paymentID": *********, "amt": 10, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-25T12:52:03"}, {"chargeID": 1361198778, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1361198772, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198778:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1361198782, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1361198772, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198782:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1361198783, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1361198772, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198783:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1361198784, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361198772, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198784:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1361198785, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361198772, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198785:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1361198772, "codeType": "AIR", "amt": 880, "curr": "AED", "originalAmt": 880, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "FZ 1807 DXB-TLV 30May2025 Fri 20:55 23:35\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198772:*********", "paymentID": *********, "amt": 550.03, "approveCode": 0}, {"key": "1361198772:*********", "paymentID": *********, "amt": 329.97, "approveCode": 0}]}, {"chargeID": 1361218189, "codeType": "PMNT", "amt": 2.1, "curr": "AED", "originalAmt": 2.1, "originalCurr": "AED", "status": 1, "billDate": "2025-05-25T13:09:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361218189:*********", "paymentID": *********, "amt": 2.1, "approveCode": 0}]}, {"chargeID": 1361198798, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361198798:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1361198800, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:50:47", "billDate": "2025-05-25T12:52:03", "desc": "Special Service Request:FRST-7E", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580"}, {"chargeID": 1361198797, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1361198772, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361198795, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1361198772, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361198796, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1361198772, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:52:03", "billDate": "2025-05-25T12:52:03", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580"}, {"chargeID": 1369202717, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-30T14:50:25", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580", "ssrCommentId": "*********"}]}], "parentPNRs": [], "childPNRs": []}