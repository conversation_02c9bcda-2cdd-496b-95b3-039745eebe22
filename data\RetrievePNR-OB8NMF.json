{"seriesNum": "299", "PNR": "OB8NMF", "bookAgent": "ANDROID_APP", "resCurrency": "AED", "PNRPin": "82621428", "bookDate": "2025-05-07T10:50:27", "modifyDate": "2025-05-08T05:19:56", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "3bbfff8341f17435d7cbe96bufs28861n95c454e2236", "securityGUID": "3bbfff8341f17435d7cbe96bufs28861n95c454e2236", "lastLoadGUID": "e5cdc64b-4454-4287-b9a7-e45f7852de3b", "isAsyncPNR": false, "MasterPNR": "OB8NMF", "segments": [{"segKey": "16087462:16087462:5/8/2025 12:20:00 AM", "LFID": 16087462, "depDate": "2025-05-08T00:00:00", "flightGroupId": "16087462", "org": "DXB", "dest": "ASB", "depTime": "2025-05-08T00:20:00", "depTimeGMT": "2025-05-07T20:20:00", "arrTime": "2025-05-08T03:50:00", "operCarrier": "FZ", "operFlightNum": "731", "mrktCarrier": "FZ ", "mrktFlightNum": "731", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181213, "depDate": "2025-05-08T00:20:00", "legKey": "16087462:181213:5/8/2025 12:20:00 AM", "customerKey": "2EFC4F13A99A2E2CC4B33245000C7BA753E1A070CBE81E43E953C28582C356A2"}], "active": true, "changeType": "TK"}, {"segKey": "16087483:16087483:6/1/2025 5:15:00 AM", "LFID": 16087483, "depDate": "2025-06-01T00:00:00", "flightGroupId": "16087483", "org": "ASB", "dest": "DXB", "depTime": "2025-06-01T05:15:00", "depTimeGMT": "2025-06-01T00:15:00", "arrTime": "2025-06-01T06:55:00", "operCarrier": "FZ", "operFlightNum": "732", "mrktCarrier": "FZ ", "mrktFlightNum": "732", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181264, "depDate": "2025-06-01T05:15:00", "legKey": "16087483:181264:6/1/2025 5:15:00 AM", "customerKey": "7E3D8A17457F1BE2C3481DFB18AB3BC0E362B6962462BC165FFAE76419C747EE"}], "active": true, "changeType": "TK"}, {"segKey": "16225519:16225519:5/9/2025 12:20:00 AM", "LFID": 16225519, "depDate": "2025-05-09T00:00:00", "flightGroupId": "16225519", "org": "DXB", "dest": "ASB", "depTime": "2025-05-09T00:20:00", "depTimeGMT": "2025-05-08T20:20:00", "arrTime": "2025-05-09T03:50:00", "operCarrier": "FZ", "operFlightNum": "731", "mrktCarrier": "FZ", "mrktFlightNum": "731", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 182425, "depDate": "2025-05-09T00:20:00", "legKey": "16225519:182425:5/9/2025 12:20:00 AM", "customerKey": "2F95B0C54F2B43174D3C075AFE8AAE5EDF490D0A82013011B32D3AA2EB8B94F5"}], "active": true}], "persons": [{"paxID": 268183126, "fName": "YUNUS", "lName": "ARESHOV", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1998-11-15T00:00:00", "FFNum": "768150515", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "M", "insuPurchasedate": "5/7/2025 10:50:28 AM", "provider": "<PERSON>", "status": 0, "fareClass": "M", "operFareClass": "M", "FBC": "MRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "F7RXL-2A9XM-INS", "insuTransID": "F7RXL-2A9XM-INS/9ced77ce-00a7-4a19-8826-5581f724955c", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681b3a12000778000000078c#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-07T10:50:27"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "5/7/2025 10:50:28 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "F7RXL-2A9XM-INS", "insuTransID": "F7RXL-2A9XM-INS/9ced77ce-00a7-4a19-8826-5581f724955c", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681c3ce80007780000000f18#268183126#2#ENT#VAYANT#CHANGE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-07T10:50:27"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ramis.uulu", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "5/7/2025 10:50:28 AM", "provider": "<PERSON>", "status": 5, "fareClass": "M", "operFareClass": "M", "FBC": "MRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "F7RXL-2A9XM-INS/9ced77ce-00a7-4a19-8826-5581f724955c", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681c3ce80007780000000f18#268183126#1#ENT#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-08T05:16:01"}]}], "payments": [{"paymentID": *********, "paxID": 268259797, "method": "VISA", "status": "1", "paidDate": "2025-05-08T05:19:50", "cardNum": "************9917", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1504.83, "baseCurr": "AED", "baseAmt": 1504.83, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "852456", "reference": "22907259", "externalReference": "22907259", "tranId": "21277907", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21277907}, {"paymentID": *********, "paxID": 268183370, "method": "VISA", "status": "1", "paidDate": "2025-05-07T10:51:59", "cardNum": "************9274", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 3158.08, "baseCurr": "AED", "baseAmt": 3158.08, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "652498", "reference": "22890146", "externalReference": "22890146", "tranId": "21264213", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21264213}], "OAFlights": null, "physicalFlights": [{"key": "16087462:181213:2025-05-08T12:20:00 AM", "LFID": 16087462, "PFID": 181213, "org": "DXB", "dest": "ASB", "depDate": "2025-05-08T00:20:00", "depTime": "2025-05-08T00:20:00", "arrTime": "2025-05-08T03:50:00", "carrier": "FZ", "flightNum": "731", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "731", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ASB", "operatingCarrier": "FZ", "flightDuration": 9000, "reaccomChangeAlert": true, "originName": "Dubai International Airport", "destinationName": "Ashgabat", "isActive": false, "changeType": "TK", "flightChangeTime": "5/16/2024 2:21:15 PM"}, {"key": "16225519:182425:2025-05-09T12:20:00 AM", "LFID": 16225519, "PFID": 182425, "org": "DXB", "dest": "ASB", "depDate": "2025-05-09T00:20:00", "depTime": "2025-05-09T00:20:00", "arrTime": "2025-05-09T03:50:00", "carrier": "FZ", "flightNum": "731", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "731", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ASB", "operatingCarrier": "FZ", "flightDuration": 9000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Ashgabat", "isActive": false}, {"key": "16087483:181264:2025-06-01T05:15:00 AM", "LFID": 16087483, "PFID": 181264, "org": "ASB", "dest": "DXB", "depDate": "2025-06-01T05:15:00", "depTime": "2025-06-01T05:15:00", "arrTime": "2025-06-01T06:55:00", "carrier": "FZ", "flightNum": "732", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "732", "flightStatus": "OPEN", "originMetroGroup": "ASB", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 9600, "reaccomChangeAlert": false, "originName": "Ashgabat", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 1:51:07 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1336787242, "codeType": "INSU", "taxChargeID": 1336787238, "amt": -32.55, "curr": "AED", "originalAmt": -32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:01", "desc": "INSU", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798249, "paymentMap": [{"key": "1336787242:*********", "paymentID": *********, "amt": -32.55, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"17.72\",\r\n  \"Tax\": \"0.84\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-08T05:16:01"}, {"chargeID": 1335798249, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798249:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"17.72\",\r\n  \"Tax\": \"0.84\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1336787241, "codeType": "TAX", "taxID": 12611, "taxCode": "QP", "taxChargeID": 1336787238, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:01", "desc": "Passenger safety & Airport dev. tax", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798231, "paymentMap": [{"key": "1336787241:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1336787243, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336787238, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:01", "desc": "Passenger Facilities Charge.", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798234, "paymentMap": [{"key": "1336787243:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1336787245, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336787238, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:01", "desc": "Passengers Security & Safety Service Fees", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798235, "paymentMap": [{"key": "1336787245:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1336787246, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336787238, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:02", "desc": "YQ - DUMMY", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798232, "paymentMap": [{"key": "1336787246:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1336787247, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336787238, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:02", "desc": "Advanced passenger information fee", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798230, "paymentMap": [{"key": "1336787247:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1336787249, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336787238, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:02", "desc": "Passenger Service Charge (Intl)", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798233, "paymentMap": [{"key": "1336787249:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1335798230, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1335798228, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798230:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1335798233, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1335798228, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798233:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1335798232, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1335798228, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798232:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1335798235, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1335798228, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798235:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1335798234, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1335798228, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798234:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1335798231, "codeType": "TAX", "taxID": 12611, "taxCode": "QP", "taxChargeID": 1335798228, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Passenger safety & Airport dev. tax", "comment": "Passenger safety & Airport dev. tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798231:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1336787238, "codeType": "AIR", "amt": -911, "curr": "AED", "originalAmt": -911, "originalCurr": "AED", "status": 0, "billDate": "2025-05-08T05:16:01", "desc": "WEB:AIR", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798228, "paymentMap": [{"key": "1336787238:*********", "paymentID": *********, "amt": -911, "approveCode": 0}]}, {"chargeID": 1335798228, "codeType": "AIR", "amt": 911, "curr": "AED", "originalAmt": 911, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T10:50:27", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798228:*********", "paymentID": *********, "amt": 911, "approveCode": 0}]}, {"chargeID": 1335804618, "codeType": "PMNT", "amt": 91.98, "curr": "AED", "originalAmt": 91.98, "originalCurr": "AED", "status": 0, "billDate": "2025-05-07T10:52:05", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335804618:*********", "paymentID": *********, "amt": 91.98, "approveCode": 0}]}, {"chargeID": 1336787250, "codeType": "PNLT", "amt": 1191, "curr": "AED", "originalAmt": 1191, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "CancelNoRefund FZ 731 DXB - ASB 08.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787250:*********", "paymentID": *********, "amt": 1191, "approveCode": 0}]}, {"chargeID": 1335798248, "codeType": "BUPX", "amt": 340, "curr": "AED", "originalAmt": 340, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "BUPX", "comment": "FLXID:BUPX_GLOBAL_Hub_Z3:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798248:*********", "paymentID": *********, "amt": 340, "approveCode": 0}]}, {"chargeID": 1336787248, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1336787238, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:02", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798229, "paymentMap": []}, {"chargeID": 1335798229, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1335798228, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1336787244, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1336787238, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:01", "desc": "Standard meal", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798252, "paymentMap": [], "PFID": "181213"}, {"chargeID": 1335798252, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181213"}]}, {"recNum": 2, "charges": [{"chargeID": 1336787251, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:11:13", "billDate": "2025-05-08T05:16:02", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787251:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":17.72,\"tax\":0.84,\"currency\":\"USD\",\"segPaxCount\":2}", "ChargeBookDate": "2025-05-08T05:16:02"}, {"chargeID": 1336787252, "codeType": "INSU", "amt": -32.55, "curr": "AED", "originalAmt": -32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-08T05:16:02", "desc": "INSU", "comment": "MODIFY No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1335798251, "paymentMap": [{"key": "1336787252:*********", "paymentID": *********, "amt": -32.55, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"17.72\",\r\n  \"Tax\": \"0.84\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1335798251, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798251:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"17.72\",\r\n  \"Tax\": \"0.84\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1335798244, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1335798241, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798244:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1335798246, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1335798241, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798246:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1335798245, "codeType": "TAX", "taxID": 12611, "taxCode": "QP", "taxChargeID": 1335798241, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Passenger safety & Airport dev. tax", "comment": "Passenger safety & Airport dev. tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798245:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1335798242, "codeType": "TAX", "taxID": 2145, "taxCode": "TM", "taxChargeID": 1335798241, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Passenger Airport tax", "comment": "Passenger Airport tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335798242:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1335798241, "codeType": "AIR", "amt": 855, "curr": "AED", "originalAmt": 855, "originalCurr": "AED", "status": 1, "billDate": "2025-05-07T10:50:27", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1335798241:*********", "paymentID": *********, "amt": 855, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1335798243, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1335798241, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1335798253, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T10:50:27", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181264", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1336787263, "codeType": "INSU", "amt": 32.55, "curr": "AED", "originalAmt": 32.55, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:11:14", "billDate": "2025-05-08T05:16:02", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787263:*********", "paymentID": *********, "amt": 32.55, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"fx\":0.27,\"premium\":17.72,\"tax\":0.84,\"currency\":\"USD\",\"segPaxCount\":2}", "ChargeBookDate": "2025-05-08T05:16:02"}, {"chargeID": 1336787254, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336787253, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787254:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1336787255, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336787253, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787255:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1336787256, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336787253, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787256:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336787257, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1336787253, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787257:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1336787258, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336787253, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787258:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336787259, "codeType": "TAX", "taxID": 12611, "taxCode": "QP", "taxChargeID": 1336787253, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "QP: Passenger safety & Airport dev. tax", "comment": "QP: Passenger safety & Airport dev. tax", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787259:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1336787253, "codeType": "AIR", "amt": 921, "curr": "AED", "originalAmt": 921, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "FZ 731 DXB-ASB 09May2025 Fri 00:20 03:50\r\n", "reasonID": 2, "channelID": 1, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1336787253:*********", "paymentID": *********, "amt": 665.9, "approveCode": 0}, {"key": "1336787253:*********", "paymentID": *********, "amt": 255.1, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1336789818, "codeType": "PMNT", "amt": 43.83, "curr": "AED", "originalAmt": 43.83, "originalCurr": "AED", "status": 1, "billDate": "2025-05-08T05:19:55", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336789818:*********", "paymentID": *********, "amt": 43.83, "approveCode": 0}]}, {"chargeID": 1336787262, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336787262:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1336787264, "codeType": "BUPL", "amt": 200, "curr": "AED", "originalAmt": 200, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:13:15", "billDate": "2025-05-08T05:16:02", "desc": "Special Service Request", "comment": "10 kg Baggage Upgrade", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1336787264:*********", "paymentID": *********, "amt": 200, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1336787260, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1336787253, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1336787261, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1336787253, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-08T05:16:02", "billDate": "2025-05-08T05:16:02", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "182425", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}