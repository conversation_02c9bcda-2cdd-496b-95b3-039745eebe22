{"seriesNum": "299", "PNR": "J69ESJ", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82414390", "bookDate": "2025-04-29T16:54:01", "modifyDate": "2025-06-01T17:21:31", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "2836d16e548040ffa8a0obj1ibu1uej7w96c31z4fe76", "securityGUID": "2836d16e548040ffa8a0obj1ibu1uej7w96c31z4fe76", "lastLoadGUID": "5f5c0ba9-0a18-463f-8f51-cf4ed16d7e92", "isAsyncPNR": false, "MasterPNR": "J69ESJ", "segments": [{"segKey": "16108704:16108704:6/4/2025 10:25:00 PM", "LFID": 16108704, "depDate": "2025-06-04T00:00:00", "flightGroupId": "16108704", "org": "DOH", "dest": "KBV", "depTime": "2025-06-04T22:25:00", "depTimeGMT": "2025-06-04T19:25:00", "arrTime": "2025-06-05T20:25:00", "operCarrier": "FZ", "operFlightNum": "020/1481", "mrktCarrier": "FZ ", "mrktFlightNum": "020/1481", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181016, "depDate": "2025-06-04T22:25:00", "legKey": "16108704:181016:6/4/2025 10:25:00 PM", "customerKey": "4E00E5B687A803665E4DC5C3085C5517B000B54F31BAE31B7441971F54530988"}, {"PFID": 181737, "depDate": "2025-06-05T10:30:00", "legKey": "16108704:181737:6/5/2025 10:30:00 AM", "customerKey": "F50BB9A902E90424FBE8C4D758AB5409C1ACF5F2FEEB7DC5E364F6263C4A6B17"}], "active": true, "changeType": "AC"}, {"segKey": "16107530:16107530:6/5/2025 8:50:00 PM", "LFID": 16107530, "depDate": "2025-06-05T00:00:00", "flightGroupId": "16107530", "org": "DOH", "dest": "KBV", "depTime": "2025-06-05T20:50:00", "depTimeGMT": "2025-06-05T17:50:00", "arrTime": "2025-06-06T11:35:00", "operCarrier": "FZ", "operFlightNum": "006/1463", "mrktCarrier": "FZ ", "mrktFlightNum": "006/1463", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181013, "depDate": "2025-06-05T20:50:00", "legKey": "16107530:181013:6/5/2025 8:50:00 PM", "customerKey": "64CC0ED3242E18FC2B55A68E443730F064297074234FFB90190CE23DBFDB9C1A"}, {"PFID": 181742, "depDate": "2025-06-06T01:45:00", "legKey": "16107530:181742:6/6/2025 1:45:00 AM", "customerKey": "9189861B4B41A6B9A822015A5FB41CDB2FF07B4160B6DC8908064938DE883F0E"}], "active": true, "changeType": "AC"}, {"segKey": "16122640:16122640:6/13/2025 12:40:00 PM", "LFID": 16122640, "depDate": "2025-06-13T00:00:00", "flightGroupId": "16122640", "org": "KBV", "dest": "DOH", "depTime": "2025-06-13T12:40:00", "depTimeGMT": "2025-06-13T05:40:00", "arrTime": "2025-06-13T19:50:00", "operCarrier": "FZ", "operFlightNum": "1464/005", "mrktCarrier": "FZ ", "mrktFlightNum": "1464/005", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181763, "depDate": "2025-06-13T12:40:00", "legKey": "16122640:181763:6/13/2025 12:40:00 PM", "customerKey": "5C6129C63B88C1010CD4319C197FCF8124C486F5C001FFF41A4C1DD512973068"}, {"PFID": 181000, "depDate": "2025-06-13T19:40:00", "legKey": "16122640:181000:6/13/2025 7:40:00 PM", "customerKey": "7842162BFC83E177445EA1E50EBC15E9D4F84C45A9429D5A97DA583C0A1AF7C4"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 267385758, "fName": "WARREN", "lName": "FERNANDES", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1992-08-31T00:00:00", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "W", "insuPurchasedate": "4/29/2025 4:51:34 PM", "provider": "AIG", "status": 0, "fareClass": "W", "operFareClass": "W", "FBC": "WRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "986947989", "insuTransID": "8e1fd3fa-2fda-4aab-a7cb-e00e1af04e81", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6811029c0007770000007af1#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-29T16:54:01"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL7QA5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "8FRTT-J9AQ2-INS/3c7a7cf5-87f3-4fbe-9bb3-ae04a853874d", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683c86bb0007770000023638#267385758#2#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-29T16:54:01"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "W", "status": 1, "fareClass": "W", "operFareClass": "W", "FBC": "WRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "8FRTT-J9AQ2-INS/3c7a7cf5-87f3-4fbe-9bb3-ae04a853874d", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683c86bb0007770000023638#267385758#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-06-01T17:03:13"}]}], "payments": [{"paymentID": *********, "paxID": 267385811, "method": "VISA", "status": "1", "paidDate": "2025-04-29T16:54:27", "cardNum": "************1012", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 2539.98, "baseCurr": "QAR", "baseAmt": 2539.98, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "030952", "reference": "22730822", "externalReference": "22730822", "tranId": "21108483", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21108483}, {"paymentID": *********, "paxID": 270889723, "method": "VISA", "status": "1", "paidDate": "2025-06-01T17:21:29", "cardNum": "************1012", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 199.64, "baseCurr": "QAR", "baseAmt": 199.64, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "098467", "reference": "23390598", "externalReference": "23390598", "tranId": "21769895", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21769895}], "OAFlights": null, "physicalFlights": [{"key": "16108704:181016:2025-06-04T10:25:00 PM", "LFID": 16108704, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-06-04T22:25:00", "depTime": "2025-06-04T22:25:00", "arrTime": "2025-06-05T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "4/30/2025 7:37:33 AM"}, {"key": "16108704:181737:2025-06-05T10:30:00 AM", "LFID": 16108704, "PFID": 181737, "org": "DXB", "dest": "KBV", "depDate": "2025-06-05T10:30:00", "depTime": "2025-06-05T10:30:00", "arrTime": "2025-06-05T20:25:00", "carrier": "FZ", "flightNum": "1481", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "1481", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "KBV", "operatingCarrier": "FZ", "flightDuration": 24900, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": true, "changeType": "AC", "flightChangeTime": "4/30/2025 7:37:33 AM"}, {"key": "16107530:181013:2025-06-05T08:50:00 PM", "LFID": 16107530, "PFID": 181013, "org": "DOH", "dest": "DXB", "depDate": "2025-06-05T20:50:00", "depTime": "2025-06-05T20:50:00", "arrTime": "2025-06-05T23:05:00", "carrier": "FZ", "flightNum": "006", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "006", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/28/2025 9:05:11 AM"}, {"key": "16107530:181742:2025-06-06T01:45:00 AM", "LFID": 16107530, "PFID": 181742, "org": "DXB", "dest": "KBV", "depDate": "2025-06-06T01:45:00", "depTime": "2025-06-06T01:45:00", "arrTime": "2025-06-06T11:35:00", "carrier": "FZ", "flightNum": "1463", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1463", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "KBV", "operatingCarrier": "FZ", "flightDuration": 24600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": true, "changeType": "AC", "flightChangeTime": "5/28/2025 9:05:11 AM"}, {"key": "16122640:181763:2025-06-13T12:40:00 PM", "LFID": 16122640, "PFID": 181763, "org": "KBV", "dest": "DXB", "depDate": "2025-06-13T12:40:00", "depTime": "2025-06-13T12:40:00", "arrTime": "2025-06-13T16:05:00", "carrier": "FZ", "flightNum": "1464", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1464", "flightStatus": "OPEN", "originMetroGroup": "KBV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 23100, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:25:34 PM"}, {"key": "16122640:181000:2025-06-13T07:40:00 PM", "LFID": 16122640, "PFID": 181000, "org": "DXB", "dest": "DOH", "depDate": "2025-06-13T19:40:00", "depTime": "2025-06-13T19:40:00", "arrTime": "2025-06-13T19:50:00", "carrier": "FZ", "flightNum": "005", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "005", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true, "changeType": "TK", "flightChangeTime": "3/5/2025 2:25:34 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T16:54:01", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-29T16:54:00"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325671095, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671095:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1325671090, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671090:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325671092, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671092:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1325671094, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "International Arrival and Departure Fees", "comment": "International Arrival and Departure Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671094:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325671089, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Advance Passenger Processing User Charge", "comment": "Advance Passenger Processing User Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671089:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325670908, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325670908:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1325671091, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671091:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1371870132, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:13", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325671091, "paymentMap": [{"key": "1371870132:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1371870123, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1371870123:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1371870127, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325671092, "paymentMap": [{"key": "1371870127:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1371870125, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325671095, "paymentMap": [{"key": "1371870125:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1371870129, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Advance Passenger Processing User Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325671089, "paymentMap": [{"key": "1371870129:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1371870126, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325671090, "paymentMap": [{"key": "1371870126:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1371870131, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325670908, "paymentMap": [{"key": "1371870131:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1371870124, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1371870124:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1371870128, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "International Arrival and Departure Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1325671094, "paymentMap": [{"key": "1371870128:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1135, "curr": "QAR", "originalAmt": 1135, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-29T16:54:01", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1135, "approveCode": 0}]}, {"chargeID": 1371870130, "codeType": "AIR", "amt": -1135, "curr": "QAR", "originalAmt": -1135, "originalCurr": "QAR", "status": 0, "billDate": "2025-06-01T17:03:13", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1371870130:*********", "paymentID": *********, "amt": -1135, "approveCode": 0}]}, {"chargeID": 1325674448, "codeType": "PMNT", "amt": 73.98, "curr": "QAR", "originalAmt": 73.98, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-29T16:54:31", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325674448:*********", "paymentID": *********, "amt": 73.98, "approveCode": 0}]}, {"chargeID": 1371870133, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-06-01T17:03:14", "desc": "Penalty AddedDueToModify FZ  006 DOH  - DXB  05-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870133:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1325671096, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1325671112, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181742"}, {"chargeID": 1325671113, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181013"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-29T16:54:01", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-29T16:54:00"}, {"chargeID": **********, "codeType": "INSU", "amt": 22.42, "curr": "QAR", "originalAmt": 22.42, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-06-01T17:03:14", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 22.42, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.58\",\"SegPaxCount\":\"2\"}"}, {"chargeID": **********, "codeType": "TAX", "taxID": 11652, "taxCode": "TS", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1325671100, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671100:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325671103, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "International Arrival and Departure Fees", "comment": "International Arrival and Departure Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671103:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325671099, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Advance Passenger Processing User Charge", "comment": "Advance Passenger Processing User Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671099:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1325671104, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671104:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1325671101, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671101:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1325671102, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1325671102:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 545, "curr": "QAR", "originalAmt": 545, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-29T16:54:01", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 545, "approveCode": 0}]}, {"chargeID": 1325671106, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1325671115, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181763"}, {"chargeID": 1325671114, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-29T16:54:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181000"}]}, {"recNum": 3, "charges": [{"chargeID": 1371870213, "codeType": "INSU", "amt": 22.41, "curr": "QAR", "originalAmt": 22.41, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-06-01T17:03:14", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870213:*********", "paymentID": *********, "amt": 22.41, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.58\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-06-01T17:03:14"}, {"chargeID": 1371870144, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371870135, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870144:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1371870137, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1371870135, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870137:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1371870143, "codeType": "TAX", "taxID": 9067, "taxCode": "G8", "taxChargeID": 1371870135, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "International Arrival and Departure Fees", "comment": "International Arrival and Departure Fees", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870143:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1371870139, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1371870135, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870139:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1371870140, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1371870135, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870140:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1371870141, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371870135, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870141:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1371870136, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1371870135, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870136:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1371870142, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371870135, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870142:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1371870138, "codeType": "TAX", "taxID": 11649, "taxCode": "E7", "taxChargeID": 1371870135, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Advance Passenger Processing User Charge", "comment": "Advance Passenger Processing User Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870138:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1371870135, "codeType": "AIR", "amt": 1135, "curr": "QAR", "originalAmt": 1135, "originalCurr": "QAR", "status": 1, "billDate": "2025-06-01T17:03:14", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371870135:*********", "paymentID": *********, "amt": 986, "approveCode": 0}, {"key": "1371870135:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1371894835, "codeType": "PMNT", "amt": 5.81, "curr": "QAR", "originalAmt": 5.81, "originalCurr": "QAR", "status": 1, "billDate": "2025-06-01T17:21:31", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371894835:*********", "paymentID": *********, "amt": 5.81, "approveCode": 0}]}, {"chargeID": 1371870145, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371870135, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371870153, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181737"}, {"chargeID": 1371870152, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-06-01T17:03:14", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181016"}]}], "parentPNRs": [], "childPNRs": []}