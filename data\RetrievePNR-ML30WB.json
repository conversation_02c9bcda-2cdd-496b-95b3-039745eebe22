{"seriesNum": "299", "PNR": "ML30WB", "bookAgent": "ANDROID_APP", "resCurrency": "OMR", "PNRPin": "83189976", "bookDate": "2025-05-27T23:48:30", "modifyDate": "2025-05-27T23:53:17", "resType": "MOBILE", "resBalance": 0, "timeLimitGMT": "2025-05-27T23:48:30", "timeLimitODT": "2025-05-28T03:48:30", "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "de0bueu64bzey86635s7v4ezk6ecb056532ffee59da6", "securityGUID": "de0bueu64bzey86635s7v4ezk6ecb056532ffee59da6", "lastLoadGUID": "d48fbbe7-0d28-4785-8f2a-b7f8d68dfc7f", "isAsyncPNR": false, "MasterPNR": "ML30WB", "segments": [{"segKey": "16087306:16087306:5/29/2025 1:10:00 PM", "LFID": 16087306, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16087306", "org": "MCT", "dest": "DXB", "depTime": "2025-05-29T13:10:00", "depTimeGMT": "2025-05-29T09:10:00", "arrTime": "2025-05-29T14:20:00", "operCarrier": "FZ", "operFlightNum": "042", "mrktCarrier": "FZ ", "mrktFlightNum": "042", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181057, "depDate": "2025-05-29T13:10:00", "legKey": "16087306:181057:5/29/2025 1:10:00 PM", "customerKey": "1F91682033E48669B1B72AFCCE006BA10ADC08F6790EBEAFCB137211C1CC390E"}], "active": true}], "persons": [{"paxID": 270379597, "fName": "FAISAL", "lName": "ALYAFII", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "L", "insuPurchasedate": "5/27/2025 11:48:30 PM", "provider": "<PERSON>", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LO6OM2", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "XVGDZ-P6UPB-INS/434cdaef-1bca-4a06-b412-8823cbbb8302", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68364e460007770000000a70#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T23:48:30"}]}], "payments": [{"paymentID": *********, "paxID": 270379692, "method": "MSCD", "status": "1", "paidDate": "2025-05-27T23:53:13", "cardNum": "************3161", "gateway": "EPS", "paidCurr": "OMR", "paidAmt": 43.713, "baseCurr": "OMR", "baseAmt": 43.713, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>al <PERSON><PERSON><PERSON>", "authCode": "T76281", "reference": "23291396", "externalReference": "23291396", "tranId": "21671639", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEOMR", "exchangeRate": "1", "resExternalPaymentID": 21671639}, {"paymentID": 210510361, "paxID": 270379694, "method": "MSCD", "status": "2", "paidDate": "2025-05-27T23:52:53", "cardNum": "************3161", "gateway": "EPS", "paidCurr": "OMR", "paidAmt": 43.713, "baseCurr": "OMR", "baseAmt": 43.713, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "<PERSON>aisal <PERSON><PERSON><PERSON>", "reference": "23291408", "externalReference": "23291408", "tranId": "21671639", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEOMR", "exchangeRate": "1", "resExternalPaymentID": 21671639}], "OAFlights": null, "physicalFlights": [{"key": "16087306:181057:2025-05-29T01:10:00 PM", "LFID": 16087306, "PFID": 181057, "org": "MCT", "dest": "DXB", "depDate": "2025-05-29T13:10:00", "depTime": "2025-05-29T13:10:00", "arrTime": "2025-05-29T14:20:00", "carrier": "FZ", "flightNum": "042", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "042", "flightStatus": "CLOSED", "originMetroGroup": "MCT", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Muscat", "destinationName": "Dubai International Airport", "isActive": false, "flightChangeTime": "5/30/2025 6:43:06 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1364930199, "codeType": "INSU", "amt": 3.74, "curr": "OMR", "originalAmt": 3.74, "originalCurr": "OMR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:48:30", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930199:*********", "paymentID": *********, "amt": 3.74, "approveCode": 0, "saleAmount": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.6\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.47\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-27T23:48:30"}, {"chargeID": 1364930197, "codeType": "TAX", "taxID": 13251, "taxCode": "S6", "taxChargeID": 1364930193, "amt": 2.2, "curr": "OMR", "originalAmt": 2.2, "originalCurr": "OMR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:48:30", "desc": "Infrastructure Charge", "comment": "Infrastructure Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930197:*********", "paymentID": *********, "amt": 2.2, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364930194, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364930193, "amt": 0.6, "curr": "OMR", "originalAmt": 0.6, "originalCurr": "OMR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:48:30", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930194:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364930195, "codeType": "TAX", "taxID": 13170, "taxCode": "OM", "taxChargeID": 1364930193, "amt": 10, "curr": "OMR", "originalAmt": 10, "originalCurr": "OMR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:48:30", "desc": "Airport Tax", "comment": "Airport Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930195:*********", "paymentID": *********, "amt": 10, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364930196, "codeType": "TAX", "taxID": 13171, "taxCode": "I2", "taxChargeID": 1364930193, "amt": 1, "curr": "OMR", "originalAmt": 1, "originalCurr": "OMR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:48:30", "desc": "Security Fee.", "comment": "Security Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930196:*********", "paymentID": *********, "amt": 1, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364930198, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364930193, "amt": 3.9, "curr": "OMR", "originalAmt": 3.9, "originalCurr": "OMR", "status": 1, "exchRate": 0, "billDate": "2025-05-27T23:48:30", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930198:*********", "paymentID": *********, "amt": 3.9, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364930193, "codeType": "AIR", "amt": 21, "curr": "OMR", "originalAmt": 21, "originalCurr": "OMR", "status": 1, "billDate": "2025-05-27T23:48:30", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930193:*********", "paymentID": *********, "amt": 21, "approveCode": 0, "saleAmount": 0}]}, {"chargeID": 1364932950, "codeType": "PMNT", "amt": 1.273, "curr": "OMR", "originalAmt": 1.273, "originalCurr": "OMR", "status": 1, "billDate": "2025-05-27T23:53:15", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364932950:*********", "paymentID": *********, "amt": 1.273, "approveCode": 0}]}]}], "parentPNRs": [], "childPNRs": []}