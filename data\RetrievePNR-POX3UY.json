{"seriesNum": "299", "PNR": "POX3UY", "bookAgent": "WEB_MOBILE", "resCurrency": "QAR", "PNRPin": "75443927", "bookDate": "2024-08-12T19:36:16", "modifyDate": "2025-05-21T12:25:21", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 4, "activeSegCount": 1, "webBookingID": "af8025f9ed66q4t4ib6gr0sb6bcbi6399ab25281578d", "securityGUID": "af8025f9ed66q4t4ib6gr0sb6bcbi6399ab25281578d", "lastLoadGUID": "26fe3be9-ab1a-4cc9-937a-137815a5672f", "isAsyncPNR": false, "MasterPNR": "POX3UY", "segments": [{"segKey": "16108365:16108365:7/2/2025 5:05:00 PM", "LFID": 16108365, "depDate": "2025-07-02T00:00:00", "flightGroupId": "16108365", "org": "DOH", "dest": "FRU", "depTime": "2025-07-02T17:05:00", "depTimeGMT": "2025-07-02T14:05:00", "arrTime": "2025-07-03T04:40:00", "operCarrier": "FZ", "operFlightNum": "018/1689", "mrktCarrier": "FZ ", "mrktFlightNum": "018/1689", "persons": [{"recNum": 12, "status": 0}, {"recNum": 10, "status": 0}, {"recNum": 9, "status": 0}, {"recNum": 11, "status": 0}], "legDetails": [{"PFID": 181022, "depDate": "2025-07-02T17:05:00", "legKey": "16108365:181022:7/2/2025 5:05:00 PM", "customerKey": "0B295612E719F87BF73954F095E2347F34B8C7A0C7C7B87E8052435297FCF284"}, {"PFID": 181530, "depDate": "2025-07-02T22:45:00", "legKey": "16108365:181530:7/2/2025 10:45:00 PM", "customerKey": "2CD698A18A2E3DBBE3FE0C1560A6603B9215F09438AFADE82AD57355894E0F8E"}], "active": true, "changeType": "TK"}, {"segKey": "15654881:15654881:12/1/2024 5:05:00 PM", "LFID": 15654881, "depDate": "2024-12-01T00:00:00", "flightGroupId": "15654881", "org": "DOH", "dest": "FRU", "depTime": "2024-12-01T17:05:00", "depTimeGMT": "2024-12-01T14:05:00", "arrTime": "2024-12-02T04:45:00", "operCarrier": "FZ", "operFlightNum": "018/1689", "mrktCarrier": "FZ ", "mrktFlightNum": "018/1689", "persons": [{"recNum": 7, "status": 0}, {"recNum": 6, "status": 0}, {"recNum": 8, "status": 0}, {"recNum": 5, "status": 0}], "legDetails": [{"PFID": 177759, "depDate": "2024-12-01T17:05:00", "legKey": "15654881:177759:12/1/2024 5:05:00 PM", "customerKey": "F0D388B382FD4E042EC73CAD297674DD9D917FB70313520FB716F7CEA35EADE3"}, {"PFID": 178310, "depDate": "2024-12-01T22:45:00", "legKey": "15654881:178310:12/1/2024 10:45:00 PM", "customerKey": "64B0E699918D679F43D227E4752B880C713F8F0E188D8881AD378225D1ACF08E"}], "active": true, "changeType": "TK"}, {"segKey": "15403795:15403795:8/14/2024 5:05:00 PM", "LFID": 15403795, "depDate": "2024-08-14T00:00:00", "flightGroupId": "15403795", "org": "DOH", "dest": "FRU", "depTime": "2024-08-14T17:05:00", "depTimeGMT": "2024-08-14T14:05:00", "arrTime": "2024-08-15T04:40:00", "operCarrier": "FZ", "operFlightNum": "018/1689", "mrktCarrier": "FZ ", "mrktFlightNum": "018/1689", "persons": [{"recNum": 3, "status": 0}, {"recNum": 4, "status": 0}, {"recNum": 2, "status": 0}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 173272, "depDate": "2024-08-14T17:05:00", "legKey": "15403795:173272:8/14/2024 5:05:00 PM", "customerKey": "6D797EFD24B7989B5F307CE78E2A5C2E6C0E9ADBB756E08EBB8F5DF00D39A2D9"}, {"PFID": 176501, "depDate": "2024-08-14T22:45:00", "legKey": "15403795:176501:8/14/2024 10:45:00 PM", "customerKey": "7776998E582E03A34A593EF50875E6BFAE56DBF4E8E91F9E6391164B0AC1BA6F"}], "active": true, "changeType": "AC"}, {"segKey": "16817280:16817280:6/3/2025 6:30:00 PM", "LFID": 16817280, "depDate": "2025-06-03T00:00:00", "flightGroupId": "16817280", "org": "DOH", "dest": "FRU", "depTime": "2025-06-03T18:30:00", "depTimeGMT": "2025-06-03T15:30:00", "arrTime": "2025-06-04T04:40:00", "operCarrier": "FZ", "operFlightNum": "010/1689", "mrktCarrier": "FZ ", "mrktFlightNum": "010/1689", "persons": [{"recNum": 13, "status": 1}, {"recNum": 15, "status": 1}, {"recNum": 14, "status": 1}, {"recNum": 16, "status": 1}], "legDetails": [{"PFID": 180999, "depDate": "2025-06-03T18:30:00", "legKey": "16817280:180999:6/3/2025 6:30:00 PM", "customerKey": "61CBBCDF6CB42E119FCD73744F178B726C7B07B407A1DA19E0C0E12805D303F9"}, {"PFID": 181530, "depDate": "2025-06-03T22:45:00", "legKey": "16817280:181530:6/3/2025 10:45:00 PM", "customerKey": "B589FF26F29CEEA4D6D2582CCABF0F55FD05ECCF6F713060D61D1D99C3C4B242"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": *********, "fName": "CHLOE", "lName": "WANLESS", "title": "MISS", "PTCID": 5, "gender": "F", "DOB": "2023-11-14T00:00:00", "recNum": [3, 7, 12, 13]}, {"paxID": *********, "fName": "CHRISTOPHER", "lName": "WANLESS", "title": "MSTR", "PTCID": 6, "gender": "M", "DOB": "2017-05-17T00:00:00", "recNum": [4, 6, 10, 15]}, {"paxID": *********, "fName": "GULNURA", "lName": "MYKTAR KYZY", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1992-05-06T00:00:00", "recNum": [2, 8, 9, 14]}, {"paxID": 240239192, "fName": "CATHERINE", "lName": "WANLESS", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2019-11-02T00:00:00", "recNum": [1, 5, 11, 16]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "8/12/2024 7:33:43 PM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "cff60e85-e88d-4a9e-96d0-760f947d13b1", "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66ba61d50007810000030743#3#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2024-08-12T19:36:16"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "8/12/2024 7:33:43 PM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "cff60e85-e88d-4a9e-96d0-760f947d13b1", "toRecNum": 8, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66ba61d50007810000030743#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2024-08-12T19:36:16"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "toRecNum": 7, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66ba61d50007810000030743#4#1#MOBILE#VAYANT#CREATE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2024-08-12T19:36:16"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "8/12/2024 7:33:43 PM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "cff60e85-e88d-4a9e-96d0-760f947d13b1", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66ba61d50007810000030743#2#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2024-08-12T19:36:16"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "8/13/2024 11:16:55 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "196d505b-77c5-4fb6-807e-dff5095776d6", "toRecNum": 11, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66bb409200076e0000001d24#240239192#1#WEB#Internal#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-08-13T11:20:46"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "8/13/2024 11:16:55 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "196d505b-77c5-4fb6-807e-dff5095776d6", "toRecNum": 10, "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66bb409200076e0000001d24#*********#1#WEB#Internal#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-08-13T11:20:46"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "toRecNum": 12, "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66bb409200076e0000001d24#*********#1#WEB#Internal#CHANGE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-08-13T11:20:46"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "8/13/2024 11:16:55 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "*********", "insuTransID": "196d505b-77c5-4fb6-807e-dff5095776d6", "toRecNum": 9, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "66bb409200076e0000001d24#*********#1#WEB#Internal#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-08-13T11:20:46"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": 1, "markFareClass": "B", "insuPurchasedate": "11/28/2024 5:25:02 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 2, "insuConfNum": "980308139", "insuTransID": "27d3c0de-b1bc-424c-b3cb-e8bb854f7818", "toRecNum": 14, "fromRecNum": 8, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6747fe96000777000000827a#*********#1#WEB#Internal#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-11-28T05:30:35"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": 1, "markFareClass": "B", "insuPurchasedate": "11/28/2024 5:25:02 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 2, "insuConfNum": "980308139", "insuTransID": "27d3c0de-b1bc-424c-b3cb-e8bb854f7818", "toRecNum": 15, "fromRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6747fe96000777000000827a#*********#1#WEB#Internal#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-11-28T05:30:35"}]}, {"recNum": 11, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": 1, "markFareClass": "B", "insuPurchasedate": "11/28/2024 5:25:02 AM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 2, "insuConfNum": "980308139", "insuTransID": "27d3c0de-b1bc-424c-b3cb-e8bb854f7818", "toRecNum": 16, "fromRecNum": 5, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6747fe96000777000000827a#240239192#1#WEB#Internal#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-11-28T05:30:35"}]}, {"recNum": 12, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": 1, "markFareClass": "B", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 2, "toRecNum": 13, "fromRecNum": 7, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6747fe96000777000000827a#*********#1#WEB#Internal#CHANGE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2024-11-28T05:30:35"}]}, {"recNum": 13, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "fromRecNum": 12, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682db8190007770000006df1#*********#1#WEB#OneSearch#CHANGE", "travelsWithPaxID": "*********", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-21T11:31:49"}]}, {"recNum": 14, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WDDU7-LVN4K-INS/6646510b-2920-4333-b7d2-2e24096d6232", "fromRecNum": 9, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682db8190007770000006df1#*********#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-21T11:31:49"}]}, {"recNum": 15, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WDDU7-LVN4K-INS/6646510b-2920-4333-b7d2-2e24096d6232", "fromRecNum": 10, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682db8190007770000006df1#*********#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-21T11:31:49"}]}, {"recNum": 16, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WDDU7-LVN4K-INS/6646510b-2920-4333-b7d2-2e24096d6232", "fromRecNum": 11, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682db8190007770000006df1#240239192#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-21T11:31:49"}]}], "payments": [{"paymentID": *********, "paxID": 251614304, "method": "VISA", "status": "1", "paidDate": "2024-11-28T05:31:36", "cardNum": "************8866", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1501.74, "baseCurr": "QAR", "baseAmt": 1501.74, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "073089", "reference": "19765542", "externalReference": "19765542", "tranId": "18169836", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 18169836}, {"paymentID": *********, "paxID": 240302687, "method": "VISA", "status": "1", "paidDate": "2024-08-13T11:21:40", "cardNum": "************8866", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 482.04, "baseCurr": "QAR", "baseAmt": 482.04, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "012801", "reference": "17875538", "externalReference": "17875538", "tranId": "16186181", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 16186181}, {"paymentID": *********, "paxID": 240239241, "method": "VISA", "status": "1", "paidDate": "2024-08-12T19:37:04", "cardNum": "************8866", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 3490.67, "baseCurr": "QAR", "baseAmt": 3490.67, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "Andrew WANLESS", "authCode": "009209", "reference": "17865076", "externalReference": "17865076", "tranId": "16174555", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 16174555}, {"paymentID": *********, "paxID": 269702372, "method": "VISA", "status": "1", "paidDate": "2025-05-21T12:25:10", "cardNum": "************8866", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1743.9, "baseCurr": "QAR", "baseAmt": 1743.9, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "090274", "reference": "23164550", "externalReference": "23164550", "tranId": "21543988", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21543988}], "OAFlights": null, "physicalFlights": [{"key": "15403795:173272:2024-08-14T05:05:00 PM", "LFID": 15403795, "PFID": 173272, "org": "DOH", "dest": "DXB", "depDate": "2024-08-14T17:05:00", "depTime": "2024-08-14T17:05:00", "arrTime": "2024-08-14T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "5/30/2024 8:37:34 AM"}, {"key": "15403795:176501:2024-08-14T10:45:00 PM", "LFID": 15403795, "PFID": 176501, "org": "DXB", "dest": "FRU", "depDate": "2024-08-14T22:45:00", "depTime": "2024-08-14T22:45:00", "arrTime": "2024-08-15T04:40:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": false, "changeType": "AC", "flightChangeTime": "5/30/2024 8:37:34 AM"}, {"key": "15654881:177759:2024-12-01T05:05:00 PM", "LFID": 15654881, "PFID": 177759, "org": "DOH", "dest": "DXB", "depDate": "2024-12-01T17:05:00", "depTime": "2024-12-01T17:05:00", "arrTime": "2024-12-01T19:15:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "7/1/2024 1:22:41 PM"}, {"key": "15654881:178310:2024-12-01T10:45:00 PM", "LFID": 15654881, "PFID": 178310, "org": "DXB", "dest": "FRU", "depDate": "2024-12-01T22:45:00", "depTime": "2024-12-01T22:45:00", "arrTime": "2024-12-02T04:45:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": false, "changeType": "TK", "flightChangeTime": "7/1/2024 1:22:41 PM"}, {"key": "16817280:180999:2025-06-03T06:30:00 PM", "LFID": 16817280, "PFID": 180999, "org": "DOH", "dest": "DXB", "depDate": "2025-06-03T18:30:00", "depTime": "2025-06-03T18:30:00", "arrTime": "2025-06-03T20:45:00", "carrier": "FZ", "flightNum": "010", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "010", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/5/2025 11:35:58 AM"}, {"key": "16817280:181530:2025-06-03T10:45:00 PM", "LFID": 16817280, "PFID": 181530, "org": "DXB", "dest": "FRU", "depDate": "2025-06-03T22:45:00", "depTime": "2025-06-03T22:45:00", "arrTime": "2025-06-04T04:40:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": true, "changeType": "AC", "flightChangeTime": "3/5/2025 11:35:58 AM"}, {"key": "16108365:181022:2025-07-02T05:05:00 PM", "LFID": 16108365, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-07-02T17:05:00", "depTime": "2025-07-02T17:05:00", "arrTime": "2025-07-02T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/14/2025 6:39:23 PM"}, {"key": "16108365:181530:2025-07-02T10:45:00 PM", "LFID": 16108365, "PFID": 181530, "org": "DXB", "dest": "FRU", "depDate": "2025-07-02T22:45:00", "depTime": "2025-07-02T22:45:00", "arrTime": "2025-07-03T04:40:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": true, "changeType": "TK", "flightChangeTime": "3/14/2025 6:39:23 PM"}], "chargeInfos": [{"recNum": 3, "charges": [{"chargeID": 966256152, "codeType": "AIR", "amt": -170, "curr": "QAR", "originalAmt": -170, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:52", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "966256152:*********", "paymentID": *********, "amt": -170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2024-08-13T11:20:52"}, {"chargeID": *********, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-12T19:36:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 170, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 7, "charges": [{"chargeID": *********, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:53", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2024-08-13T11:20:53"}, {"chargeID": 1116851230, "codeType": "AIR", "amt": -170, "curr": "QAR", "originalAmt": -170, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:37", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851230:*********", "paymentID": *********, "amt": -170, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 12, "charges": [{"chargeID": 1116851293, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:39", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851293:*********", "paymentID": *********, "amt": 170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2024-11-28T05:30:39"}, {"chargeID": 1355710019, "codeType": "AIR", "amt": -170, "curr": "QAR", "originalAmt": -170, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-21T11:31:55", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851293, "paymentMap": [{"key": "1355710019:*********", "paymentID": *********, "amt": -170, "approveCode": 0}]}, {"chargeID": 1116851294, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1116851293, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 13, "charges": [{"chargeID": 1355710058, "codeType": "AIR", "amt": 170, "curr": "QAR", "originalAmt": 170, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:56", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710058:*********", "paymentID": *********, "amt": 170, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-21T11:31:56"}, {"chargeID": 1355710059, "codeType": "BAGI", "taxID": 10314, "taxCode": "BAGI", "taxChargeID": 1355710058, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "10kg baggage INCLUDED in fare", "comment": "10kg baggage INCLUDED in fare", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}]}, {"recNum": 4, "charges": [{"chargeID": *********, "codeType": "INSU", "amt": -33, "curr": "QAR", "originalAmt": -33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-13T11:20:52", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -33, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2024-08-13T11:20:52"}, {"chargeID": *********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-12T19:36:16", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": -420, "curr": "QAR", "originalAmt": -420, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:52", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -420, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": 420, "curr": "QAR", "originalAmt": 420, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-12T19:36:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 420, "approveCode": 0}]}, {"chargeID": *********, "codeType": "PNLT", "amt": 146, "curr": "QAR", "originalAmt": 146, "originalCurr": "QAR", "status": 1, "billDate": "2024-08-13T11:20:52", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  14-Aug-2024", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 146, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "176501"}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "173272"}]}, {"recNum": 6, "charges": [{"chargeID": *********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-13T11:20:53", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2024-08-13T11:20:53"}, {"chargeID": 1116850946, "codeType": "INSU", "amt": -33, "curr": "QAR", "originalAmt": -33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:37", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116850946:*********", "paymentID": *********, "amt": -33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851249, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851249:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851253, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851253:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 1116851251, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851251:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1116851252, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851252:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851246, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851246:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851250, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851250:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1116851247, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851247:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:53", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1116851248, "codeType": "AIR", "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:37", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851248:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 1116851254, "codeType": "PNLT", "amt": 146, "curr": "QAR", "originalAmt": 146, "originalCurr": "QAR", "status": 1, "billDate": "2024-11-28T05:30:37", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  01-Dec-2024", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851254:*********", "paymentID": *********, "amt": 113, "approveCode": 0}, {"key": "1116851254:*********", "paymentID": *********, "amt": 33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "177759"}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "178310"}]}, {"recNum": 10, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2024-11-28T05:30:38"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1116851269, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851269:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851266, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851266:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851268, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851268:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851267, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851267:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851271, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851271:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710039, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851267, "paymentMap": [{"key": "1355710039:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355710037, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851271, "paymentMap": [{"key": "1355710037:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710040, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710040:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1355710036, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851266, "paymentMap": [{"key": "1355710036:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355710035, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851269, "paymentMap": [{"key": "1355710035:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710032, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710032:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 1355710031, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:54", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851268, "paymentMap": [{"key": "1355710031:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 660, "curr": "QAR", "originalAmt": 660, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:38", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 261, "approveCode": 0}, {"key": "**********:*********", "paymentID": *********, "amt": 399, "approveCode": 0}]}, {"chargeID": 1355710033, "codeType": "AIR", "amt": -660, "curr": "QAR", "originalAmt": -660, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-21T11:31:54", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710033:*********", "paymentID": *********, "amt": -261, "approveCode": 0}, {"key": "1355710033:*********", "paymentID": *********, "amt": -399, "approveCode": 0}]}, {"chargeID": 1116854761, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_MID::181530", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116854761:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1116854760, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "SPST", "comment": "FLXID:SPST_ZONE3_MID::181022", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116854760:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1355710034, "codeType": "SPST", "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T11:31:54", "desc": "SPST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116854761, "paymentMap": [{"key": "1355710034:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710038, "codeType": "SPST", "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T11:31:54", "desc": "SPST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116854760, "paymentMap": [{"key": "1355710038:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1355710041, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:55", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  02-Jul-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710041:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1116851273, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1116854763, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}, {"chargeID": 1116854762, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 15, "charges": [{"chargeID": 1355710562, "codeType": "INSU", "amt": 35.37, "curr": "QAR", "originalAmt": 35.37, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710562:*********", "paymentID": *********, "amt": 35.37, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"29.16\",\"Tax\":\"1.39\",\"SegPaxCount\":\"3\"}", "ChargeBookDate": "2025-05-21T11:31:56"}, {"chargeID": 1355710105, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1355710098, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710105:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1355710099, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1355710098, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710099:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355710104, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1355710098, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710104:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710103, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1355710098, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710103:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1355710100, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1355710098, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710100:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355710102, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1355710098, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710102:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710101, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1355710098, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710101:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710098, "codeType": "AIR", "amt": 920, "curr": "QAR", "originalAmt": 920, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:56", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710098:*********", "paymentID": *********, "amt": 7, "approveCode": 0}, {"key": "1355710098:*********", "paymentID": *********, "amt": 913, "approveCode": 0}]}, {"chargeID": 1355710564, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_MID::181530", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710564:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710563, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_MID::180999", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710563:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "180999"}, {"chargeID": 1355715172, "codeType": "BUPX", "amt": 120, "curr": "QAR", "originalAmt": 120, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "BUPX", "comment": "FLXID:GCC-AE DOH-ALA/FRU/DYU/IST/GYD/EVN/TBS/VKO:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355715172:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1355710106, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1355710098, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355710565, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "CHML", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}, {"chargeID": 1355710566, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "CHML", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180999"}]}, {"recNum": 2, "charges": [{"chargeID": 966255788, "codeType": "INSU", "amt": -33, "curr": "QAR", "originalAmt": -33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-13T11:20:51", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "966255788:*********", "paymentID": *********, "amt": -33, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2024-08-13T11:20:51"}, {"chargeID": *********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-12T19:36:16", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": -420, "curr": "QAR", "originalAmt": -420, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:51", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -420, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": 420, "curr": "QAR", "originalAmt": 420, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-12T19:36:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 420, "approveCode": 0}]}, {"chargeID": 965385936, "codeType": "PMNT", "amt": 101.67, "curr": "QAR", "originalAmt": 101.67, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-12T19:37:08", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "965385936:*********", "paymentID": *********, "amt": 101.67, "approveCode": 0}]}, {"chargeID": *********, "codeType": "PNLT", "amt": 146, "curr": "QAR", "originalAmt": 146, "originalCurr": "QAR", "status": 1, "billDate": "2024-08-13T11:20:52", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  14-Aug-2024", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 146, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "173272"}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "176501"}]}, {"recNum": 8, "charges": [{"chargeID": *********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-13T11:20:54", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2024-08-13T11:20:54"}, {"chargeID": 1116850945, "codeType": "INSU", "amt": -33, "curr": "QAR", "originalAmt": -33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116850945:*********", "paymentID": *********, "amt": -33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 61, "approveCode": 0}, {"key": "*********:*********", "paymentID": *********, "amt": 369, "approveCode": 0}]}, {"chargeID": 1116851042, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851042:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851040, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851040:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1116851244, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851244:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851043, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851043:*********", "paymentID": *********, "amt": -369, "approveCode": 0}, {"key": "1116851043:*********", "paymentID": *********, "amt": -61, "approveCode": 0}]}, {"chargeID": 1116851037, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851037:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1116851041, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851041:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1116851039, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851039:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:54", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1116851038, "codeType": "AIR", "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:37", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851038:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 966268130, "codeType": "PMNT", "amt": 14.04, "curr": "QAR", "originalAmt": 14.04, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:21:47", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "966268130:*********", "paymentID": *********, "amt": 14.04, "approveCode": 0}]}, {"chargeID": 1116851245, "codeType": "PNLT", "amt": 146, "curr": "QAR", "originalAmt": 146, "originalCurr": "QAR", "status": 1, "billDate": "2024-11-28T05:30:38", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  01-Dec-2024", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851245:*********", "paymentID": *********, "amt": 146, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "177759"}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:54", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "178310"}]}, {"recNum": 9, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2024-11-28T05:30:38"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 95, "approveCode": 0}, {"key": "**********:*********", "paymentID": *********, "amt": 335, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851237, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851237:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851234, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851234:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851232, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851232:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851235, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851235:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851236, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851236:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1355710025, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710025:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355710023, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851236, "paymentMap": [{"key": "1355710023:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1355710020, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851232, "paymentMap": [{"key": "1355710020:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355710028, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851234, "paymentMap": [{"key": "1355710028:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710026, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851237, "paymentMap": [{"key": "1355710026:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710024, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710024:*********", "paymentID": *********, "amt": -95, "approveCode": 0}, {"key": "1355710024:*********", "paymentID": *********, "amt": -335, "approveCode": 0}]}, {"chargeID": 1355710021, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:53", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851235, "paymentMap": [{"key": "1355710021:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 660, "curr": "QAR", "originalAmt": 660, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:38", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 353, "approveCode": 0}, {"key": "**********:*********", "paymentID": *********, "amt": 307, "approveCode": 0}]}, {"chargeID": 1355710027, "codeType": "AIR", "amt": -660, "curr": "QAR", "originalAmt": -660, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-21T11:31:53", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710027:*********", "paymentID": *********, "amt": -353, "approveCode": 0}, {"key": "1355710027:*********", "paymentID": *********, "amt": -307, "approveCode": 0}]}, {"chargeID": 1116857553, "codeType": "PMNT", "amt": 43.74, "curr": "QAR", "originalAmt": 43.74, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:31:44", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116857553:*********", "paymentID": *********, "amt": 43.74, "approveCode": 0}]}, {"chargeID": 1116854758, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "SPST", "comment": "FLXID:SPST_ZONE3_WIN_AIS::181022", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116854758:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1116854759, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:38", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181530", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116854759:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710022, "codeType": "SPST", "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T11:31:54", "desc": "SPST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116854759, "paymentMap": [{"key": "1355710022:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710029, "codeType": "SPST", "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T11:31:54", "desc": "SPST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116854758, "paymentMap": [{"key": "1355710029:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1355710030, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:54", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  02-Jul-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710030:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1116851239, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1116851299, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1116851300, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:38", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}, {"recNum": 14, "charges": [{"chargeID": 1355710557, "codeType": "INSU", "amt": 35.37, "curr": "QAR", "originalAmt": 35.37, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710557:*********", "paymentID": *********, "amt": 35.37, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"29.16\",\"Tax\":\"1.39\",\"SegPaxCount\":\"3\"}", "ChargeBookDate": "2025-05-21T11:31:56"}, {"chargeID": 1355710068, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1355710064, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710068:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710090, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1355710064, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710090:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710066, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1355710064, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710066:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355710091, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1355710064, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710091:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1355710065, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1355710064, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710065:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355710089, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1355710064, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710089:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1355710067, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1355710064, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710067:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710064, "codeType": "AIR", "amt": 920, "curr": "QAR", "originalAmt": 920, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:56", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710064:*********", "paymentID": *********, "amt": 402, "approveCode": 0}, {"key": "1355710064:*********", "paymentID": *********, "amt": 221, "approveCode": 0}, {"key": "1355710064:*********", "paymentID": *********, "amt": 297, "approveCode": 0}]}, {"chargeID": 1355829050, "codeType": "PMNT", "amt": 50.79, "curr": "QAR", "originalAmt": 50.79, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T12:25:21", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355829050:*********", "paymentID": *********, "amt": 50.79, "approveCode": 0}]}, {"chargeID": 1355710559, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181530", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710559:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710558, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::180999", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710558:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "180999"}, {"chargeID": 1355715171, "codeType": "BUPX", "amt": 120, "curr": "QAR", "originalAmt": 120, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:56", "desc": "BUPX", "comment": "FLXID:GCC-AE DOH-ALA/FRU/DYU/IST/GYD/EVN/TBS/VKO:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355715171:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1355710092, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1355710064, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355710127, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}, {"chargeID": 1355710126, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:56", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180999"}]}, {"recNum": 1, "charges": [{"chargeID": *********, "codeType": "INSU", "amt": -33, "curr": "QAR", "originalAmt": -33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-13T11:20:51", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -33, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2024-08-13T11:20:51"}, {"chargeID": *********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-12T19:36:16", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:50", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:50", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:50", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:50", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:50", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:51", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": -420, "curr": "QAR", "originalAmt": -420, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:50", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": -420, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": 420, "curr": "QAR", "originalAmt": 420, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-12T19:36:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 420, "approveCode": 0}]}, {"chargeID": *********, "codeType": "PNLT", "amt": 146, "curr": "QAR", "originalAmt": 146, "originalCurr": "QAR", "status": 1, "billDate": "2024-08-13T11:20:51", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  14-Aug-2024", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 146, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "173272"}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-12T19:36:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "176501"}]}, {"recNum": 5, "charges": [{"chargeID": *********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-08-13T11:20:53", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2024-08-13T11:20:53"}, {"chargeID": 1116850947, "codeType": "INSU", "amt": -33, "curr": "QAR", "originalAmt": -33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:37", "desc": "INSU", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116850947:*********", "paymentID": *********, "amt": -33, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851259, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": *********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:36", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851259:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 1116851260, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": *********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:36", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851260:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1116851262, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:36", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851262:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1116851256, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": *********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:36", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851256:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1116851261, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:36", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851261:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851258, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851258:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1116851257, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": *********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:37", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851257:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": *********, "codeType": "AIR", "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "billDate": "2024-08-13T11:20:52", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "*********:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1116851255, "codeType": "AIR", "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:36", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********, "paymentMap": [{"key": "1116851255:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 1116851229, "codeType": "PNLT", "amt": 146, "curr": "QAR", "originalAmt": 146, "originalCurr": "QAR", "status": 1, "billDate": "2024-11-28T05:30:37", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  01-Dec-2024", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851229:*********", "paymentID": *********, "amt": 33, "approveCode": 0}, {"key": "1116851229:*********", "paymentID": *********, "amt": 113, "approveCode": 0}]}, {"chargeID": *********, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": *********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:52", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "178310"}, {"chargeID": *********, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-08-13T11:20:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "177759"}]}, {"recNum": 11, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:39", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2024-11-28T05:30:39"}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851281, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851281:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1116851282, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851282:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851285, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851285:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1116851286, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851286:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1116851283, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116851283:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710044, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851281, "paymentMap": [{"key": "1355710044:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355710069, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851283, "paymentMap": [{"key": "1355710069:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710045, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851282, "paymentMap": [{"key": "1355710045:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710043, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710043:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355710042, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851285, "paymentMap": [{"key": "1355710042:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355710048, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -430, "curr": "QAR", "originalAmt": -430, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116851286, "paymentMap": [{"key": "1355710048:*********", "paymentID": *********, "amt": -430, "approveCode": 0}]}, {"chargeID": 1355710046, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:31:55", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710046:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 660, "curr": "QAR", "originalAmt": 660, "originalCurr": "QAR", "status": 0, "billDate": "2024-11-28T05:30:38", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 660, "approveCode": 0}]}, {"chargeID": 1355710071, "codeType": "AIR", "amt": -660, "curr": "QAR", "originalAmt": -660, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-21T11:31:55", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355710071:*********", "paymentID": *********, "amt": -660, "approveCode": 0}]}, {"chargeID": 1116854804, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:39", "desc": "SPST", "comment": "FLXID:SPST_ZONE3_WIN_AIS::181022", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116854804:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1116854805, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:39", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181530", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1116854805:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710070, "codeType": "SPST", "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T11:31:55", "desc": "SPST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116854805, "paymentMap": [{"key": "1355710070:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355710047, "codeType": "SPST", "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T11:31:55", "desc": "SPST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1116854804, "paymentMap": [{"key": "1355710047:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181022"}, {"chargeID": 1355710072, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:55", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  02-Jul-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710072:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1116851287, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2024-11-28T05:30:39", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1116854807, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:39", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}, {"chargeID": 1116854806, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2024-11-28T05:30:39", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 16, "charges": [{"chargeID": 1355710567, "codeType": "INSU", "amt": 35.37, "curr": "QAR", "originalAmt": 35.37, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:57", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710567:*********", "paymentID": *********, "amt": 35.37, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"29.16\",\"Tax\":\"1.39\",\"SegPaxCount\":\"3\"}", "ChargeBookDate": "2025-05-21T11:31:57"}, {"chargeID": 1355710118, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1355710112, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710118:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710117, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1355710112, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710117:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1355710114, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1355710112, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710114:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355710113, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1355710112, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710113:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355710119, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1355710112, "amt": 430, "curr": "QAR", "originalAmt": 430, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710119:*********", "paymentID": *********, "amt": 430, "approveCode": 0}]}, {"chargeID": 1355710116, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1355710112, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710116:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710115, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1355710112, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710115:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355710112, "codeType": "AIR", "amt": 920, "curr": "QAR", "originalAmt": 920, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:31:57", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710112:*********", "paymentID": *********, "amt": 920, "approveCode": 0}]}, {"chargeID": 1355710568, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:57", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::180999", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710568:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "180999"}, {"chargeID": 1355710609, "codeType": "SPST", "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:57", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181530", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355710609:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181530"}, {"chargeID": 1355715173, "codeType": "BUPX", "amt": 120, "curr": "QAR", "originalAmt": 120, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:57", "desc": "BUPX", "comment": "FLXID:GCC-AE DOH-ALA/FRU/DYU/IST/GYD/EVN/TBS/VKO:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355715173:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1355710120, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1355710112, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:31:57", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1355710613, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:57", "desc": "CHML", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180999"}, {"chargeID": 1355710612, "codeType": "CHML", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:31:57", "desc": "CHML", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}], "parentPNRs": [], "childPNRs": []}