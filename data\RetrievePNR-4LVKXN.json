{"seriesNum": "299", "PNR": "4LVKXN", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82305667", "bookDate": "2025-04-25T13:55:33", "modifyDate": "2025-05-21T12:00:09", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "734d369ca393zy64s6p5uey9od63zd41feb3ae2d11c6", "securityGUID": "734d369ca393zy64s6p5uey9od63zd41feb3ae2d11c6", "lastLoadGUID": "60c0eb49-3396-47d6-b473-beca0a356712", "isAsyncPNR": false, "MasterPNR": "4LVKXN", "segments": [{"segKey": "16660469:16660469:6/8/2025 10:55:00 AM", "LFID": 16660469, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16660469", "org": "DOH", "dest": "TLV", "depTime": "2025-06-08T10:55:00", "depTimeGMT": "2025-06-08T07:55:00", "arrTime": "2025-06-08T18:15:00", "operCarrier": "FZ", "operFlightNum": "004/1125", "mrktCarrier": "FZ ", "mrktFlightNum": "004/1125", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181009, "depDate": "2025-06-08T10:55:00", "legKey": "16660469:181009:6/8/2025 10:55:00 AM", "customerKey": "5B4657E9AF5FD47B6F774C3B3E5B9888E407724E05F87BE1D85C1DE669CE48E7"}, {"PFID": 185106, "depDate": "2025-06-08T15:45:00", "legKey": "16660469:185106:6/8/2025 3:45:00 PM", "customerKey": "4C4E44C5CAD0AE954793BB59052D1DB50847DAB1470FF57528DDB1021E47D865"}], "active": true, "changeType": "AC"}, {"segKey": "17083824:17083824:6/8/2025 10:25:00 PM", "LFID": 17083824, "depDate": "2025-06-08T00:00:00", "flightGroupId": "17083824", "org": "DOH", "dest": "TLV", "depTime": "2025-06-08T22:25:00", "depTimeGMT": "2025-06-08T19:25:00", "arrTime": "2025-06-09T08:35:00", "operCarrier": "FZ", "operFlightNum": "020/1073", "mrktCarrier": "FZ ", "mrktFlightNum": "020/1073", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181016, "depDate": "2025-06-08T22:25:00", "legKey": "17083824:181016:6/8/2025 10:25:00 PM", "customerKey": "E11CBD0DEBEAEF85BDD8D39194EE87D297C8DC31CA70E9FD490A93640402F10D"}, {"PFID": 187724, "depDate": "2025-06-09T06:05:00", "legKey": "17083824:187724:6/9/2025 6:05:00 AM", "customerKey": "C8B349678AD5CC4A22A6D978957A11E0A167F31301FFA4A3CED224FE72BB791D"}], "active": true}, {"segKey": "16158598:16158598:6/16/2025 12:50:00 AM", "LFID": 16158598, "depDate": "2025-06-16T00:00:00", "flightGroupId": "16158598", "org": "TLV", "dest": "DOH", "depTime": "2025-06-16T00:50:00", "depTimeGMT": "2025-06-15T21:50:00", "arrTime": "2025-06-16T08:55:00", "operCarrier": "FZ", "operFlightNum": "1808/001", "mrktCarrier": "FZ ", "mrktFlightNum": "1808/001", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181630, "depDate": "2025-06-16T00:50:00", "legKey": "16158598:181630:6/16/2025 12:50:00 AM", "customerKey": "B7D44C75BF018ED5288600CD8987EDF83BEFD79DF6E233277D1C37542292990A"}, {"PFID": 181010, "depDate": "2025-06-16T08:45:00", "legKey": "16158598:181010:6/16/2025 8:45:00 AM", "customerKey": "D441E0FB5A338BDE96BAD72013F2A600E444B9C70990A4AB8959908AFD051C89"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 266961907, "fName": "CONCHITA", "lName": "TOLARBA", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1980-01-14T00:00:00", "FFNum": "786675061", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "4/25/2025 1:51:02 PM", "provider": "AIG", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "986786683", "insuTransID": "3555c987-2638-49d3-822c-adc9f9c720aa", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680b8e480007770000003dbd#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-25T13:55:33"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "T", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WWCMV-7MD29-INS/3573e71a-17bb-4ab5-b503-8c04945dc12d", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682db9d50007780000006b2b#266961907#2#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-25T13:55:33"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "T", "status": 1, "fareClass": "T", "operFareClass": "T", "FBC": "TRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "WWCMV-7MD29-INS/3573e71a-17bb-4ab5-b503-8c04945dc12d", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682db9d50007780000006b2b#266961907#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-21T11:37:36"}]}], "payments": [{"paymentID": *********, "paxID": 266962034, "method": "MSCD", "status": "1", "paidDate": "2025-04-25T13:56:56", "cardNum": "************0023", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 2818.08, "baseCurr": "QAR", "baseAmt": 2818.08, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "CONCHITA TOLARBA", "authCode": "469923", "reference": "22642951", "externalReference": "22642951", "tranId": "21022859", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21022859}, {"paymentID": *********, "paxID": 269699058, "method": "MSCD", "status": "1", "paidDate": "2025-05-21T12:00:03", "cardNum": "************0023", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 199.54, "baseCurr": "QAR", "baseAmt": 199.54, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "CONCHITA TOLARBA", "authCode": "533220", "reference": "23163923", "externalReference": "23163923", "tranId": "21543412", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21543412}], "OAFlights": null, "physicalFlights": [{"key": "16660469:181009:2025-06-08T10:55:00 AM", "LFID": 16660469, "PFID": 181009, "org": "DOH", "dest": "DXB", "depDate": "2025-06-08T10:55:00", "depTime": "2025-06-08T10:55:00", "arrTime": "2025-06-08T13:10:00", "carrier": "FZ", "flightNum": "004", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "004", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/30/2025 12:35:04 PM"}, {"key": "16660469:185106:2025-06-08T03:45:00 PM", "LFID": 16660469, "PFID": 185106, "org": "DXB", "dest": "TLV", "depDate": "2025-06-08T15:45:00", "depTime": "2025-06-08T15:45:00", "arrTime": "2025-06-08T18:15:00", "carrier": "FZ", "flightNum": "1125", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1125", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": true, "changeType": "AC", "flightChangeTime": "5/30/2025 12:35:04 PM"}, {"key": "17083824:181016:2025-06-08T10:25:00 PM", "LFID": 17083824, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-06-08T22:25:00", "depTime": "2025-06-08T22:25:00", "arrTime": "2025-06-09T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "17083824:187724:2025-06-09T06:05:00 AM", "LFID": 17083824, "PFID": 187724, "org": "DXB", "dest": "TLV", "depDate": "2025-06-09T06:05:00", "depTime": "2025-06-09T06:05:00", "arrTime": "2025-06-09T08:35:00", "carrier": "FZ", "flightNum": "1073", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1073", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": true}, {"key": "16158598:181630:2025-06-16T12:50:00 AM", "LFID": 16158598, "PFID": 181630, "org": "TLV", "dest": "DXB", "depDate": "2025-06-16T00:50:00", "depTime": "2025-06-16T00:50:00", "arrTime": "2025-06-16T05:10:00", "carrier": "FZ", "flightNum": "1808", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1808", "flightStatus": "OPEN", "originMetroGroup": "TLV", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Tel Aviv Ben Gurion", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "3/14/2025 7:05:09 PM"}, {"key": "16158598:181010:2025-06-16T08:45:00 AM", "LFID": 16158598, "PFID": 181010, "org": "DXB", "dest": "DOH", "depDate": "2025-06-16T08:45:00", "depTime": "2025-06-16T08:45:00", "arrTime": "2025-06-16T08:55:00", "carrier": "FZ", "flightNum": "001", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "001", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true, "changeType": "TK", "flightChangeTime": "3/14/2025 7:05:09 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-25T13:55:33", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-25T13:55:32"}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:36", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320252403, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355725558, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320252429, "paymentMap": [{"key": "1355725558:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1355725560, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320252404, "paymentMap": [{"key": "1355725560:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1355725554, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320252408, "paymentMap": [{"key": "1355725554:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355725555, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320252405, "paymentMap": [{"key": "1355725555:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1355725557, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1320252407, "paymentMap": [{"key": "1355725557:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1320252403, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252403:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1320252408, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252408:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320252429, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252429:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1320252404, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252404:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320252405, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252405:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320252407, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252407:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1355725553, "codeType": "AIR", "amt": -1115, "curr": "QAR", "originalAmt": -1115, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-21T11:37:36", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1355725553:*********", "paymentID": *********, "amt": -1115, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1115, "curr": "QAR", "originalAmt": 1115, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-25T13:55:33", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1115, "approveCode": 0}]}, {"chargeID": 1320260844, "codeType": "PMNT", "amt": 82.08, "curr": "QAR", "originalAmt": 82.08, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-25T13:57:00", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320260844:*********", "paymentID": *********, "amt": 82.08, "approveCode": 0}]}, {"chargeID": 1355725561, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:37:37", "desc": "Penalty AddedDueToModify FZ  020 DOH  - DXB  08-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725561:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1320252430, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1320252445, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181016"}, {"chargeID": 1320252444, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187724"}]}, {"recNum": 2, "charges": [{"chargeID": 1355725718, "codeType": "INSU", "amt": 22.37, "curr": "QAR", "originalAmt": 22.37, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:37:37", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725718:*********", "paymentID": *********, "amt": 22.37, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-21T11:37:37"}, {"chargeID": **********, "codeType": "INSU", "amt": 23, "curr": "QAR", "originalAmt": 23, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-25T13:55:33", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 23, "approveCode": 0}], "parameter2Name": "PROVIDER", "parameter2Value": "AIG"}, {"chargeID": **********, "codeType": "TAX", "taxID": 12530, "taxCode": "IL", "taxChargeID": **********, "amt": 110, "curr": "QAR", "originalAmt": 110, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Departure Passenger Airport Tax - (Intl.)", "comment": "Departure Passenger Airport Tax - (Intl.)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 110, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1320252436, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252436:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1320252434, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252434:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1320252435, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1320252435:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1115, "curr": "QAR", "originalAmt": 1115, "originalCurr": "QAR", "status": 1, "billDate": "2025-04-25T13:55:33", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1115, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1320252438, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1320252447, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181010", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1320252446, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-04-25T13:55:33", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181630", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1355725717, "codeType": "INSU", "amt": 22.36, "curr": "QAR", "originalAmt": 22.36, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T11:37:38", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725717:*********", "paymentID": *********, "amt": 22.36, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-21T11:37:38"}, {"chargeID": 1355725566, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1355725564, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725566:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355725568, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1355725564, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725568:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355725565, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1355725564, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725565:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1355725589, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1355725564, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725589:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1355725591, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1355725564, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725591:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1355725567, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1355725564, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:38", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725567:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355725590, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1355725564, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:38", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355725590:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1355725564, "codeType": "AIR", "amt": 1115, "curr": "QAR", "originalAmt": 1115, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T11:37:37", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 300, "tierPoints": 300, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1355725564:*********", "paymentID": *********, "amt": 149, "approveCode": 0}, {"key": "1355725564:*********", "paymentID": *********, "amt": 966, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1355785199, "codeType": "PMNT", "amt": 5.81, "curr": "QAR", "originalAmt": 5.81, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T12:00:09", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1355785199:*********", "paymentID": *********, "amt": 5.81, "approveCode": 0}]}, {"chargeID": 1355725592, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1355725564, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:37", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1355725599, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:38", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "185106", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1355725598, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-21T11:37:38", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181009", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}