{"seriesNum": "299", "PNR": "1MVIWG", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "83164471", "bookDate": "2025-05-27T07:21:10", "modifyDate": "2025-05-28T06:55:29", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 9, "activeSegCount": 2, "webBookingID": "864evbkun8o4u9le4069a9w4464e85c86ba43ab64bdb", "securityGUID": "864evbkun8o4u9le4069a9w4464e85c86ba43ab64bdb", "lastLoadGUID": "999cb5bf-4d7d-478a-9af8-71e21df83abc", "isAsyncPNR": false, "MasterPNR": "1MVIWG", "segments": [{"segKey": "16087488:16087488:8/13/2025 12:25:00 PM", "LFID": 16087488, "depDate": "2025-08-13T00:00:00", "flightGroupId": "16087488", "org": "EVN", "dest": "DXB", "depTime": "2025-08-13T12:25:00", "depTimeGMT": "2025-08-13T08:25:00", "arrTime": "2025-08-13T15:30:00", "operCarrier": "FZ", "operFlightNum": "718", "mrktCarrier": "FZ ", "mrktFlightNum": "718", "persons": [{"recNum": 15, "status": 1}, {"recNum": 18, "status": 1}, {"recNum": 14, "status": 1}, {"recNum": 17, "status": 1}, {"recNum": 16, "status": 1}, {"recNum": 11, "status": 1}, {"recNum": 13, "status": 1}, {"recNum": 12, "status": 1}, {"recNum": 10, "status": 1}], "legDetails": [{"PFID": 181219, "depDate": "2025-08-13T12:25:00", "legKey": "16087488:181219:8/13/2025 12:25:00 PM", "customerKey": "2A93F7D0CD38BF494886DA1D79F630C7DDF64E78FE419D000FBC463F21928D1D"}], "active": true, "changeType": "TK"}, {"segKey": "16087495:16087495:8/9/2025 8:10:00 AM", "LFID": 16087495, "depDate": "2025-08-09T00:00:00", "flightGroupId": "16087495", "org": "DXB", "dest": "EVN", "depTime": "2025-08-09T08:10:00", "depTimeGMT": "2025-08-09T04:10:00", "arrTime": "2025-08-09T11:25:00", "operCarrier": "FZ", "operFlightNum": "717", "mrktCarrier": "FZ ", "mrktFlightNum": "717", "persons": [{"recNum": 7, "status": 1}, {"recNum": 3, "status": 1}, {"recNum": 6, "status": 1}, {"recNum": 2, "status": 1}, {"recNum": 1, "status": 1}, {"recNum": 4, "status": 1}, {"recNum": 8, "status": 1}, {"recNum": 9, "status": 1}, {"recNum": 5, "status": 1}], "legDetails": [{"PFID": 181246, "depDate": "2025-08-09T08:10:00", "legKey": "16087495:181246:8/9/2025 8:10:00 AM", "customerKey": "8F5EEA64D1954715E9435905705362B558747810023E1BC499E9BC16088C18A8"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270281467, "fName": "AMAIRA AMBRISH", "lName": "LULLA", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2019-12-12T00:00:00", "recNum": [7, 15]}, {"paxID": 270281464, "fName": "SHRADDHA ASHOK", "lName": "CHABRIA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [3, 18]}, {"paxID": 270281459, "fName": "KAMLESH", "lName": "PARWANI", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1974-09-10T00:00:00", "nationality": "356", "FFNum": "274105451", "FFTier": "GOLD", "TierID": "5", "recNum": [6, 14]}, {"paxID": 270281463, "fName": "AMBRISH RAMESH", "lName": "LULLA", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [2, 17]}, {"paxID": 270281465, "fName": "NAISA KAMLESH", "lName": "PARWANI", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2016-11-28T00:00:00", "recNum": [1, 16]}, {"paxID": 270281461, "fName": "HIYA KAMLESH", "lName": "PARWANI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [4, 11]}, {"paxID": 270281462, "fName": "KANCHAN ASHOK", "lName": "CHABRIA", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [8, 13]}, {"paxID": 270281466, "fName": "SAMAIRA AMBRISH", "lName": "LULLA", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2019-12-12T00:00:00", "recNum": [9, 12]}, {"paxID": 270281460, "fName": "REVA KAMLESH", "lName": "PARWANI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [5, 10]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#7#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#5#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#6#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#3#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#2#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683563b100077700000006f7#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#9#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#4#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#8#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#2#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 11, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#3#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 12, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#8#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 13, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#4#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 14, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683563b100077700000006f7#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 15, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#9#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 16, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#7#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 17, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#5#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}, {"recNum": 18, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/27/2025 7:21:11 AM", "provider": "<PERSON>", "status": 1, "fareClass": "H", "operFareClass": "H", "FBC": "HRB6AE5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "HQ3US-JH32N-INS/477d1751-3adb-40b1-a3e3-fbba3f7bbc83", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683563b100077700000006f7#6#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-27T07:21:10"}]}], "payments": [{"paymentID": 210409493, "paxID": 270281446, "method": "IPAY", "status": "1", "paidDate": "2025-05-27T07:21:17", "cardNum": "************8342", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 29.87, "baseCurr": "AED", "baseAmt": 29.87, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "569218", "reference": "23275319", "externalReference": "23275319", "tranId": "21653089", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21653089}, {"paymentID": *********, "paxID": 270398756, "method": "IPAY", "status": "1", "paidDate": "2025-05-28T06:55:25", "cardNum": "************8342", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 11748.49, "baseCurr": "AED", "baseAmt": 11748.49, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON> ", "authCode": "221364", "reference": "23294164", "externalReference": "23294164", "tranId": "21675123", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21675123}], "OAFlights": null, "physicalFlights": [{"key": "16087495:181246:2025-08-09T08:10:00 AM", "LFID": 16087495, "PFID": 181246, "org": "DXB", "dest": "EVN", "depDate": "2025-08-09T08:10:00", "depTime": "2025-08-09T08:10:00", "arrTime": "2025-08-09T11:25:00", "carrier": "FZ", "flightNum": "717", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "717", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "EVN", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Yerevan", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 7:45:59 AM"}, {"key": "16087488:181219:2025-08-13T12:25:00 PM", "LFID": 16087488, "PFID": 181219, "org": "EVN", "dest": "DXB", "depDate": "2025-08-13T12:25:00", "depTime": "2025-08-13T12:25:00", "arrTime": "2025-08-13T15:30:00", "carrier": "FZ", "flightNum": "718", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "718", "flightStatus": "OPEN", "originMetroGroup": "EVN", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11100, "reaccomChangeAlert": false, "originName": "Yerevan", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 7:46:27 AM"}], "chargeInfos": [{"recNum": 7, "charges": [{"chargeID": 1363558071, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558071:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363558030, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363558027, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558030:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558033, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363558027, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558033:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558031, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363558027, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558031:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363558032, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363558027, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558032:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363558029, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363558027, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558029:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558027, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558027:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558072, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558072:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558028, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363558027, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 15, "charges": [{"chargeID": 1363558073, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558073:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363558035, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363558034, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558035:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1363558037, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363558034, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558037:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558038, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363558034, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558038:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558034, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558034:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558074, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558074:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558036, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363558034, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1363558059, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558059:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557996, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363557990, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557996:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557995, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363557990, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557995:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363557994, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363557990, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557994:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363557992, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557990, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557992:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557993, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557990, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557993:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557990, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557990:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558060, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558060:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557991, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557990, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 18, "charges": [{"chargeID": 1363558061, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558061:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557998, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363557997, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557998:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558001, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557997, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558001:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558000, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557997, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558000:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558002, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1363557997, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558002:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1363557997, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557997:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558062, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558062:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557999, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557997, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 6, "charges": [{"chargeID": 1363558039, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558039:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557908, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363557904, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557908:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363557929, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363557904, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557929:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363557930, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363557904, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557930:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557906, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557904, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557906:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557907, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557904, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557907:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557904, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 180, "PromoTier": 0, "paymentMap": [{"key": "1363557904:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 180, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363560010, "codeType": "PMNT", "amt": 0.87, "curr": "AED", "originalAmt": 0.87, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:24", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363560010:210409493", "paymentID": 210409493, "amt": 0.87, "approveCode": 0}]}, {"chargeID": 1365174571, "codeType": "PMNT", "amt": 342.19, "curr": "AED", "originalAmt": 342.19, "originalCurr": "AED", "status": 1, "billDate": "2025-05-28T06:55:29", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365174571:*********", "paymentID": *********, "amt": 342.19, "approveCode": 0}]}, {"chargeID": 1363560011, "codeType": "BHFT", "amt": 29, "curr": "AED", "originalAmt": 29, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:24", "desc": "Hold Book Charge", "comment": "TLTRULE:102", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363560011:210409493", "paymentID": 210409493, "amt": 29, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "parameter1Name": "TLT_EXTENSION_HR", "parameter1Value": "24", "parameter2Name": "TLT_EXTENSION_FEE", "parameter2Value": "29.0"}, {"chargeID": 1363558040, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558040:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557905, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557904, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 14, "charges": [{"chargeID": 1363558041, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558041:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557936, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1363557931, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557936:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1363557934, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557931, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557934:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557932, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363557931, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557932:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557935, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557931, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557935:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557931, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 180, "PromoTier": 0, "paymentMap": [{"key": "1363557931:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 180, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558042, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558042:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557933, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557931, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1363558055, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558055:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557982, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363557977, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557982:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363557979, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557977, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557979:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557983, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363557977, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557983:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557981, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363557977, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557981:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363557980, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557977, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557980:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557977, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557977:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558056, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558056:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557978, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557977, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 17, "charges": [{"chargeID": 1363558057, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558057:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557989, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1363557984, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557989:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1363557987, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557984, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557987:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557985, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363557984, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557985:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557988, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557984, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557988:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557984, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557984:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558058, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558058:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557986, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557984, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": 1363558063, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558063:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:08"}, {"chargeID": 1363558007, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363558003, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558007:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363558006, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363558003, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558006:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558005, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363558003, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558005:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558008, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363558003, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558008:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363558009, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363558003, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558009:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558003, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558003:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558064, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558064:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558004, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363558003, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 16, "charges": [{"chargeID": 1363558065, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558065:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363558011, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363558010, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558011:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1363558014, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363558010, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558014:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558013, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363558010, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558013:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558010, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558010:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558066, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558066:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558012, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363558010, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1363558047, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558047:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557953, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557951, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557953:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557955, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363557951, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557955:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363557956, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363557951, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557956:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363557957, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363557951, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557957:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557954, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557951, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557954:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557951, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557951:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558048, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558048:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557952, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557951, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 11, "charges": [{"chargeID": 1363558049, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558049:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557961, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557958, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557961:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557963, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1363557958, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557963:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1363557962, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557958, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557962:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557959, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363557958, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557959:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557958, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557958:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558050, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558050:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557960, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557958, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 8, "charges": [{"chargeID": 1363558051, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558051:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557970, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363557964, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557970:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557966, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557964, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557966:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557968, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363557964, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557968:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363557969, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363557964, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557969:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363557967, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557964, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557967:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557964, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557964:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558052, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558052:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557965, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557964, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 13, "charges": [{"chargeID": 1363558053, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558053:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557974, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557971, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557974:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557976, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1363557971, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557976:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1363557972, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363557971, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557972:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557975, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557971, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557975:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557971, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557971:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558054, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558054:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557973, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557971, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 9, "charges": [{"chargeID": 1363558067, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558067:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363558017, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363558015, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558017:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558021, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363558015, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558021:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558019, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363558015, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558019:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363558020, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363558015, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558020:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363558018, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363558015, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558018:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558015, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558015:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558068, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558068:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558016, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363558015, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 12, "charges": [{"chargeID": 1363558069, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558069:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363558026, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363558022, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558026:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363558023, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363558022, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558023:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1363558025, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363558022, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558025:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363558022, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558022:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558070, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558070:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558024, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363558022, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 5, "charges": [{"chargeID": 1363558043, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558043:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557941, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557938, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557941:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557943, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363557938, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557943:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363557940, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557938, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557940:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557942, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363557938, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557942:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363557944, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363557938, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557944:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557938, "codeType": "AIR", "amt": 295, "curr": "AED", "originalAmt": 295, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557938:*********", "paymentID": *********, "amt": 295, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558044, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181246", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558044:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181246", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557939, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557938, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 10, "charges": [{"chargeID": 1363558045, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363558045:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"87.48\",\r\n  \"Tax\": \"4.17\",\r\n  \"SegPaxCount\": \"18\"\r\n}", "ChargeBookDate": "2025-05-27T07:21:09"}, {"chargeID": 1363557948, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363557945, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557948:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363557949, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363557945, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557949:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557950, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1363557945, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557950:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1363557946, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1363557945, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363557946:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1363557945, "codeType": "AIR", "amt": 265, "curr": "AED", "originalAmt": 265, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T07:21:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363557945:*********", "paymentID": *********, "amt": 265, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363558046, "codeType": "FRST", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181219", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1363558046:*********", "paymentID": *********, "amt": 65, "approveCode": 0}], "PFID": "181219", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363557947, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1363557945, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-27T07:21:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}