{"seriesNum": "299", "PNR": "MTHGZ9", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82355780", "bookDate": "2025-04-27T21:30:31", "modifyDate": "2025-05-14T10:18:13", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "6006b1d08d2fx48e45pc4kn895tbn0iamea8b24b1042", "securityGUID": "6006b1d08d2fx48e45pc4kn895tbn0iamea8b24b1042", "lastLoadGUID": "a00f2d75-dd29-4853-9790-b701b35f9dc5", "isAsyncPNR": false, "MasterPNR": "MTHGZ9", "segments": [{"segKey": "16087854:16087854:5/15/2025 11:45:00 PM", "LFID": 16087854, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087854", "org": "BEG", "dest": "DXB", "depTime": "2025-05-15T23:45:00", "depTimeGMT": "2025-05-15T21:45:00", "arrTime": "2025-05-16T07:05:00", "operCarrier": "FZ", "operFlightNum": "1750", "mrktCarrier": "FZ ", "mrktFlightNum": "1750", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181604, "depDate": "2025-05-15T23:45:00", "legKey": "16087854:181604:5/15/2025 11:45:00 PM", "customerKey": "DA6B475F328ABD976F4917C5B5BCBE97F7303BBA2D7BEF3EA2FD880F0A700022"}], "active": true, "changeType": "TK"}, {"segKey": "16087827:16087827:5/8/2025 6:55:00 PM", "LFID": 16087827, "depDate": "2025-05-08T00:00:00", "flightGroupId": "16087827", "org": "DXB", "dest": "BEG", "depTime": "2025-05-08T18:55:00", "depTimeGMT": "2025-05-08T14:55:00", "arrTime": "2025-05-08T22:45:00", "operCarrier": "FZ", "operFlightNum": "1749", "mrktCarrier": "FZ ", "mrktFlightNum": "1749", "persons": [{"recNum": 1, "status": 9}], "legDetails": [{"PFID": 181557, "depDate": "2025-05-08T18:55:00", "legKey": "16087827:181557:5/8/2025 6:55:00 PM", "customerKey": "50AB219071075E444D87F9F201BB8E5124B7A0C49B6AE31C189767DAC2F45B6A"}], "active": true, "changeType": "TK"}, {"segKey": "16087760:16087760:6/17/2025 1:55:00 PM", "LFID": 16087760, "depDate": "2025-06-17T00:00:00", "flightGroupId": "16087760", "org": "BEG", "dest": "DXB", "depTime": "2025-06-17T13:55:00", "depTimeGMT": "2025-06-17T11:55:00", "arrTime": "2025-06-17T21:15:00", "operCarrier": "FZ", "operFlightNum": "1746", "mrktCarrier": "FZ ", "mrktFlightNum": "1746", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181550, "depDate": "2025-06-17T13:55:00", "legKey": "16087760:181550:6/17/2025 1:55:00 PM", "customerKey": "DE757096DEE8701E9146666E594D69A159EB11C8229494DA3650D30A4FD2FB07"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 267163627, "fName": "VLADIMIR", "lName": "MILOSEVIC", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "4/27/2025 9:28:41 PM", "provider": "AIG", "status": 9, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "986872176", "insuTransID": "bd4eb5db-906e-49fc-94c0-0d9910da0661", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680ea064000777000000de28#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-04-27T21:30:31"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "4/27/2025 9:28:41 PM", "provider": "AIG", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "986872176", "insuTransID": "bd4eb5db-906e-49fc-94c0-0d9910da0661", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680ea064000777000000de28#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-27T21:30:31"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "V", "insuPurchasedate": "5/14/2025 10:18:01 AM", "provider": "<PERSON>", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VOL8RS5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "39A8M-PMMEC-INS/95889f32-3ede-4e79-9133-9a6cf91bb0ac", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682461d30007780000001eac#267163627#1#MOBILE#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 12, "bookDate": "2025-05-14T10:18:00"}]}], "payments": [{"paymentID": *********, "paxID": 267163645, "method": "VISA", "status": "1", "paidDate": "2025-04-27T21:31:24", "cardNum": "************2852", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2959.65, "baseCurr": "AED", "baseAmt": 2959.65, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "authCode": "YAYYXZ", "reference": "22691116", "externalReference": "22691116", "tranId": "21065376", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21065376}, {"paymentID": *********, "paxID": 268940358, "method": "VISA", "status": "1", "paidDate": "2025-05-14T10:18:08", "cardNum": "************2852", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 206.72, "baseCurr": "AED", "baseAmt": 206.72, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "IIHJEC", "reference": "23027594", "externalReference": "23027594", "tranId": "21401855", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21401855}], "OAFlights": null, "physicalFlights": [{"key": "16087827:181557:2025-05-08T06:55:00 PM", "LFID": 16087827, "PFID": 181557, "org": "DXB", "dest": "BEG", "depDate": "2025-05-08T18:55:00", "depTime": "2025-05-08T18:55:00", "arrTime": "2025-05-08T22:45:00", "carrier": "FZ", "flightNum": "1749", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1749", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BEG", "operatingCarrier": "FZ", "flightDuration": 21000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Belgrade", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:12:47 AM"}, {"key": "16087854:181604:2025-05-15T11:45:00 PM", "LFID": 16087854, "PFID": 181604, "org": "BEG", "dest": "DXB", "depDate": "2025-05-15T23:45:00", "depTime": "2025-05-15T23:45:00", "arrTime": "2025-05-16T07:05:00", "carrier": "FZ", "flightNum": "1750", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1750", "flightStatus": "CLOSED", "originMetroGroup": "BEG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Belgrade", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:07:09 AM"}, {"key": "16087760:181550:2025-06-17T01:55:00 PM", "LFID": 16087760, "PFID": 181550, "org": "BEG", "dest": "DXB", "depDate": "2025-06-17T13:55:00", "depTime": "2025-06-17T13:55:00", "arrTime": "2025-06-17T21:15:00", "carrier": "FZ", "flightNum": "1746", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1746", "flightStatus": "OPEN", "originMetroGroup": "BEG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 19200, "reaccomChangeAlert": false, "originName": "Belgrade", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 8:06:45 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 78.23, "curr": "AED", "originalAmt": 78.23, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-04-27T21:30:32", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 78.23, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-27T21:30:31"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1322851843, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322851843:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1322851841, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322851841:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1322851845, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322851845:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1237, "curr": "AED", "originalAmt": 1237, "originalCurr": "AED", "status": 1, "billDate": "2025-04-27T21:30:32", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1237, "approveCode": 0}]}, {"chargeID": 1322853378, "codeType": "PMNT", "amt": 86.2, "curr": "AED", "originalAmt": 86.2, "originalCurr": "AED", "status": 1, "billDate": "2025-04-27T21:31:28", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322853378:*********", "paymentID": *********, "amt": 86.2, "approveCode": 0}]}, {"chargeID": 1322851847, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1322851842, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1322851861, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181557"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 78.22, "curr": "AED", "originalAmt": 78.22, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-04-27T21:30:32", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 78.22, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-27T21:30:31"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 9086, "taxCode": "LG", "taxChargeID": **********, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Security Charge", "comment": "Security Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1322851850, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322851850:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1322851854, "codeType": "TAX", "taxID": 8264, "taxCode": "RS", "taxChargeID": **********, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322851854:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1322851852, "codeType": "TAX", "taxID": 4964, "taxCode": "RF", "taxChargeID": **********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "CAD Passenger charge", "comment": "CAD Passenger charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1322851852:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1345660020, "codeType": "TAX", "taxID": 8264, "taxCode": "RS", "taxChargeID": **********, "amt": -90, "curr": "AED", "originalAmt": -90, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T10:18:00", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1322851854, "paymentMap": [{"key": "1345660020:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1345660017, "codeType": "TAX", "taxID": 9086, "taxCode": "LG", "taxChargeID": **********, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T10:18:00", "desc": "Security Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1345660017:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1345660018, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T10:18:00", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1322851850, "paymentMap": [{"key": "1345660018:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1345660016, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T10:18:00", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1345660016:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1345660019, "codeType": "TAX", "taxID": 4964, "taxCode": "RF", "taxChargeID": **********, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T10:18:00", "desc": "CAD Passenger charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1322851852, "paymentMap": [{"key": "1345660019:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 655, "curr": "AED", "originalAmt": 655, "originalCurr": "AED", "status": 0, "billDate": "2025-04-27T21:30:32", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 655, "approveCode": 0}]}, {"chargeID": 1345660021, "codeType": "AIR", "amt": -655, "curr": "AED", "originalAmt": -655, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T10:18:00", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1345660021:*********", "paymentID": *********, "amt": -655, "approveCode": 0}]}, {"chargeID": 1322851856, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1322851851, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1322851862, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-04-27T21:30:32", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181550"}]}, {"recNum": 3, "charges": [{"chargeID": 1345660339, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-14T10:18:01", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660339:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-14T10:18:01"}, {"chargeID": 1345660205, "codeType": "TAX", "taxID": 8264, "taxCode": "RS", "taxChargeID": 1345660199, "amt": 90, "curr": "AED", "originalAmt": 90, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660205:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1345660202, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1345660199, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660202:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345660200, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1345660199, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660200:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1345660207, "codeType": "TAX", "taxID": 9086, "taxCode": "LG", "taxChargeID": 1345660199, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Security Charge", "comment": "Security Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660207:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1345660204, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1345660199, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660204:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1345660203, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1345660199, "amt": 310, "curr": "AED", "originalAmt": 310, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660203:*********", "paymentID": *********, "amt": 310, "approveCode": 0}]}, {"chargeID": 1345660201, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1345660199, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660201:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1345660206, "codeType": "TAX", "taxID": 4964, "taxCode": "RF", "taxChargeID": 1345660199, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "CAD Passenger charge", "comment": "CAD Passenger charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660206:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1345660199, "codeType": "AIR", "amt": 665, "curr": "AED", "originalAmt": 665, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T10:18:01", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345660199:*********", "paymentID": *********, "amt": 165, "approveCode": 0}, {"key": "1345660199:*********", "paymentID": *********, "amt": 500, "approveCode": 0}]}, {"chargeID": 1345754934, "codeType": "PMNT", "amt": 6.02, "curr": "AED", "originalAmt": 6.02, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T10:18:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1345754934:*********", "paymentID": *********, "amt": 6.02, "approveCode": 0}]}, {"chargeID": 1345660209, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1345660199, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1345660208, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1345660199, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1345660232, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-14T10:18:01", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181604"}]}], "parentPNRs": [], "childPNRs": []}