{"seriesNum": "299", "PNR": "DCXU2B", "bookAgent": "WEB_MOBILE", "resCurrency": "QAR", "PNRPin": "82957545", "bookDate": "2025-05-19T14:39:14", "modifyDate": "2025-05-21T17:11:35", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "76ce643d00184988aa19vcwek8l0ueb2vb62h2hd480c", "securityGUID": "76ce643d00184988aa19vcwek8l0ueb2vb62h2hd480c", "lastLoadGUID": "766005b9-5e45-4b59-af60-34cbd4827d09", "isAsyncPNR": false, "MasterPNR": "DCXU2B", "segments": [{"segKey": "16108885:16108885:5/26/2025 10:25:00 PM", "LFID": 16108885, "depDate": "2025-05-26T00:00:00", "flightGroupId": "16108885", "org": "DOH", "dest": "EBB", "depTime": "2025-05-26T22:25:00", "depTimeGMT": "2025-05-26T19:25:00", "arrTime": "2025-05-27T10:25:00", "operCarrier": "FZ", "operFlightNum": "020/619", "mrktCarrier": "FZ", "mrktFlightNum": "020/619", "persons": [{"recNum": 2, "status": 9}], "legDetails": [{"PFID": 181016, "depDate": "2025-05-26T22:25:00", "legKey": "16108885:181016:5/26/2025 10:25:00 PM", "customerKey": "812FD21FA09D6D8D2B28D2BCC118805A46EB48F757A06BCBAA1AB649FA013F2E"}, {"PFID": 181198, "depDate": "2025-05-27T05:55:00", "legKey": "16108885:181198:5/27/2025 5:55:00 AM", "customerKey": "FEA3EBCDEC61D25D4EA453B7EA6D2C75C81B10D95F70FA8B7DE0213851D62426"}], "active": true, "changeType": "AC"}, {"segKey": "16108885:16108885:5/21/2025 10:25:00 PM", "LFID": 16108885, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16108885", "org": "DOH", "dest": "EBB", "depTime": "2025-05-21T22:25:00", "depTimeGMT": "2025-05-21T19:25:00", "arrTime": "2025-05-22T10:25:00", "operCarrier": "FZ", "operFlightNum": "020/619", "mrktCarrier": "FZ ", "mrktFlightNum": "020/619", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181016, "depDate": "2025-05-21T22:25:00", "legKey": "16108885:181016:5/21/2025 10:25:00 PM", "customerKey": "B6D0B0C3E695A9A8B90BB3264EC4DC21E9F44D3034FE826472B7EC3A2DB0A35A"}, {"PFID": 181198, "depDate": "2025-05-22T05:55:00", "legKey": "16108885:181198:5/22/2025 5:55:00 AM", "customerKey": "0907D803B56DBE1348007354D12AECBF40AA4652FEFF78BABCA226BBEB70F0B2"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 269482447, "fName": "KANSIIME", "lName": "JOANITAH", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/19/2025 2:39:14 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KO6QA5", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "RBH2R-XUBBT-INS", "insuTransID": "RBH2R-XUBBT-INS/119e3931-de49-4bfd-9624-29ce4bc261ee", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682b40af00077800000037f2#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 11, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-19T14:39:14"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "falah.buksh", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/19/2025 2:39:14 PM", "provider": "<PERSON>", "status": 9, "fareClass": "H", "operFareClass": "H", "FBC": "HO6QA5", "fareBrand": "Lite", "cabin": "ECONOMY", "insuTransID": "RBH2R-XUBBT-INS/119e3931-de49-4bfd-9624-29ce4bc261ee", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682e05850007780000009799#269482447#1#ENT#OneSearch#CHANGE", "fareTypeID": 11, "channelID": 1, "bookDate": "2025-05-21T16:57:36"}]}], "payments": [{"paymentID": 209568119, "paxID": 269482900, "method": "MTUG", "status": "2", "paidDate": "2025-05-19T14:42:04", "gateway": "EPS", "paidCurr": "UGX", "paidAmt": 898104, "baseCurr": "QAR", "baseAmt": 839.89, "userID": "WEB_MOBILE", "channelID": 12, "authCode": "A5395762", "reference": "A5395762", "externalReference": "A5395762", "tranId": "21503637", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "DPO_MTUG_UGX", "exchangeRate": "1069.31120904", "resExternalPaymentID": 21503637}, {"paymentID": *********, "paxID": 269482901, "method": "MTUG", "status": "1", "paidDate": "2025-05-19T14:43:19", "gateway": "EPS", "paidCurr": "UGX", "paidAmt": 898104, "baseCurr": "QAR", "baseAmt": 839.89, "userID": "WEB_MOBILE", "channelID": 12, "authCode": "A5395789", "reference": "A5395789", "externalReference": "A5395789", "tranId": "21503637", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "DPO_MTUG_UGX", "exchangeRate": "1069.31120904", "resExternalPaymentID": 21503637}, {"paymentID": *********, "paxID": 269736823, "method": "MSCD", "status": "1", "paidDate": "2025-05-21T17:11:30", "cardNum": "************0109", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 660.93, "baseCurr": "QAR", "baseAmt": 660.93, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "419404", "reference": "23169942", "externalReference": "23169942", "tranId": "21550475", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21550475}], "OAFlights": null, "physicalFlights": [{"key": "16108885:181016:2025-05-21T10:25:00 PM", "LFID": 16108885, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-05-21T22:25:00", "depTime": "2025-05-21T22:25:00", "arrTime": "2025-05-22T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:06 AM"}, {"key": "16108885:181198:2025-05-22T05:55:00 AM", "LFID": 16108885, "PFID": 181198, "org": "DXB", "dest": "EBB", "depDate": "2025-05-22T05:55:00", "depTime": "2025-05-22T05:55:00", "arrTime": "2025-05-22T10:25:00", "carrier": "FZ", "flightNum": "619", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "619", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "EBB", "operatingCarrier": "FZ", "flightDuration": 19800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:06 AM"}, {"key": "16108885:181016:2025-05-26T10:25:00 PM", "LFID": 16108885, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-05-26T22:25:00", "depTime": "2025-05-26T22:25:00", "arrTime": "2025-05-27T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:06 AM"}, {"key": "16108885:181198:2025-05-27T05:55:00 AM", "LFID": 16108885, "PFID": 181198, "org": "DXB", "dest": "EBB", "depDate": "2025-05-27T05:55:00", "depTime": "2025-05-27T05:55:00", "arrTime": "2025-05-27T10:25:00", "carrier": "FZ", "flightNum": "619", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "619", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "EBB", "operatingCarrier": "FZ", "flightDuration": 19800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:06 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1356279445, "codeType": "INSU", "taxChargeID": 1356279438, "amt": -35.43, "curr": "QAR", "originalAmt": -35.43, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879047, "paymentMap": [{"key": "1356279445:*********", "paymentID": *********, "amt": -35.43, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-21T16:57:37"}, {"chargeID": 1352879047, "codeType": "INSU", "amt": 35.43, "curr": "QAR", "originalAmt": 35.43, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879047:*********", "paymentID": *********, "amt": 35.43, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1*********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1356279438, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879045, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1356279443, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1356279438, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "Passenger Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879043, "paymentMap": [{"key": "1356279443:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1356279444, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1356279438, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "Passenger safety and security fees (PSSF)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879042, "paymentMap": [{"key": "1356279444:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1356279446, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1356279438, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "Airport Fee.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879041, "paymentMap": [{"key": "1356279446:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1356279447, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1356279438, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879044, "paymentMap": [{"key": "1356279447:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1356279448, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1356279438, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879046, "paymentMap": [{"key": "1356279448:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1356279489, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1356279438, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-21T16:57:37", "desc": "Passenger Facility Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879040, "paymentMap": [{"key": "1356279489:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1352879045, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352879039, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879045:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1352879043, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1352879039, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879043:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1352879042, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1352879039, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879042:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1352879040, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1352879039, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879040:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352879041, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1352879039, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879041:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1352879044, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352879039, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879044:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1352879046, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352879039, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-19T14:39:14", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879046:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1356279438, "codeType": "AIR", "amt": -390, "curr": "QAR", "originalAmt": -390, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-21T16:57:37", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1352879039, "paymentMap": [{"key": "1356279438:*********", "paymentID": *********, "amt": -390, "approveCode": 0}]}, {"chargeID": 1352879039, "codeType": "AIR", "amt": 390, "curr": "QAR", "originalAmt": 390, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-19T14:39:14", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352879039:*********", "paymentID": *********, "amt": 390, "approveCode": 0}]}, {"chargeID": 1352886827, "codeType": "PMNT", "amt": 24.46, "curr": "QAR", "originalAmt": 24.46, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-19T14:43:27", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352886827:*********", "paymentID": *********, "amt": 24.46, "approveCode": 0}]}, {"chargeID": 1356279490, "codeType": "PNLT", "amt": 580, "curr": "QAR", "originalAmt": 580, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:37", "billDate": "2025-05-21T16:57:37", "desc": "CancelNoRefund FZ 020/619 DOH - EBB 21.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279490:*********", "paymentID": *********, "amt": 580, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1356279500, "codeType": "INSU", "amt": 35.43, "curr": "QAR", "originalAmt": 35.43, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:55:36", "billDate": "2025-05-21T16:57:38", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279500:*********", "paymentID": *********, "amt": 35.43, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-21T16:57:38"}, {"chargeID": 1356279492, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1356279491, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "G4: Passenger Facility Charge.", "comment": "G4: Passenger Facility Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279492:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1356279493, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1356279491, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "QA: Airport Fee.", "comment": "QA: Airport Fee.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279493:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1356279494, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1356279491, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "R9: Passenger safety and security fees (PSSF)", "comment": "R9: Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279494:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1356279495, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1356279491, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "PZ: Passenger Service Charge", "comment": "PZ: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279495:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1356279496, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1356279491, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279496:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1356279497, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1356279491, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279497:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1356279498, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1356279491, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279498:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1356279491, "codeType": "AIR", "amt": 390, "curr": "QAR", "originalAmt": 390, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:37", "billDate": "2025-05-21T16:57:38", "desc": "FZ 020 DOH-DXB 26May2025 Mon 22:25 00:40\r\nFZ 619 DXB-EBB 27May2025 Tue 05:55 10:25\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279491:*********", "paymentID": *********, "amt": 235.43, "approveCode": 0}, {"key": "1356279491:*********", "paymentID": *********, "amt": 154.57, "approveCode": 0}]}, {"chargeID": 1356300444, "codeType": "PMNT", "amt": 19.25, "curr": "QAR", "originalAmt": 19.25, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-21T17:11:35", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356300444:*********", "paymentID": *********, "amt": 19.25, "approveCode": 0}]}, {"chargeID": 1356279499, "codeType": "MFEE", "amt": 61.68, "curr": "QAR", "originalAmt": 61.68, "originalCurr": "QAR", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-21T16:57:38", "billDate": "2025-05-21T16:57:38", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356279499:*********", "paymentID": *********, "amt": 61.68, "approveCode": 0}]}]}], "parentPNRs": [], "childPNRs": []}