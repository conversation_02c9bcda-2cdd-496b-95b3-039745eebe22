{"seriesNum": "299", "PNR": "UH5OTK", "bookAgent": "support.sys", "IATA": "1003081P", "resCurrency": "AED", "PNRPin": "82565348", "bookDate": "2025-05-05T15:19:05", "modifyDate": "2025-05-13T14:40:40", "resType": "TA", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "abe0b745qf75e0j9u244z964x81a492b6aaba51f6299", "securityGUID": "abe0b745qf75e0j9u244z964x81a492b6aaba51f6299", "lastLoadGUID": "3506372022D902CDE0631E206F0A3F06", "MasterPNR": "UH5OTK", "segments": [{"segKey": "16087284:16087284:5/21/2025 2:35:00 PM", "LFID": 16087284, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087284", "org": "DXB", "dest": "MCT", "depTime": "2025-05-21T14:35:00", "depTimeGMT": "2025-05-21T10:35:00", "arrTime": "2025-05-21T15:45:00", "operCarrier": "FZ", "operFlightNum": "043", "mrktCarrier": "FZ ", "mrktFlightNum": "043", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181055, "depDate": "2025-05-21T14:35:00", "legKey": "16087284:181055:5/21/2025 2:35:00 PM", "customerKey": "554236C2E1E62572693EBEC957A81917C205DD3420C4412AB5391D61E321E0D5"}], "active": true, "changeType": "AC"}, {"segKey": "16528370:16528370:12/18/2025 7:10:00 AM", "LFID": 16528370, "depDate": "2025-12-18T00:00:00", "flightGroupId": "16528370", "org": "DXB", "dest": "MCT", "depTime": "2025-12-18T07:10:00", "depTimeGMT": "2025-12-18T03:10:00", "arrTime": "2025-12-18T08:20:00", "operCarrier": "FZ", "operFlightNum": "045", "mrktCarrier": "FZ ", "mrktFlightNum": "045", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 184294, "depDate": "2025-12-18T07:10:00", "legKey": "16528370:184294:12/18/2025 7:10:00 AM", "customerKey": "252BD9062A8924BCA1E8228C4EB885D92A04487BEA688028E7123C6DFD4D68C5"}], "active": true}], "persons": [{"paxID": 267972538, "fName": "PRATIMA", "lName": "TEST", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "support.sys", "cancelAgent": "support.sys", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "L", "insuPurchasedate": "5/5/2025 3:17:41 PM", "provider": "AIG", "status": 0, "fareClass": "L", "operFareClass": "L", "FBC": "LO6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987207730", "insuTransID": "ca065421-4e86-40df-a4f9-dd131b2cfb91", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6818d6640007780000002bbc#1#1#TA#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 16, "cancelReasonID": 0, "bookDate": "2025-05-05T15:19:05"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "support.sys", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "L", "insuPurchasedate": "5/5/2025 11:35:20 PM", "provider": "<PERSON>", "status": 0, "fareClass": "L", "operFareClass": "L", "FBC": "LOB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "TD2HP-MB4CB-INS/058bc6cd-443c-43d3-83d3-8c9e6861c129", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68194ae9000778000000423a#267972538#1#TA#SFQE#CHANGE", "fareTypeID": 22, "channelID": 16, "cancelReasonID": 7, "bookDate": "2025-05-05T23:35:19"}]}], "payments": [{"paymentID": *********, "paxID": 267970601, "method": "VCHR", "status": "1", "paidDate": "2025-05-05T23:35:23", "IATANum": "1003081P", "voucherNum": 3255489, "gateway": "EPS", "paidCurr": "AED", "paidAmt": 294.7, "baseCurr": "AED", "baseAmt": 294.7, "userID": "support.sys", "channelID": 16, "voucherNumFull": "QHBY94", "authCode": "QHBY94", "reference": "A5314014", "externalReference": "A5314014", "tranId": "21232113", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21232113}, {"paymentID": *********, "paxID": 267970601, "method": "VCHR", "status": "1", "paidDate": "2025-05-05T15:19:14", "IATANum": "1003081P", "voucherNum": 3255489, "gateway": "EPS", "paidCurr": "AED", "paidAmt": 445.65, "baseCurr": "AED", "baseAmt": 445.65, "userID": "support.sys", "channelID": 16, "voucherNumFull": "QHBY94", "authCode": "QHBY94", "reference": "A5312520", "externalReference": "A5312520", "tranId": "21225796", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21225796}, {"paymentID": *********, "paxID": 267972538, "method": "VCHR", "status": "1", "paidDate": "2025-05-13T14:40:39", "voucherNum": 3259741, "paidCurr": "AED", "paidAmt": -425, "baseCurr": "AED", "baseAmt": -425, "userID": "nusrath.shaik", "channelID": 1, "paymentComment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "voucherNumFull": "YQDBWJ", "reference": "Cancel Refund Voucher Flier", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "16087284:181055:2025-05-21T02:35:00 PM", "LFID": 16087284, "PFID": 181055, "org": "DXB", "dest": "MCT", "depDate": "2025-05-21T14:35:00", "depTime": "2025-05-21T14:35:00", "arrTime": "2025-05-21T15:45:00", "carrier": "FZ", "flightNum": "043", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "043", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "MCT", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Muscat", "isActive": false, "changeType": "AC", "flightChangeTime": "4/7/2025 12:35:55 PM"}, {"key": "16528370:184294:2025-12-18T07:10:00 AM", "LFID": 16528370, "PFID": 184294, "org": "DXB", "dest": "MCT", "depDate": "2025-12-18T07:10:00", "depTime": "2025-12-18T07:10:00", "arrTime": "2025-12-18T08:20:00", "carrier": "FZ", "flightNum": "045", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "045", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "MCT", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Muscat", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 34.65, "curr": "AED", "originalAmt": 34.65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T15:19:05", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 34.65, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T15:19:04"}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333064653, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1333486133, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333064652, "paymentMap": [{"key": "1333486133:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1333486135, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333064656, "paymentMap": [{"key": "1333486135:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1333486139, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333064655, "paymentMap": [{"key": "1333486139:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1333064652, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333064652:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333064656, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333064656:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333064653, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333064653:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1333064655, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333064655:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1333486134, "codeType": "AIR", "amt": -165, "curr": "AED", "originalAmt": -165, "originalCurr": "AED", "status": 0, "billDate": "2025-05-05T23:35:19", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1333486134:*********", "paymentID": *********, "amt": -165, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 165, "curr": "AED", "originalAmt": 165, "originalCurr": "AED", "status": 0, "billDate": "2025-05-05T15:19:05", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 165, "approveCode": 0}]}, {"chargeID": 1333486136, "codeType": "NSST", "amt": -16, "curr": "AED", "originalAmt": -16, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "NSST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333064658, "paymentMap": [{"key": "1333486136:*********", "paymentID": *********, "amt": -16, "approveCode": 0}], "PFID": "184294"}, {"chargeID": 1333064658, "codeType": "NSST", "amt": 16, "curr": "AED", "originalAmt": 16, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "NSST", "comment": "FLXID:NSST_ZONE1_MID::184294", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333064658:*********", "paymentID": *********, "amt": 16, "approveCode": 0}], "PFID": "184294"}, {"chargeID": 1333486140, "codeType": "PNLT", "amt": 245, "curr": "AED", "originalAmt": 245, "originalCurr": "AED", "status": 1, "billDate": "2025-05-05T23:35:20", "desc": "Penalty AddedDueToModify FZ  045 DXB  - MCT  18-Dec-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486140:*********", "paymentID": *********, "amt": 245, "approveCode": 0}]}, {"chargeID": 1333486137, "codeType": "BAGB", "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "BAGB", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333064657, "paymentMap": [{"key": "1333486137:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1333064657, "codeType": "BAGB", "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:19:05", "desc": "BAGB", "comment": "FLXID:GCC-AE DXB-OM:", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333064657:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1333486142, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-05T23:35:20", "desc": "INSU", "reasonID": 0, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486142:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-05T23:35:20"}, {"chargeID": 1333486159, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333486154, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486159:*********", "paymentID": *********, "amt": 44, "approveCode": 0}, {"key": "1333486159:*********", "paymentID": *********, "amt": 36, "approveCode": 0}]}, {"chargeID": 1333486155, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1333486154, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486155:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1333486158, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1333486154, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486158:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1333486156, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1333486154, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486156:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333486157, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333486154, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486157:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1344567772, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344567767, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "Advanced passenger information fee", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486157, "paymentMap": [{"key": "1344567772:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1344567774, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1344567767, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "Passengers Security & Safety Service Fees", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486156, "paymentMap": [{"key": "1344567774:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1344567776, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1344567767, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "Passenger Facilities Charge.", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486155, "paymentMap": [{"key": "1344567776:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1344567777, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344567767, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "YQ - DUMMY", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486159, "paymentMap": [{"key": "1344567777:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1344567778, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1344567767, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "Passenger Service Charge (Intl)", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486158, "paymentMap": [{"key": "1344567778:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1333486154, "codeType": "AIR", "amt": 215, "curr": "AED", "originalAmt": 215, "originalCurr": "AED", "status": 0, "billDate": "2025-05-05T23:35:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333486154:*********", "paymentID": *********, "amt": 215, "approveCode": 0}]}, {"chargeID": 1344567767, "codeType": "AIR", "amt": -215, "curr": "AED", "originalAmt": -215, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T14:40:40", "desc": "WEB:AIR", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486154, "paymentMap": [{"key": "1344567767:*********", "paymentID": *********, "amt": -215, "approveCode": 0}]}, {"chargeID": 1333486160, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1333486154, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1344567773, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1344567767, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486160, "paymentMap": []}, {"chargeID": 1333486166, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:35:20", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 16, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181055"}, {"chargeID": 1344567775, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1344567767, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:40:40", "desc": "Standard meal", "comment": "TEST PNR WHICH IS CREATED FOR PRODUCTION VALIDATION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333486166, "paymentMap": [], "PFID": "181055"}]}], "parentPNRs": [], "childPNRs": []}