/**
 * Test script for manual policy request generation
 * This script demonstrates how to generate manual policy creation requests
 * when Cover Genius API returns 404 errors.
 */

const { generateManualPolicyRequest } = require('./integrations');
const fs = require('fs');
const path = require('path');

// Sample PNR data structure (based on actual PNR format)
const samplePnrData = {
    "pnrNumber": "ABC123",
    "resCurrency": "AED",
    "segments": [
        {
            "org": "DXB",
            "dest": "SPX",
            "depDate": "2025-06-21T00:00:00"
        }
    ],
    "persons": [
        {
            "fName": "JOHN",
            "lName": "DOE",
            "title": "MR",
            "gender": "M"
        },
        {
            "fName": "JANE",
            "lName": "DOE", 
            "title": "MRS",
            "gender": "F"
        },
        {
            "fName": "CHILD",
            "lName": "DOE",
            "title": "MSTR",
            "gender": "M"
        }
    ],
    "contactInfos": [
        {
            "email": "<EMAIL>",
            "phone": "+971501234567"
        }
    ]
};

/**
 * Test the manual policy request generation
 */
async function testManualPolicyRequest() {
    console.log('🧪 Testing Manual Policy Request Generation');
    console.log('='.repeat(50));
    
    const testPnr = "ABC123";
    const testPolicyId = "TEST-POLICY-123-INS";
    
    try {
        console.log(`📋 Generating manual policy request for:`);
        console.log(`   PNR: ${testPnr}`);
        console.log(`   Policy ID: ${testPolicyId}`);
        console.log('');
        
        const manualRequest = generateManualPolicyRequest(testPnr, testPolicyId, samplePnrData);
        
        if (manualRequest) {
            console.log('✅ Manual policy request generated successfully!');
            console.log('');
            console.log('📄 Request Details:');
            console.log(`   URL: ${manualRequest.requestDetails.url}`);
            console.log(`   Method: ${manualRequest.requestDetails.method}`);
            console.log(`   Passengers: ${manualRequest.notes.extractedPassengers}`);
            console.log(`   Email: ${manualRequest.notes.extractedEmail}`);
            console.log(`   Origin: ${manualRequest.notes.firstSegmentOrigin}`);
            console.log(`   Country: ${manualRequest.notes.detectedCountry}`);
            console.log(`   Quote ID: ${manualRequest.notes.quoteId}`);
            console.log(`   Has Contact Info: ${manualRequest.notes.hasContactInfo}`);
            console.log('');
            
            console.log('🔧 Curl Command:');
            console.log('='.repeat(50));
            console.log(manualRequest.curlCommand);
            console.log('='.repeat(50));
            console.log('');
            
            console.log('📝 Instructions:');
            manualRequest.instructions.forEach((instruction, index) => {
                console.log(`   ${index + 1}. ${instruction}`);
            });
            console.log('');
            
            console.log('📊 Request Payload:');
            console.log(JSON.stringify(manualRequest.requestDetails.payload, null, 2));
            
        } else {
            console.log('❌ Failed to generate manual policy request');
        }
        
    } catch (error) {
        console.error('❌ Error during test:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

/**
 * Test with different scenarios
 */
async function testDifferentScenarios() {
    console.log('');
    console.log('🧪 Testing Different Scenarios');
    console.log('='.repeat(50));
    
    // Test with different origins and contact info
    const scenarios = [
        {
            name: "Dubai Origin (DXB)",
            pnrData: { 
                ...samplePnrData, 
                segments: [{ org: "DXB", dest: "SPX" }],
                contactInfos: [{ email: "<EMAIL>" }]
            },
            expectedCountry: "AE"
        },
        {
            name: "Cairo Origin (CAI)",
            pnrData: { 
                ...samplePnrData, 
                segments: [{ org: "CAI", dest: "DXB" }],
                contactInfos: [{ email: "<EMAIL>" }]
            },
            expectedCountry: "EG"
        },
        {
            name: "Doha Origin (DOH)",
            pnrData: { 
                ...samplePnrData, 
                segments: [{ org: "DOH", dest: "DXB" }],
                contactInfos: [{ email: "<EMAIL>" }]
            },
            expectedCountry: "QA"
        },
        {
            name: "No Contact Info",
            pnrData: { 
                ...samplePnrData, 
                segments: [{ org: "DXB", dest: "SPX" }],
                contactInfos: []
            },
            expectedCountry: "AE"
        },
        {
            name: "Unknown Origin",
            pnrData: { 
                ...samplePnrData, 
                segments: [{ org: "XYZ", dest: "DXB" }],
                contactInfos: [{ email: "<EMAIL>" }]
            },
            expectedCountry: "AE"
        }
    ];
    
    for (const scenario of scenarios) {
        console.log(`\n📋 Testing: ${scenario.name}`);
        const request = generateManualPolicyRequest("TEST123", "TEST-POL-456", scenario.pnrData);
        if (request) {
            console.log(`   ✓ Country detected: ${request.notes.detectedCountry} (expected: ${scenario.expectedCountry})`);
            console.log(`   ✓ Email: ${request.notes.extractedEmail}`);
            console.log(`   ✓ Origin: ${request.notes.firstSegmentOrigin}`);
            console.log(`   ✓ Has Contact Info: ${request.notes.hasContactInfo}`);
        } else {
            console.log(`   ❌ Failed to generate request`);
        }
    }
}

// Run the tests
if (require.main === module) {
    (async () => {
        await testManualPolicyRequest();
        await testDifferentScenarios();
        
        console.log('');
        console.log('🎉 Test completed!');
        console.log('Check the data/ directory for generated manual policy request files.');
    })();
}

module.exports = {
    testManualPolicyRequest,
    testDifferentScenarios
};
