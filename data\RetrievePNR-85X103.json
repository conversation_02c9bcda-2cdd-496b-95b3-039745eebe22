{"seriesNum": "299", "PNR": "85X103", "bookAgent": "WEB_MOBILE", "resCurrency": "QAR", "PNRPin": "81213108", "bookDate": "2025-03-15T17:40:09", "modifyDate": "2025-05-23T11:11:32", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "51cfdee91be84c80o3f8z84czbb5tc90p46b2a137a9b", "securityGUID": "51cfdee91be84c80o3f8z84czbb5tc90p46b2a137a9b", "lastLoadGUID": "bde07cbd-45b1-4bc1-80c3-cde877eb29c8", "isAsyncPNR": false, "MasterPNR": "85X103", "segments": [{"segKey": "16108726:16108726:8/7/2025 10:25:00 PM", "LFID": 16108726, "depDate": "2025-08-07T00:00:00", "flightGroupId": "16108726", "org": "DOH", "dest": "PEN", "depTime": "2025-08-07T22:25:00", "depTimeGMT": "2025-08-07T19:25:00", "arrTime": "2025-08-08T15:45:00", "operCarrier": "FZ", "operFlightNum": "020/1603", "mrktCarrier": "FZ ", "mrktFlightNum": "020/1603", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181016, "depDate": "2025-08-07T22:25:00", "legKey": "16108726:181016:8/7/2025 10:25:00 PM", "customerKey": "ABBD71A19419E981F83E22939232F1F0DA08FAAF9B6CAB561F09259AAFEAB3C2"}, {"PFID": 181746, "depDate": "2025-08-08T04:25:00", "legKey": "16108726:181746:8/8/2025 4:25:00 AM", "customerKey": "8EAB6C11EB7845C0E8DE50596361EBCE97313A2C8E711D99C3424D31B5826B27"}], "active": true, "changeType": "TK"}, {"segKey": "16108726:16108726:7/31/2025 10:25:00 PM", "LFID": 16108726, "depDate": "2025-07-31T00:00:00", "flightGroupId": "16108726", "org": "DOH", "dest": "PEN", "depTime": "2025-07-31T22:25:00", "depTimeGMT": "2025-07-31T19:25:00", "arrTime": "2025-08-01T15:45:00", "operCarrier": "FZ", "operFlightNum": "020/1603", "mrktCarrier": "FZ ", "mrktFlightNum": "020/1603", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181016, "depDate": "2025-07-31T22:25:00", "legKey": "16108726:181016:7/31/2025 10:25:00 PM", "customerKey": "BD6D336633CD311903903BA14805712209F30BD95A0D9F61FCDA31FB4DE4E031"}, {"PFID": 181746, "depDate": "2025-08-01T04:25:00", "legKey": "16108726:181746:8/1/2025 4:25:00 AM", "customerKey": "9D910073DD2FED6F32FFCC77D08B30879852FB6D17947FBDBC81C99C2DCF925D"}], "active": true, "changeType": "TK"}, {"segKey": "16145438:16145438:8/18/2025 10:05:00 PM", "LFID": 16145438, "depDate": "2025-08-18T00:00:00", "flightGroupId": "16145438", "org": "PEN", "dest": "DOH", "depTime": "2025-08-18T22:05:00", "depTimeGMT": "2025-08-18T14:05:00", "arrTime": "2025-08-19T21:25:00", "operCarrier": "FZ", "operFlightNum": "1604/019", "mrktCarrier": "FZ ", "mrktFlightNum": "1604/019", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181755, "depDate": "2025-08-18T22:05:00", "legKey": "16145438:181755:8/18/2025 10:05:00 PM", "customerKey": "37B8F65271D2593879CEA8ED44B5AB57F819AAE5AE344E9A4693FBB219D654B1"}, {"PFID": 181014, "depDate": "2025-08-19T21:15:00", "legKey": "16145438:181014:8/19/2025 9:15:00 PM", "customerKey": "56A81BD6FD569627677A037568E7D3C496C697D774A3930CC0F2D5965EDA285A"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 262659496, "fName": "SPENCER", "lName": "ELLMORE", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "O", "insuPurchasedate": "3/15/2025 5:35:24 PM", "provider": "AIG", "status": 0, "fareClass": "O", "operFareClass": "O", "FBC": "ORL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "984972733", "insuTransID": "f74e03b3-5a8f-41bc-93f7-0d87125227c7", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67d5b708000778000000f1d3#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-03-15T17:40:09"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "M", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JP964-WD2ZQ-INS/be47cd80-c8c1-4b50-a948-4eb3ecd17c08", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683056570007770000011c65#262659496#2#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-03-15T17:40:09"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "O", "status": 1, "fareClass": "O", "operFareClass": "O", "FBC": "ORL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "JP964-WD2ZQ-INS/be47cd80-c8c1-4b50-a948-4eb3ecd17c08", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683056570007770000011c65#262659496#1#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-23T11:07:42"}]}], "payments": [{"paymentID": *********, "paxID": 262659534, "method": "MSCD", "status": "1", "paidDate": "2025-03-15T17:40:33", "cardNum": "************3378", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 3553.5, "baseCurr": "QAR", "baseAmt": 3553.5, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "028499", "reference": "21810349", "externalReference": "21810349", "tranId": "20194567", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 20194567}, {"paymentID": *********, "paxID": 269919583, "method": "MSCD", "status": "1", "paidDate": "2025-05-23T11:11:27", "cardNum": "************0621", "gateway": "EPS", "paidCurr": "GBP", "paidAmt": 48.13, "baseCurr": "QAR", "baseAmt": 230.63, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "SPENCER ELLMORE", "authCode": "038057", "reference": "23203533", "externalReference": "23203533", "tranId": "21584345", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgmccecomgbpallniall001", "exchangeRate": "0.20868516", "resExternalPaymentID": 21584345}], "OAFlights": null, "physicalFlights": [{"key": "16108726:181016:2025-07-31T10:25:00 PM", "LFID": 16108726, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-07-31T22:25:00", "depTime": "2025-07-31T22:25:00", "arrTime": "2025-08-01T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "4/15/2025 12:51:07 PM"}, {"key": "16108726:181746:2025-08-01T04:25:00 AM", "LFID": 16108726, "PFID": 181746, "org": "DXB", "dest": "PEN", "depDate": "2025-08-01T04:25:00", "depTime": "2025-08-01T04:25:00", "arrTime": "2025-08-01T15:45:00", "carrier": "FZ", "flightNum": "1603", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1603", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "PEN", "operatingCarrier": "FZ", "flightDuration": 26400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Penang International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "4/15/2025 12:51:07 PM"}, {"key": "16108726:181016:2025-08-07T10:25:00 PM", "LFID": 16108726, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-08-07T22:25:00", "depTime": "2025-08-07T22:25:00", "arrTime": "2025-08-08T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "4/15/2025 12:51:07 PM"}, {"key": "16108726:181746:2025-08-08T04:25:00 AM", "LFID": 16108726, "PFID": 181746, "org": "DXB", "dest": "PEN", "depDate": "2025-08-08T04:25:00", "depTime": "2025-08-08T04:25:00", "arrTime": "2025-08-08T15:45:00", "carrier": "FZ", "flightNum": "1603", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1603", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "PEN", "operatingCarrier": "FZ", "flightDuration": 26400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Penang International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "4/15/2025 12:51:07 PM"}, {"key": "16145438:181755:2025-08-18T10:05:00 PM", "LFID": 16145438, "PFID": 181755, "org": "PEN", "dest": "DXB", "depDate": "2025-08-18T22:05:00", "depTime": "2025-08-18T22:05:00", "arrTime": "2025-08-19T01:10:00", "carrier": "FZ", "flightNum": "1604", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1604", "flightStatus": "OPEN", "originMetroGroup": "PEN", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 25500, "reaccomChangeAlert": false, "originName": "Penang International Airport", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "4/15/2025 12:44:26 PM"}, {"key": "16145438:181014:2025-08-19T09:15:00 PM", "LFID": 16145438, "PFID": 181014, "org": "DXB", "dest": "DOH", "depDate": "2025-08-19T21:15:00", "depTime": "2025-08-19T21:15:00", "arrTime": "2025-08-19T21:25:00", "carrier": "FZ", "flightNum": "019", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "019", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true, "changeType": "TK", "flightChangeTime": "4/15/2025 12:44:26 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-03-15T17:40:09", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-03-15T17:40:08"}, {"chargeID": **********, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1264984516, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984516:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1264984514, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984514:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1264984515, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984515:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1264984519, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984519:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1264984520, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984520:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1358738401, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984520, "paymentMap": [{"key": "1358738401:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1358738397, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984514, "paymentMap": [{"key": "1358738397:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1358738393, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1358738393:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1358738396, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984516, "paymentMap": [{"key": "1358738396:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1358738395, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1358738395:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1358738398, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984515, "paymentMap": [{"key": "1358738398:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1358738400, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984519, "paymentMap": [{"key": "1358738400:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1465, "curr": "QAR", "originalAmt": 1465, "originalCurr": "QAR", "status": 0, "billDate": "2025-03-15T17:40:09", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1465, "approveCode": 0}]}, {"chargeID": 1358738394, "codeType": "AIR", "amt": -1465, "curr": "QAR", "originalAmt": -1465, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-23T11:07:43", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1358738394:*********", "paymentID": *********, "amt": -1465, "approveCode": 0}]}, {"chargeID": 1264987750, "codeType": "PMNT", "amt": 103.5, "curr": "QAR", "originalAmt": 103.5, "originalCurr": "QAR", "status": 0, "billDate": "2025-03-15T17:40:37", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264987750:*********", "paymentID": *********, "amt": 103.5, "approveCode": 0}]}, {"chargeID": 1264984595, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181016", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984595:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "PFID": "181016"}, {"chargeID": 1264984596, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181746", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984596:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "PFID": "181746"}, {"chargeID": 1358738392, "codeType": "FRST", "amt": -70, "curr": "QAR", "originalAmt": -70, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "FRST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984595, "paymentMap": [{"key": "1358738392:*********", "paymentID": *********, "amt": -70, "approveCode": 0}], "PFID": "181016"}, {"chargeID": 1358738399, "codeType": "FRST", "amt": -70, "curr": "QAR", "originalAmt": -70, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-23T11:07:43", "desc": "FRST", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1264984596, "paymentMap": [{"key": "1358738399:*********", "paymentID": *********, "amt": -70, "approveCode": 0}], "PFID": "181746"}, {"chargeID": 1358738402, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-23T11:07:43", "desc": "Penalty AddedDueToModify FZ  020 DOH  - DXB  31-Jul-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738402:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1264984521, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1264984602, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181016"}, {"chargeID": 1264984601, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181746"}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 35, "curr": "QAR", "originalAmt": 35, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-03-15T17:40:09", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-03-15T17:40:08"}, {"chargeID": **********, "codeType": "INSU", "amt": 22.46, "curr": "QAR", "originalAmt": 22.46, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T11:07:44", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 22.46, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}"}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1264984590, "codeType": "TAX", "taxID": 11906, "taxCode": "G1", "taxChargeID": **********, "amt": 20, "curr": "QAR", "originalAmt": 20, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Departure <PERSON>.", "comment": "Departure <PERSON>.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984590:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1264984584, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984584:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1264984588, "codeType": "TAX", "taxID": 9731, "taxCode": "MY", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Passenger Service and Security Charge (International)", "comment": "Passenger Service and Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984588:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1264984589, "codeType": "TAX", "taxID": 11907, "taxCode": "H8", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Regulatory Service Charge - International", "comment": "Regulatory Service Charge - International", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984589:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1264984587, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984587:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1264984586, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984586:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 905, "curr": "QAR", "originalAmt": 905, "originalCurr": "QAR", "status": 1, "billDate": "2025-03-15T17:40:09", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 905, "approveCode": 0}]}, {"chargeID": 1264984599, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181755", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984599:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "PFID": "181755"}, {"chargeID": 1264984600, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181014", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1264984600:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "PFID": "181014"}, {"chargeID": 1264984591, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1264984603, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181014"}, {"chargeID": 1264984604, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-03-15T17:40:09", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181755"}]}, {"recNum": 3, "charges": [{"chargeID": 1358738668, "codeType": "INSU", "amt": 22.45, "curr": "QAR", "originalAmt": 22.45, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T11:07:44", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738668:*********", "paymentID": *********, "amt": 22.45, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-23T11:07:44"}, {"chargeID": 1358738426, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1358738423, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738426:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1358738428, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1358738423, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738428:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1358738425, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1358738423, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738425:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1358738427, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1358738423, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738427:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1358738429, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1358738423, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738429:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1358738424, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1358738423, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738424:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1358738430, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1358738423, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738430:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1358738423, "codeType": "AIR", "amt": 1495, "curr": "QAR", "originalAmt": 1495, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-23T11:07:44", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738423:*********", "paymentID": *********, "amt": 39, "approveCode": 0}, {"key": "1358738423:*********", "paymentID": *********, "amt": 1456, "approveCode": 0}]}, {"chargeID": 1358750557, "codeType": "PMNT", "amt": 6.72, "curr": "QAR", "originalAmt": 6.72, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-23T11:11:32", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358750557:*********", "paymentID": *********, "amt": 6.72, "approveCode": 0}]}, {"chargeID": 1358738692, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T11:07:44", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181746", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738692:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "PFID": "181746"}, {"chargeID": 1358738691, "codeType": "FRST", "amt": 70, "curr": "QAR", "originalAmt": 70, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-23T11:07:44", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181016", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1358738691:*********", "paymentID": *********, "amt": 70, "approveCode": 0}], "PFID": "181016"}, {"chargeID": 1358738431, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1358738423, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1358738438, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181016"}, {"chargeID": 1358738437, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-23T11:07:44", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181746"}]}], "parentPNRs": [], "childPNRs": []}