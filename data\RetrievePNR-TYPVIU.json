{"seriesNum": "299", "PNR": "TYPVIU", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82934166", "bookDate": "2025-05-18T22:36:24", "modifyDate": "2025-05-31T17:45:12", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "1142e2894f0f4dh77c287f45k7ibt2p0u7ubacdeaf61", "securityGUID": "1142e2894f0f4dh77c287f45k7ibt2p0u7ubacdeaf61", "lastLoadGUID": "bead96b0-7b45-44a0-97be-905bd9fd75b1", "isAsyncPNR": false, "MasterPNR": "TYPVIU", "segments": [{"segKey": "16087615:16087615:6/1/2025 9:35:00 AM", "LFID": 16087615, "depDate": "2025-06-01T00:00:00", "flightGroupId": "16087615", "org": "DXB", "dest": "JED", "depTime": "2025-06-01T09:35:00", "depTimeGMT": "2025-06-01T05:35:00", "arrTime": "2025-06-01T11:35:00", "operCarrier": "FZ", "operFlightNum": "909", "mrktCarrier": "FZ", "mrktFlightNum": "909", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181376, "depDate": "2025-06-01T09:35:00", "legKey": "16087615:181376:6/1/2025 9:35:00 AM", "customerKey": "8332F884FD710D65D56DD7228001D4A4835D20EBDA6214F47F1CF4E51CDB39D7"}], "active": true, "changeType": "TK"}, {"segKey": "16087555:16087555:6/1/2025 4:45:00 AM", "LFID": 16087555, "depDate": "2025-06-01T00:00:00", "flightGroupId": "16087555", "org": "DXB", "dest": "JED", "depTime": "2025-06-01T04:45:00", "depTimeGMT": "2025-06-01T00:45:00", "arrTime": "2025-06-01T06:50:00", "operCarrier": "FZ", "operFlightNum": "831", "mrktCarrier": "FZ ", "mrktFlightNum": "831", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181306, "depDate": "2025-06-01T04:45:00", "legKey": "16087555:181306:6/1/2025 4:45:00 AM", "customerKey": "F4B88DC67B945B5124508176334E8EDBEF33631ECEE1EF604F44714E138662A9"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 269398752, "fName": "MOHAMMED SALEH", "lName": "ALZAHRANI", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1983-12-24T00:00:00", "nationality": "682", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "H", "insuPurchasedate": "5/18/2025 10:36:25 PM", "provider": "<PERSON>", "status": 0, "fareClass": "H", "operFareClass": "H", "FBC": "HOB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "SJ97W-MJYUH-INS", "insuTransID": "SJ97W-MJYUH-INS/ac6d7743-3263-4b51-8670-182e7ce313d5", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682a5e4c0007770000011a6b#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-18T22:36:25"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "dhikra.ben", "statusReasonID": 0, "markFareClass": "L", "insuPurchasedate": "5/18/2025 10:36:25 PM", "provider": "<PERSON>", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LOB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "SJ97W-MJYUH-INS/ac6d7743-3263-4b51-8670-182e7ce313d5", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b3d4e00077800000159fe#269398752#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-31T17:34:30"}]}], "payments": [{"paymentID": *********, "paxID": 270803469, "method": "IPAY", "status": "1", "paidDate": "2025-05-31T17:45:09", "cardNum": "************4010", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 659.2, "baseCurr": "AED", "baseAmt": 659.2, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "066439", "reference": "23368159", "externalReference": "23368159", "tranId": "21751970", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21751970}, {"paymentID": *********, "paxID": 269398756, "method": "MADA", "status": "1", "paidDate": "2025-05-18T22:36:53", "gateway": "EPS", "paidCurr": "SAR", "paidAmt": 779.7, "baseCurr": "AED", "baseAmt": 747.47, "userID": "MOBILE_APP", "channelID": 12, "authCode": "A5390893", "reference": "A5390893", "externalReference": "A5390893", "tranId": "21488441", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "20266238", "exchangeRate": "1.04312333", "resExternalPaymentID": 21488441}], "OAFlights": null, "physicalFlights": [{"key": "16087555:181306:2025-06-01T04:45:00 AM", "LFID": 16087555, "PFID": 181306, "org": "DXB", "dest": "JED", "depDate": "2025-06-01T04:45:00", "depTime": "2025-06-01T04:45:00", "arrTime": "2025-06-01T06:50:00", "carrier": "FZ", "flightNum": "831", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "831", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "JED", "operatingCarrier": "FZ", "flightDuration": 11100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Jeddah", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:59:59 AM"}, {"key": "16087615:181376:2025-06-01T09:35:00 AM", "LFID": 16087615, "PFID": 181376, "org": "DXB", "dest": "JED", "depDate": "2025-06-01T09:35:00", "depTime": "2025-06-01T09:35:00", "arrTime": "2025-06-01T11:35:00", "carrier": "FZ", "flightNum": "909", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "909", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "JED", "operatingCarrier": "FZ", "flightDuration": 10800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Jeddah", "isActive": false, "changeType": "TK", "flightChangeTime": "1/31/2025 9:12:56 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1351790916, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790916:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-18T22:36:24"}, {"chargeID": 1370683579, "codeType": "INSU", "taxChargeID": 1370683577, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790916, "paymentMap": [{"key": "1370683579:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1351790910, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1351790906, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790910:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1351790907, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1351790906, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790907:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1351790909, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1351790906, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790909:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1351790912, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1351790906, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790912:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1351790908, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1351790906, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790908:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1351790911, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1351790906, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790911:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1370683580, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370683577, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790910, "paymentMap": [{"key": "1370683580:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370683595, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1370683577, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790909, "paymentMap": [{"key": "1370683595:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1370683603, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370683577, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790912, "paymentMap": [{"key": "1370683603:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1370683610, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370683577, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790908, "paymentMap": [{"key": "1370683610:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1370683624, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1370683577, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790907, "paymentMap": [{"key": "1370683624:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1351790906, "codeType": "AIR", "amt": 420, "curr": "AED", "originalAmt": 420, "originalCurr": "AED", "status": 0, "billDate": "2025-05-18T22:36:25", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790906:*********", "paymentID": *********, "amt": 420, "approveCode": 0}]}, {"chargeID": 1370683577, "codeType": "AIR", "amt": -420, "curr": "AED", "originalAmt": -420, "originalCurr": "AED", "status": 0, "billDate": "2025-05-31T17:34:30", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790906, "paymentMap": [{"key": "1370683577:*********", "paymentID": *********, "amt": -420, "approveCode": 0}]}, {"chargeID": 1351792312, "codeType": "PMNT", "amt": 21.77, "curr": "AED", "originalAmt": 21.77, "originalCurr": "AED", "status": 0, "billDate": "2025-05-18T22:36:57", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351792312:*********", "paymentID": *********, "amt": 21.77, "approveCode": 0}]}, {"chargeID": 1351790917, "codeType": "SPST", "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE2_WIN_AIS::181306", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1351790917:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181306"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 500, "curr": "AED", "originalAmt": 500, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "CancelNoRefund FZ 831 DXB - JED 01.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 500, "approveCode": 0}]}, {"chargeID": 1351790913, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1351790906, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370683588, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370683577, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790913, "paymentMap": []}, {"chargeID": 1351790918, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-18T22:36:25", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181306"}, {"chargeID": 1370683578, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370683577, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-31T17:34:31", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1351790918, "paymentMap": [], "PFID": "181306"}]}, {"recNum": 2, "charges": [{"chargeID": 1370683733, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:33:17", "billDate": "2025-05-31T17:34:31", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683733:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-31T17:34:31"}, {"chargeID": 1370683647, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1*********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683647:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1370683654, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1*********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683654:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1370683668, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1*********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683668:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370683676, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1*********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683676:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1370683683, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1*********, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "E3: Security Charges", "comment": "E3: Security Charges", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683683:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1370683697, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1*********, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683697:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "AIR", "amt": 430, "curr": "AED", "originalAmt": 430, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "FZ 909 DXB-JED 01Jun2025 Sun 09:35 11:35\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 165.7, "approveCode": 0}, {"key": "1*********:*********", "paymentID": *********, "amt": 264.3, "approveCode": 0}]}, {"chargeID": 1370693980, "codeType": "PMNT", "amt": 19.2, "curr": "AED", "originalAmt": 19.2, "originalCurr": "AED", "status": 1, "billDate": "2025-05-31T17:45:12", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370693980:*********", "paymentID": *********, "amt": 19.2, "approveCode": 0}]}, {"chargeID": 1370683731, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683731:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1370683732, "codeType": "FRST", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:33:14", "billDate": "2025-05-31T17:34:31", "desc": "Special Service Request:FRST-5A", "comment": "FLXID:FRST_Zone2_73H_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370683732:*********", "paymentID": *********, "amt": 60, "approveCode": 0}], "PFID": "181376"}, {"chargeID": 1370683705, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1*********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370683730, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1*********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T17:34:31", "billDate": "2025-05-31T17:34:31", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181376"}]}], "parentPNRs": [], "childPNRs": []}