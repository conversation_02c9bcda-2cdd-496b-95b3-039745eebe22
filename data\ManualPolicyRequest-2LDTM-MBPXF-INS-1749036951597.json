{"pnr": "H56RCK", "policyId": "2LDTM-MBPXF-INS", "timestamp": "2025-06-04T11:35:51.597Z", "requestDetails": {"url": "https://api.xcover.com/x/partners/ZDTIY/bookings/2LDTM-MBPXF-INS", "method": "POST", "headers": {"Content-Type": "application/json"}, "payload": {"quotes": [{"id": "2LDTM-MBPXF", "insured": [{"first_name": "HANOCH", "last_name": "HARAZI"}]}], "policyholder": {"first_name": "HANOCH", "last_name": "HARAZI", "email": "<EMAIL>", "country": "AE"}}}, "curlCommand": "curl --location 'https://api.xcover.com/x/partners/ZDTIY/bookings/2LDTM-MBPXF-INS' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n  \"quotes\": [\n    {\n      \"id\": \"2LDTM-MBPXF\",\n      \"insured\": [\n        {\n          \"first_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n          \"last_name\": \"HARAZ<PERSON>\"\n        }\n      ]\n    }\n  ],\n  \"policyholder\": {\n    \"first_name\": \"<PERSON><PERSON>OC<PERSON>\",\n    \"last_name\": \"HARAZI\",\n    \"email\": \"<EMAIL>\",\n    \"country\": \"AE\"\n  }\n}'", "instructions": ["1. Verify the email address in the policyholder section is correct", "2. Verify passenger names are correct", "3. Confirm the country code is appropriate", "4. Execute the curl command manually", "5. Save the response for record keeping"], "notes": {"extractedPassengers": 1, "extractedEmail": "<EMAIL>", "primaryPassengerId": 270379204, "firstSegmentOrigin": "DXB", "detectedCountry": "AE", "quoteId": "2LDTM-MBPXF", "hasContactInfo": true, "emailFromPrimaryPax": true}}