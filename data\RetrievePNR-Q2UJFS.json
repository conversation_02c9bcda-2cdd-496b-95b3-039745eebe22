{"seriesNum": "299", "PNR": "Q2UJFS", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82757245", "bookDate": "2025-05-12T12:46:26", "modifyDate": "2025-05-16T08:30:24", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "66bcb8a922404asbs786v8u8e5066771z6406b2bd933", "securityGUID": "66bcb8a922404asbs786v8u8e5066771z6406b2bd933", "lastLoadGUID": "353D50186223638BE0631E206F0ADC0F", "isAsyncPNR": false, "MasterPNR": "Q2UJFS", "segments": [{"segKey": "16087437:16087437:6/26/2025 5:55:00 AM", "LFID": 16087437, "depDate": "2025-06-26T00:00:00", "flightGroupId": "16087437", "org": "DXB", "dest": "EBB", "depTime": "2025-06-26T05:55:00", "depTimeGMT": "2025-06-26T01:55:00", "arrTime": "2025-06-26T10:25:00", "operCarrier": "FZ", "operFlightNum": "619", "mrktCarrier": "FZ", "mrktFlightNum": "619", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181198, "depDate": "2025-06-26T05:55:00", "legKey": "16087437:181198:6/26/2025 5:55:00 AM", "customerKey": "C3322EBC95176CBC29D333FBABA2A27915AFDD50A8D3075A84822D322B9EF8A9"}], "active": true, "changeType": "AC"}, {"segKey": "16087436:16087436:6/28/2025 11:25:00 AM", "LFID": 16087436, "depDate": "2025-06-28T00:00:00", "flightGroupId": "16087436", "org": "EBB", "dest": "DXB", "depTime": "2025-06-28T11:25:00", "depTimeGMT": "2025-06-28T08:25:00", "arrTime": "2025-06-28T18:10:00", "operCarrier": "FZ", "operFlightNum": "620", "mrktCarrier": "FZ", "mrktFlightNum": "620", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181197, "depDate": "2025-06-28T11:25:00", "legKey": "16087436:181197:6/28/2025 11:25:00 AM", "customerKey": "A9D2A1888779CB06E73BF813937C8A990AC58EE56CB8268DD095997A6AEF04B2"}], "active": true, "changeType": "AC"}, {"segKey": "16087437:16087437:5/15/2025 5:55:00 AM", "LFID": 16087437, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087437", "org": "DXB", "dest": "EBB", "depTime": "2025-05-15T05:55:00", "depTimeGMT": "2025-05-15T01:55:00", "arrTime": "2025-05-15T10:25:00", "operCarrier": "FZ", "operFlightNum": "619", "mrktCarrier": "FZ ", "mrktFlightNum": "619", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181198, "depDate": "2025-05-15T05:55:00", "legKey": "16087437:181198:5/15/2025 5:55:00 AM", "customerKey": "C2D2DC812F046798B9F2ACBB5B778764DE6EB182492772E967E0A05512805430"}], "active": true, "changeType": "AC"}, {"segKey": "16087436:16087436:5/17/2025 11:25:00 AM", "LFID": 16087436, "depDate": "2025-05-17T00:00:00", "flightGroupId": "16087436", "org": "EBB", "dest": "DXB", "depTime": "2025-05-17T11:25:00", "depTimeGMT": "2025-05-17T08:25:00", "arrTime": "2025-05-17T18:10:00", "operCarrier": "FZ", "operFlightNum": "620", "mrktCarrier": "FZ ", "mrktFlightNum": "620", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181197, "depDate": "2025-05-17T11:25:00", "legKey": "16087436:181197:5/17/2025 11:25:00 AM", "customerKey": "7A5B556C140C47616054F50A01D33F78BBF560A422D0F7EB7EA8F894F23681FE"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268710744, "fName": "JAMAL", "lName": "NASIR", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/12/2025 12:46:27 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "7R3ST-ADSSP-INS", "insuTransID": "7R3ST-ADSSP-INS/e414263b-2883-4a07-97f8-551419c00d4a", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821e7040007780000001dcd#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-12T12:46:26"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "M", "insuPurchasedate": "5/12/2025 12:46:27 PM", "provider": "<PERSON>", "status": 0, "fareClass": "M", "operFareClass": "M", "FBC": "MRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "7R3ST-ADSSP-INS", "insuTransID": "7R3ST-ADSSP-INS/e414263b-2883-4a07-97f8-551419c00d4a", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821e7040007780000001dcd#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-12T12:46:26"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "muhammed.v", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/12/2025 12:46:27 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "7R3ST-ADSSP-INS", "insuTransID": "7R3ST-ADSSP-INS/e414263b-2883-4a07-97f8-551419c00d4a", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6824a5ac000777000000377b#268710744#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-14T14:20:14"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "muhammed.v", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/12/2025 12:46:27 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRX8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "7R3ST-ADSSP-INS", "insuTransID": "7R3ST-ADSSP-INS/e414263b-2883-4a07-97f8-551419c00d4a", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6824a5ac000777000000377b#268710744#2#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-14T14:20:14"}]}], "payments": [{"paymentID": *********, "paxID": 268974439, "method": "MSCD", "status": "1", "paidDate": "2025-05-14T14:24:10", "cardNum": "************8115", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 310.03, "baseCurr": "AED", "baseAmt": 310.03, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "174542", "reference": "23030409", "externalReference": "23030409", "tranId": "21407653", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21407653}, {"paymentID": *********, "paxID": 268710839, "method": "MSCD", "status": "1", "paidDate": "2025-05-12T12:46:57", "cardNum": "************8115", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2543.79, "baseCurr": "AED", "baseAmt": 2543.79, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "885579", "reference": "22989933", "externalReference": "22989933", "tranId": "21361059", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21361059}], "OAFlights": null, "physicalFlights": [{"key": "16087437:181198:2025-05-15T05:55:00 AM", "LFID": 16087437, "PFID": 181198, "org": "DXB", "dest": "EBB", "depDate": "2025-05-15T05:55:00", "depTime": "2025-05-15T05:55:00", "arrTime": "2025-05-15T10:25:00", "carrier": "FZ", "flightNum": "619", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "619", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "EBB", "operatingCarrier": "FZ", "flightDuration": 19800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:04 AM"}, {"key": "16087436:181197:2025-05-17T11:25:00 AM", "LFID": 16087436, "PFID": 181197, "org": "EBB", "dest": "DXB", "depDate": "2025-05-17T11:25:00", "depTime": "2025-05-17T11:25:00", "arrTime": "2025-05-17T18:10:00", "carrier": "FZ", "flightNum": "620", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "620", "flightStatus": "CLOSED", "originMetroGroup": "EBB", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:54:11 AM"}, {"key": "16087437:181198:2025-06-26T05:55:00 AM", "LFID": 16087437, "PFID": 181198, "org": "DXB", "dest": "EBB", "depDate": "2025-06-26T05:55:00", "depTime": "2025-06-26T05:55:00", "arrTime": "2025-06-26T10:25:00", "carrier": "FZ", "flightNum": "619", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "619", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "EBB", "operatingCarrier": "FZ", "flightDuration": 19800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON><PERSON><PERSON>", "isActive": true, "changeType": "AC", "flightChangeTime": "3/26/2025 10:50:01 AM"}, {"key": "16087436:181197:2025-06-28T11:25:00 AM", "LFID": 16087436, "PFID": 181197, "org": "EBB", "dest": "DXB", "depDate": "2025-06-28T11:25:00", "depTime": "2025-06-28T11:25:00", "arrTime": "2025-06-28T18:10:00", "carrier": "FZ", "flightNum": "620", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "620", "flightStatus": "OPEN", "originMetroGroup": "EBB", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "<PERSON><PERSON><PERSON>", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/26/2025 10:50:28 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1346201399, "codeType": "INSU", "taxChargeID": 1346201392, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715412, "paymentMap": [{"key": "1346201399:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-14T14:20:15"}, {"chargeID": 1342715412, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715412:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1346201393, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346201392, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715396, "paymentMap": [{"key": "1346201393:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346201397, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346201392, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715399, "paymentMap": [{"key": "1346201397:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1346201400, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346201392, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715397, "paymentMap": [{"key": "1346201400:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1346201402, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346201392, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715401, "paymentMap": [{"key": "1346201402:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346201409, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346201392, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715400, "paymentMap": [{"key": "1346201409:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1342715396, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342715395, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715396:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342715401, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1342715395, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715401:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342715400, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342715395, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715400:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1342715397, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342715395, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715397:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1342715399, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1342715395, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715399:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1346201392, "codeType": "AIR", "amt": -429, "curr": "AED", "originalAmt": -429, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T14:20:15", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715395, "paymentMap": [{"key": "1346201392:*********", "paymentID": *********, "amt": -429, "approveCode": 0}]}, {"chargeID": 1342715395, "codeType": "AIR", "amt": 429, "curr": "AED", "originalAmt": 429, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T12:46:26", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715395:*********", "paymentID": *********, "amt": 429, "approveCode": 0}]}, {"chargeID": 1342721479, "codeType": "PMNT", "amt": 74.09, "curr": "AED", "originalAmt": 74.09, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T12:47:02", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342721479:*********", "paymentID": *********, "amt": 74.09, "approveCode": 0}]}, {"chargeID": 1346201396, "codeType": "XLGR", "taxChargeID": 1346201392, "amt": -177, "curr": "AED", "originalAmt": -177, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "XLGR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715413, "paymentMap": [{"key": "1346201396:*********", "paymentID": *********, "amt": -177, "approveCode": 0}], "PFID": "181198"}, {"chargeID": 1342715413, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181198", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715413:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181198"}, {"chargeID": 1346201398, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1346201392, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715402, "paymentMap": []}, {"chargeID": 1342715402, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1342715395, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201395, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1346201392, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "40kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715398, "paymentMap": []}, {"chargeID": 1342715398, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1342715395, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201401, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346201392, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:15", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715417, "paymentMap": [], "PFID": "181198"}, {"chargeID": 1342715417, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181198"}]}, {"recNum": 2, "charges": [{"chargeID": 1346201437, "codeType": "INSU", "taxChargeID": 1346201410, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715415, "paymentMap": [{"key": "1346201437:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-14T14:20:16"}, {"chargeID": 1342715415, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715415:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1346201450, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346201410, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715405, "paymentMap": [{"key": "1346201450:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346201434, "codeType": "TAX", "taxID": 7485, "taxCode": "UG", "taxChargeID": 1346201410, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "Security Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715409, "paymentMap": [{"key": "1346201434:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1346201435, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346201410, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715407, "paymentMap": [{"key": "1346201435:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1346201436, "codeType": "TAX", "taxID": 7484, "taxCode": "UL", "taxChargeID": 1346201410, "amt": -150, "curr": "AED", "originalAmt": -150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "Passenger Service Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715408, "paymentMap": [{"key": "1346201436:*********", "paymentID": *********, "amt": -150, "approveCode": 0}]}, {"chargeID": 1342715405, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342715404, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715405:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1342715408, "codeType": "TAX", "taxID": 7484, "taxCode": "UL", "taxChargeID": 1342715404, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715408:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1342715407, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342715404, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715407:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1342715409, "codeType": "TAX", "taxID": 7485, "taxCode": "UG", "taxChargeID": 1342715404, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715409:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1346201410, "codeType": "AIR", "amt": -755, "curr": "AED", "originalAmt": -755, "originalCurr": "AED", "status": 0, "billDate": "2025-05-14T14:20:15", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715404, "paymentMap": [{"key": "1346201410:*********", "paymentID": *********, "amt": -755, "approveCode": 0}]}, {"chargeID": 1342715404, "codeType": "AIR", "amt": 755, "curr": "AED", "originalAmt": 755, "originalCurr": "AED", "status": 0, "billDate": "2025-05-12T12:46:26", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715404:*********", "paymentID": *********, "amt": 755, "approveCode": 0}]}, {"chargeID": 1346201433, "codeType": "XLGR", "taxChargeID": 1346201410, "amt": -188, "curr": "AED", "originalAmt": -188, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "XLGR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715416, "paymentMap": [{"key": "1346201433:*********", "paymentID": *********, "amt": -188, "approveCode": 0}], "PFID": "181197"}, {"chargeID": 1342715416, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181197", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342715416:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "181197"}, {"chargeID": 1346201453, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "CancelNoRefund FZ 620 EBB - DXB 17.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201453:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1346201451, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1346201410, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715406, "paymentMap": []}, {"chargeID": 1342715406, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342715404, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201452, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346201410, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-14T14:20:16", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1342715418, "paymentMap": [], "PFID": "181197"}, {"chargeID": 1342715418, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-12T12:46:26", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181197"}]}, {"recNum": 3, "charges": [{"chargeID": 1346201466, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:16:32", "billDate": "2025-05-14T14:20:16", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201466:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T14:20:16"}, {"chargeID": 1346201455, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346201454, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201455:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1346201456, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346201454, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201456:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1346201457, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346201454, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201457:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346201458, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346201454, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201458:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346201459, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346201454, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201459:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1346201454, "codeType": "AIR", "amt": 439, "curr": "AED", "originalAmt": 439, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "FZ 619 DXB-EBB 26Jun2025 Thu 05:55 10:25\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201454:*********", "paymentID": *********, "amt": 439, "approveCode": 0}]}, {"chargeID": 1346207161, "codeType": "PMNT", "amt": 9.03, "curr": "AED", "originalAmt": 9.03, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T14:24:15", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346207161:*********", "paymentID": *********, "amt": 9.03, "approveCode": 0}]}, {"chargeID": 1346201464, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201464:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1346201465, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:16:22", "billDate": "2025-05-14T14:20:16", "desc": "Special Service Request:XLGR-6A", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201465:*********", "paymentID": *********, "amt": 188, "approveCode": 0}], "PFID": "181198"}, {"chargeID": 1346201463, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1346201454, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201461, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1346201454, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201462, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346201454, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181198"}]}, {"recNum": 4, "charges": [{"chargeID": 1346201477, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:16:32", "billDate": "2025-05-14T14:20:17", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201477:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-14T14:20:17"}, {"chargeID": 1346201468, "codeType": "TAX", "taxID": 7485, "taxCode": "UG", "taxChargeID": 1346201467, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "UG: Security Charge (International)", "comment": "UG: Security Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201468:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1346201469, "codeType": "TAX", "taxID": 7484, "taxCode": "UL", "taxChargeID": 1346201467, "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "UL: Passenger Service Charge (International)", "comment": "UL: Passenger Service Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201469:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1346201470, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346201467, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201470:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346201471, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346201467, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201471:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1346201467, "codeType": "AIR", "amt": 765, "curr": "AED", "originalAmt": 765, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "FZ 620 EBB-DXB 28Jun2025 Sat 11:25 18:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201467:*********", "paymentID": *********, "amt": 765, "approveCode": 0}]}, {"chargeID": 1346201475, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:17", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201475:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1346201476, "codeType": "XLGR", "amt": 188, "curr": "AED", "originalAmt": 188, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:16:27", "billDate": "2025-05-14T14:20:17", "desc": "Special Service Request:XLGR-6A", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346201476:*********", "paymentID": *********, "amt": 24.85, "approveCode": 0}, {"key": "1346201476:*********", "paymentID": *********, "amt": 163.15, "approveCode": 0}], "PFID": "181197"}, {"chargeID": 1346201474, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1346201467, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201472, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1346201467, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "BAGX: 40kg BAG INCLUDED IN FARE", "comment": "BAGX: 40kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346201473, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1346201467, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-14T14:20:16", "billDate": "2025-05-14T14:20:16", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181197"}]}], "parentPNRs": [], "childPNRs": []}