{"seriesNum": "299", "PNR": "9XE5RB", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82575844", "bookDate": "2025-05-06T04:56:40", "modifyDate": "2025-05-16T04:58:48", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "7b4a3gd7dcy9d0ida4uaub196ax9z145d367e4c6361c", "securityGUID": "7b4a3gd7dcy9d0ida4uaub196ax9z145d367e4c6361c", "lastLoadGUID": "7fadb0b9-5398-40ed-8994-c22f049db78e", "isAsyncPNR": false, "MasterPNR": "9XE5RB", "segments": [{"segKey": "16087557:16087557:5/19/2025 7:15:00 PM", "LFID": 16087557, "depDate": "2025-05-19T00:00:00", "flightGroupId": "16087557", "org": "DXB", "dest": "RUH", "depTime": "2025-05-19T19:15:00", "depTimeGMT": "2025-05-19T15:15:00", "arrTime": "2025-05-19T20:15:00", "operCarrier": "FZ", "operFlightNum": "855", "mrktCarrier": "FZ", "mrktFlightNum": "855", "persons": [{"recNum": 5, "status": 5}], "legDetails": [{"PFID": 181308, "depDate": "2025-05-19T19:15:00", "legKey": "16087557:181308:5/19/2025 7:15:00 PM", "customerKey": "1552DBFC3FCF70A36304160D935407F29911B05506B4DC097972CB92A1BEBE63"}], "active": true}, {"segKey": "16087594:16087594:5/21/2025 9:15:00 PM", "LFID": 16087594, "depDate": "2025-05-21T00:00:00", "flightGroupId": "16087594", "org": "RUH", "dest": "DXB", "depTime": "2025-05-21T21:15:00", "depTimeGMT": "2025-05-21T18:15:00", "arrTime": "2025-05-22T00:10:00", "operCarrier": "FZ", "operFlightNum": "856", "mrktCarrier": "FZ", "mrktFlightNum": "856", "persons": [{"recNum": 6, "status": 5}], "legDetails": [{"PFID": 181335, "depDate": "2025-05-21T21:15:00", "legKey": "16087594:181335:5/21/2025 9:15:00 PM", "customerKey": "CABD51D0D05C70E5575AD900982407DF9822250AF1739CEF4AD7E3C94BCD786E"}], "active": true, "changeType": "TK"}, {"segKey": "16087588:16087588:5/19/2025 7:50:00 AM", "LFID": 16087588, "depDate": "2025-05-19T00:00:00", "flightGroupId": "16087588", "org": "DXB", "dest": "RUH", "depTime": "2025-05-19T07:50:00", "depTimeGMT": "2025-05-19T03:50:00", "arrTime": "2025-05-19T08:50:00", "operCarrier": "FZ", "operFlightNum": "847", "mrktCarrier": "FZ", "mrktFlightNum": "847", "persons": [{"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181329, "depDate": "2025-05-19T07:50:00", "legKey": "16087588:181329:5/19/2025 7:50:00 AM", "customerKey": "451C0E11D66D435BBFEC35ADA89C2BA7712553B84A470C3679EF5D5712D3A03B"}], "active": true, "changeType": "AC"}, {"segKey": "16087523:16087523:5/20/2025 1:15:00 PM", "LFID": 16087523, "depDate": "2025-05-20T00:00:00", "flightGroupId": "16087523", "org": "RUH", "dest": "DXB", "depTime": "2025-05-20T13:15:00", "depTimeGMT": "2025-05-20T10:15:00", "arrTime": "2025-05-20T16:15:00", "operCarrier": "FZ", "operFlightNum": "842", "mrktCarrier": "FZ", "mrktFlightNum": "842", "persons": [{"recNum": 4, "status": 0}], "legDetails": [{"PFID": 181324, "depDate": "2025-05-20T13:15:00", "legKey": "16087523:181324:5/20/2025 1:15:00 PM", "customerKey": "A1FD4437A1CDFA53A335AC78A8558863C38B7A5CC9A0301388A48BF56F9B0B30"}], "active": true, "changeType": "AC"}, {"segKey": "16087588:16087588:5/12/2025 7:50:00 AM", "LFID": 16087588, "depDate": "2025-05-12T00:00:00", "flightGroupId": "16087588", "org": "DXB", "dest": "RUH", "depTime": "2025-05-12T07:50:00", "depTimeGMT": "2025-05-12T03:50:00", "arrTime": "2025-05-12T08:50:00", "operCarrier": "FZ", "operFlightNum": "847", "mrktCarrier": "FZ ", "mrktFlightNum": "847", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181329, "depDate": "2025-05-12T07:50:00", "legKey": "16087588:181329:5/12/2025 7:50:00 AM", "customerKey": "1069AD30C73B000DA42EAF5790B64AD66B8C0819A72661D203D45E0C4C40B9C3"}], "active": true, "changeType": "AC"}, {"segKey": "16087523:16087523:5/13/2025 1:15:00 PM", "LFID": 16087523, "depDate": "2025-05-13T00:00:00", "flightGroupId": "16087523", "org": "RUH", "dest": "DXB", "depTime": "2025-05-13T13:15:00", "depTimeGMT": "2025-05-13T10:15:00", "arrTime": "2025-05-13T16:15:00", "operCarrier": "FZ", "operFlightNum": "842", "mrktCarrier": "FZ ", "mrktFlightNum": "842", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181324, "depDate": "2025-05-13T13:15:00", "legKey": "16087523:181324:5/13/2025 1:15:00 PM", "customerKey": "1BC5F140EC03DEC00E371E35B30B49F7DE2C720CC074E0B7A112A4B3D9712FFE"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268013734, "fName": "DAVID GORDAN", "lName": "HAMMOND", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1983-12-22T00:00:00", "nationality": "826", "recNum": [1, 2, 3, 4, 5, 6]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "O", "insuPurchasedate": "5/6/2025 4:56:41 AM", "provider": "<PERSON>", "status": 0, "fareClass": "O", "operFareClass": "O", "FBC": "ORB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "KB7QW-AGN9Z-INS", "insuTransID": "KB7QW-AGN9Z-INS/e4a88508-976a-4a46-aec3-0811b873b101", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681994af00077800000047f2#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-06T04:56:40"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "L", "insuPurchasedate": "5/6/2025 4:56:41 AM", "provider": "<PERSON>", "status": 0, "fareClass": "L", "operFareClass": "L", "FBC": "LRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "KB7QW-AGN9Z-INS", "insuTransID": "KB7QW-AGN9Z-INS/e4a88508-976a-4a46-aec3-0811b873b101", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681994af00077800000047f2#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-06T04:56:40"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ammar.i", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "I", "insuPurchasedate": "5/6/2025 4:56:41 AM", "provider": "<PERSON>", "status": 0, "fareClass": "I", "operFareClass": "I", "FBC": "IRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "KB7QW-AGN9Z-INS/e4a88508-976a-4a46-aec3-0811b873b101", "toRecNum": 5, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681d97270007770000003778#268013734#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-09T05:52:18"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "ammar.i", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "L", "insuPurchasedate": "5/6/2025 4:56:41 AM", "provider": "<PERSON>", "status": 0, "fareClass": "L", "operFareClass": "L", "FBC": "LRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "KB7QW-AGN9Z-INS/e4a88508-976a-4a46-aec3-0811b873b101", "toRecNum": 6, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681d97270007770000003778#268013734#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-09T05:52:19"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "calvin.boorsma", "statusReasonID": 0, "markFareClass": "W", "insuPurchasedate": "5/6/2025 4:56:41 AM", "provider": "<PERSON>", "status": 5, "fareClass": "W", "operFareClass": "W", "FBC": "WRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "KB7QW-AGN9Z-INS/e4a88508-976a-4a46-aec3-0811b873b101", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6825ba610007770000007772#268013734#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-15T09:59:25"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "calvin.boorsma", "statusReasonID": 0, "markFareClass": "I", "insuPurchasedate": "5/6/2025 4:56:41 AM", "provider": "<PERSON>", "status": 5, "fareClass": "I", "operFareClass": "I", "FBC": "IRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "KB7QW-AGN9Z-INS/e4a88508-976a-4a46-aec3-0811b873b101", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6825ba610007770000007772#268013734#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-15T09:59:25"}]}], "payments": [{"paymentID": *********, "paxID": 268377704, "method": "VISA", "status": "1", "paidDate": "2025-05-09T06:16:58", "cardNum": "************6206", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1133, "baseCurr": "AED", "baseAmt": 1133, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "802346", "reference": "22927712", "externalReference": "22927712", "tranId": "21298693", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21298693}, {"paymentID": *********, "paxID": 269143321, "method": "VISA", "status": "1", "paidDate": "2025-05-16T04:58:40", "cardNum": "************6206", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2502.9, "baseCurr": "AED", "baseAmt": 2502.9, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "589071", "reference": "23063041", "externalReference": "23063041", "tranId": "21437990", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21437990}, {"paymentID": *********, "paxID": 268013758, "method": "VISA", "status": "1", "paidDate": "2025-05-06T04:57:15", "cardNum": "************6206", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2318.22, "baseCurr": "AED", "baseAmt": 2318.22, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "469466", "reference": "22864577", "externalReference": "22864577", "tranId": "21233443", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21233443}], "OAFlights": null, "physicalFlights": [{"key": "16087588:181329:2025-05-12T07:50:00 AM", "LFID": 16087588, "PFID": 181329, "org": "DXB", "dest": "RUH", "depDate": "2025-05-12T07:50:00", "depTime": "2025-05-12T07:50:00", "arrTime": "2025-05-12T08:50:00", "carrier": "FZ", "flightNum": "847", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "847", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "RUH", "operatingCarrier": "FZ", "flightDuration": 7200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Riyadh", "isActive": false, "changeType": "AC", "flightChangeTime": "1/31/2025 8:39:54 AM"}, {"key": "16087523:181324:2025-05-13T01:15:00 PM", "LFID": 16087523, "PFID": 181324, "org": "RUH", "dest": "DXB", "depDate": "2025-05-13T13:15:00", "depTime": "2025-05-13T13:15:00", "arrTime": "2025-05-13T16:15:00", "carrier": "FZ", "flightNum": "842", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "842", "flightStatus": "CLOSED", "originMetroGroup": "RUH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 7200, "reaccomChangeAlert": false, "originName": "Riyadh", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "4/29/2025 9:03:12 AM"}, {"key": "16087588:181329:2025-05-19T07:50:00 AM", "LFID": 16087588, "PFID": 181329, "org": "DXB", "dest": "RUH", "depDate": "2025-05-19T07:50:00", "depTime": "2025-05-19T07:50:00", "arrTime": "2025-05-19T08:50:00", "carrier": "FZ", "flightNum": "847", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "847", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "RUH", "operatingCarrier": "FZ", "flightDuration": 7200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Riyadh", "isActive": false, "changeType": "AC", "flightChangeTime": "1/31/2025 8:39:55 AM"}, {"key": "16087557:181308:2025-05-19T07:15:00 PM", "LFID": 16087557, "PFID": 181308, "org": "DXB", "dest": "RUH", "depDate": "2025-05-19T19:15:00", "depTime": "2025-05-19T19:15:00", "arrTime": "2025-05-19T20:15:00", "carrier": "FZ", "flightNum": "855", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "855", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "RUH", "operatingCarrier": "FZ", "flightDuration": 7200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Riyadh", "isActive": false, "flightChangeTime": "12/25/2024 10:06:36 AM"}, {"key": "16087523:181324:2025-05-20T01:15:00 PM", "LFID": 16087523, "PFID": 181324, "org": "RUH", "dest": "DXB", "depDate": "2025-05-20T13:15:00", "depTime": "2025-05-20T13:15:00", "arrTime": "2025-05-20T16:15:00", "carrier": "FZ", "flightNum": "842", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "842", "flightStatus": "CLOSED", "originMetroGroup": "RUH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 7200, "reaccomChangeAlert": false, "originName": "Riyadh", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "4/15/2025 11:11:03 AM"}, {"key": "16087594:181335:2025-05-21T09:15:00 PM", "LFID": 16087594, "PFID": 181335, "org": "RUH", "dest": "DXB", "depDate": "2025-05-21T21:15:00", "depTime": "2025-05-21T21:15:00", "arrTime": "2025-05-22T00:10:00", "carrier": "FZ", "flightNum": "856", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73N", "mrktCarrier": "FZ", "mrktFlightNum": "856", "flightStatus": "CLOSED", "originMetroGroup": "RUH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 6900, "reaccomChangeAlert": false, "originName": "Riyadh", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "4/15/2025 12:46:00 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1338329401, "codeType": "INSU", "taxChargeID": 1338329396, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "INSU", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584856, "paymentMap": [{"key": "1338329401:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-09T05:52:20"}, {"chargeID": 1333584856, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584856:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1338329399, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338329396, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:19", "desc": "Passengers Security & Safety Service Fees", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584825, "paymentMap": [{"key": "1338329399:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1338329402, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338329396, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "Advanced passenger information fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584820, "paymentMap": [{"key": "1338329402:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1338329403, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338329396, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "YQ - DUMMY", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584821, "paymentMap": [{"key": "1338329403:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1338329404, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338329396, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "Passenger Service Charge (Intl)", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584822, "paymentMap": [{"key": "1338329404:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1338329405, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338329396, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "Passenger Facilities Charge.", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584824, "paymentMap": [{"key": "1338329405:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1333584824, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1333584818, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584824:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1333584822, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1333584818, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584822:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1333584821, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333584818, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584821:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1333584820, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333584818, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584820:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333584823, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1333584818, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584823:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1333584825, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1333584818, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584825:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338329396, "codeType": "AIR", "amt": -1535, "curr": "AED", "originalAmt": -1535, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T05:52:19", "desc": "WEB:AIR", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584818, "paymentMap": [{"key": "1338329396:*********", "paymentID": *********, "amt": -1535, "approveCode": 0}]}, {"chargeID": 1333584818, "codeType": "AIR", "amt": 1535, "curr": "AED", "originalAmt": 1535, "originalCurr": "AED", "status": 0, "billDate": "2025-05-06T04:56:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584818:*********", "paymentID": *********, "amt": 1535, "approveCode": 0}]}, {"chargeID": 1333586352, "codeType": "PMNT", "amt": 67.52, "curr": "AED", "originalAmt": 67.52, "originalCurr": "AED", "status": 0, "billDate": "2025-05-06T04:57:19", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333586352:*********", "paymentID": *********, "amt": 67.52, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "CancelNoRefund FZ 847 DXB - RUH 12.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1338329400, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1338329396, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:19", "desc": "20kg BAG INCLUDED IN FARE", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584819, "paymentMap": []}, {"chargeID": 1333584819, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1333584818, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338329398, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1338329396, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:19", "desc": "Standard meal", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584859, "paymentMap": [], "PFID": "181329"}, {"chargeID": 1333584859, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181329"}]}, {"recNum": 2, "charges": [{"chargeID": 1338329453, "codeType": "INSU", "taxChargeID": 1338329407, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "INSU", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584858, "paymentMap": [{"key": "1338329453:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-09T05:52:20"}, {"chargeID": 1333584858, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584858:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1338329449, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338329407, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "YQ - DUMMY", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584851, "paymentMap": [{"key": "1338329449:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1338329451, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338329407, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "Advanced passenger information fee", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584849, "paymentMap": [{"key": "1338329451:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1338329452, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": 1338329407, "amt": -130, "curr": "AED", "originalAmt": -130, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "International Airport Building Charge", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584850, "paymentMap": [{"key": "1338329452:*********", "paymentID": *********, "amt": -130, "approveCode": 0}]}, {"chargeID": 1338329454, "codeType": "TAX", "taxID": 13453, "taxCode": "T2", "taxChargeID": 1338329407, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "GACA Services Charge (International)", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584853, "paymentMap": [{"key": "1338329454:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1333584850, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": 1333584827, "amt": 130, "curr": "AED", "originalAmt": 130, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "International Airport Building Charge", "comment": "International Airport Building Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584850:*********", "paymentID": *********, "amt": 130, "approveCode": 0}]}, {"chargeID": 1333584849, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333584827, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584849:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1333584853, "codeType": "TAX", "taxID": 13453, "taxCode": "T2", "taxChargeID": 1333584827, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "GACA Services Charge (International)", "comment": "GACA Services Charge (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584853:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1333584852, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1333584827, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584852:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1333584851, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333584827, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584851:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1338329407, "codeType": "AIR", "amt": -225, "curr": "AED", "originalAmt": -225, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T05:52:20", "desc": "WEB:AIR", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584827, "paymentMap": [{"key": "1338329407:*********", "paymentID": *********, "amt": -225, "approveCode": 0}]}, {"chargeID": 1333584827, "codeType": "AIR", "amt": 225, "curr": "AED", "originalAmt": 225, "originalCurr": "AED", "status": 0, "billDate": "2025-05-06T04:56:40", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333584827:*********", "paymentID": *********, "amt": 225, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "CancelNoRefund FZ 842 RUH - DXB 13.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1338329408, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1338329407, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "20kg BAG INCLUDED IN FARE", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584828, "paymentMap": []}, {"chargeID": 1333584828, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1333584827, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338329450, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1338329407, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-09T05:52:20", "desc": "Standard meal", "comment": "XX No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333584860, "paymentMap": [], "PFID": "181324"}, {"chargeID": 1333584860, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-06T04:56:40", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181324"}]}, {"recNum": 3, "charges": [{"chargeID": 1338329475, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:48:27", "billDate": "2025-05-09T05:52:21", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329475:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-09T05:52:21"}, {"chargeID": 1347335448, "codeType": "INSU", "taxChargeID": 1347335443, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:48:27", "billDate": "2025-05-15T09:59:27", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329475, "paymentMap": [{"key": "1347335448:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1347335445, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347335443, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-15T09:59:26", "desc": "AE: Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329463, "paymentMap": [{"key": "1347335445:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1347335446, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347335443, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-15T09:59:26", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329459, "paymentMap": [{"key": "1347335446:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1347335447, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347335443, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-15T09:59:27", "desc": "F6: Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329458, "paymentMap": [{"key": "1347335447:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1338329462, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1338329457, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "E3: Security Charges", "comment": "E3: Security Charges", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329462:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338329463, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338329457, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329463:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1338329460, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338329457, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329460:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338329466, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338329457, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:21", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329466:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1338329458, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338329457, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329458:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1338329459, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338329457, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329459:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1347335472, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347335443, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-15T09:59:27", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329466, "paymentMap": [{"key": "1347335472:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1347335473, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347335443, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-15T09:59:27", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329460, "paymentMap": [{"key": "1347335473:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1347335443, "codeType": "AIR", "amt": -2185, "curr": "AED", "originalAmt": -2185, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-15T09:59:26", "desc": "FZ 847 DXB-RUH 19May2025 Mon 07:50 08:50\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329457, "paymentMap": [{"key": "1347335443:*********", "paymentID": *********, "amt": -1930.7, "approveCode": 0}, {"key": "1347335443:*********", "paymentID": *********, "amt": -254.3, "approveCode": 0}]}, {"chargeID": 1338329457, "codeType": "AIR", "amt": 2185, "curr": "AED", "originalAmt": 2185, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:20", "billDate": "2025-05-09T05:52:20", "desc": "FZ 847 DXB-RUH 19May2025 Mon 07:50 08:50\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329457:*********", "paymentID": *********, "amt": 1930.7, "approveCode": 0}, {"key": "1338329457:*********", "paymentID": *********, "amt": 254.3, "approveCode": 0}]}, {"chargeID": 1338359022, "codeType": "PMNT", "amt": 33, "curr": "AED", "originalAmt": 33, "originalCurr": "AED", "status": 0, "billDate": "2025-05-09T06:17:03", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338359022:*********", "paymentID": *********, "amt": 33, "approveCode": 0}]}, {"chargeID": 1338329473, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329473:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:27", "billDate": "2025-05-15T09:59:27", "desc": "CancelNoRefund FZ 847 DXB - RUH 19.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1338329469, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1338329457, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1347335470, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1347335443, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329469, "paymentMap": []}, {"chargeID": 1338329471, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1338329457, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181329"}, {"chargeID": 1347335469, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1347335443, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329471, "paymentMap": [], "PFID": "181329"}]}, {"recNum": 4, "charges": [{"chargeID": 1338329494, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:48:27", "billDate": "2025-05-09T05:52:21", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329494:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-09T05:52:21"}, {"chargeID": 1347335480, "codeType": "INSU", "taxChargeID": 1347335475, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:48:27", "billDate": "2025-05-15T09:59:27", "desc": "Special Service Request", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329494, "paymentMap": [{"key": "1347335480:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1338329479, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338329477, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329479:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1338329481, "codeType": "TAX", "taxID": 13453, "taxCode": "T2", "taxChargeID": 1338329477, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "T2: GACA Services Charge (International)", "comment": "T2: GACA Services Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329481:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338329483, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": 1338329477, "amt": 130, "curr": "AED", "originalAmt": 130, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "IO: International Airport Building Charge", "comment": "IO: International Airport Building Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329483:*********", "paymentID": *********, "amt": 130, "approveCode": 0}]}, {"chargeID": 1338329484, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1338329477, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "E3: Security Charges", "comment": "E3: Security Charges", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329484:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1338329486, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338329477, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329486:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1347335479, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347335475, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329486, "paymentMap": [{"key": "1347335479:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1347335482, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347335475, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329479, "paymentMap": [{"key": "1347335482:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1347335477, "codeType": "TAX", "taxID": 13453, "taxCode": "T2", "taxChargeID": 1347335475, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "T2: GACA Services Charge (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329481, "paymentMap": [{"key": "1347335477:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1347335478, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": 1347335475, "amt": -130, "curr": "AED", "originalAmt": -130, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "IO: International Airport Building Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329483, "paymentMap": [{"key": "1347335478:*********", "paymentID": *********, "amt": -130, "approveCode": 0}]}, {"chargeID": 1338329477, "codeType": "AIR", "amt": 235, "curr": "AED", "originalAmt": 235, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "FZ 842 RUH-DXB 20May2025 Tue 13:15 16:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329477:*********", "paymentID": *********, "amt": 235, "approveCode": 0}]}, {"chargeID": 1347335475, "codeType": "AIR", "amt": -235, "curr": "AED", "originalAmt": -235, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "FZ 842 RUH-DXB 20May2025 Tue 13:15 16:15\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329477, "paymentMap": [{"key": "1347335475:*********", "paymentID": *********, "amt": -235, "approveCode": 0}]}, {"chargeID": 1338329492, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338329492:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "CancelNoRefund FZ 842 RUH - DXB 20.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1338329488, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1338329477, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1347335483, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1347335475, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:28", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329488, "paymentMap": []}, {"chargeID": 1338329490, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1338329477, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-09T05:52:21", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181324"}, {"chargeID": 1347335481, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1347335475, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-09T05:52:21", "billDate": "2025-05-15T09:59:27", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338329490, "paymentMap": [], "PFID": "181324"}]}, {"recNum": 5, "charges": [{"chargeID": 1347335509, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:56:54", "billDate": "2025-05-15T09:59:28", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335509:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-15T09:59:28"}, {"chargeID": 1347335486, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347335485, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335486:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1347335487, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347335485, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335487:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1347335488, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347335485, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335488:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1347335495, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1347335485, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "E3: Security Charges", "comment": "E3: Security Charges", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335495:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347335503, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347335485, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335503:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347335505, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347335485, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335505:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1347335485, "codeType": "AIR", "amt": 2195, "curr": "AED", "originalAmt": 2195, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "FZ 855 DXB-RUH 19May2025 Mon 19:15 20:15\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335485:*********", "paymentID": *********, "amt": 1780.7, "approveCode": 0}, {"key": "1347335485:*********", "paymentID": *********, "amt": 414.3, "approveCode": 0}]}, {"chargeID": 1348423893, "codeType": "PMNT", "amt": 72.9, "curr": "AED", "originalAmt": 72.9, "originalCurr": "AED", "status": 1, "billDate": "2025-05-16T04:58:48", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1348423893:*********", "paymentID": *********, "amt": 72.9, "approveCode": 0}]}, {"chargeID": 1347335508, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335508:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1347335506, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1347335485, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1347335507, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1347335485, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181308"}]}, {"recNum": 6, "charges": [{"chargeID": 1347335519, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:56:54", "billDate": "2025-05-15T09:59:29", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335519:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-15T09:59:29"}, {"chargeID": 1347335512, "codeType": "TAX", "taxID": 13453, "taxCode": "T2", "taxChargeID": 1347335510, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "T2: GACA Services Charge (International)", "comment": "T2: GACA Services Charge (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335512:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347335513, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": 1347335510, "amt": 130, "curr": "AED", "originalAmt": 130, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "IO: International Airport Building Charge", "comment": "IO: International Airport Building Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335513:*********", "paymentID": *********, "amt": 130, "approveCode": 0}]}, {"chargeID": 1347335514, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1347335510, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:29", "desc": "E3: Security Charges", "comment": "E3: Security Charges", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335514:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347335515, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347335510, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:29", "billDate": "2025-05-15T09:59:29", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335515:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1347335511, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347335510, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335511:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1347335510, "codeType": "AIR", "amt": 2215, "curr": "AED", "originalAmt": 2215, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:28", "billDate": "2025-05-15T09:59:28", "desc": "FZ 856 RUH-DXB 21May2025 Wed 21:15 00:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335510:*********", "paymentID": *********, "amt": 157.85, "approveCode": 0}, {"key": "1347335510:*********", "paymentID": *********, "amt": 2057.15, "approveCode": 0}]}, {"chargeID": 1347335518, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:29", "billDate": "2025-05-15T09:59:29", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347335518:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1347335516, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1347335510, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:29", "billDate": "2025-05-15T09:59:29", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1347335517, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1347335510, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-15T09:59:29", "billDate": "2025-05-15T09:59:29", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181335"}]}], "parentPNRs": [], "childPNRs": []}