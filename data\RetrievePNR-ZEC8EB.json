{"seriesNum": "299", "PNR": "ZEC8EB", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "83189970", "bookDate": "2025-05-27T23:47:37", "modifyDate": "2025-05-29T13:03:40", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "a59cdb2732q7n1542c45ucbbt9l9zbuc84266dcc9989", "securityGUID": "a59cdb2732q7n1542c45ucbbt9l9zbuc84266dcc9989", "lastLoadGUID": "3646D6321024434FE0630A57380A92DC", "isAsyncPNR": false, "MasterPNR": "ZEC8EB", "segments": [{"segKey": "16087487:16087487:5/29/2025 12:10:00 PM", "LFID": 16087487, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16087487", "org": "DXB", "dest": "TBS", "depTime": "2025-05-29T12:10:00", "depTimeGMT": "2025-05-29T08:10:00", "arrTime": "2025-05-29T15:35:00", "operCarrier": "FZ", "operFlightNum": "711", "mrktCarrier": "FZ ", "mrktFlightNum": "711", "persons": [{"recNum": 2, "status": 0}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181218, "depDate": "2025-05-29T12:10:00", "legKey": "16087487:181218:5/29/2025 12:10:00 PM", "customerKey": "CC24579673E3648C08A07BC064C5BD04FE8F174B1AEB5597CA6C875C1FD9C595"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270379562, "fName": "HAMAD", "lName": "ALNUAIMI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [2]}, {"paxID": 270379563, "fName": "ABDULLA", "lName": "ALNUAIMI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "AutoCancel", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 11:47:37 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KOB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "KKGWE-FVBAA-INS/4a380192-d3c5-47e6-bd1d-e46fd2c607ae", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68364e2a0007780000000aac#2#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 10, "bookDate": "2025-05-27T23:47:37"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "AutoCancel", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/27/2025 11:47:37 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KOB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "KKGWE-FVBAA-INS/4a380192-d3c5-47e6-bd1d-e46fd2c607ae", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68364e2a0007780000000aac#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 10, "bookDate": "2025-05-27T23:47:37"}]}], "payments": [{"paymentID": *********, "paxID": 270379658, "method": "IPAY", "status": "1", "paidDate": "2025-05-29T13:02:44", "cardNum": "************8897", "gateway": "EPS", "paidCurr": "AED", "paidAmt": -1575.28, "baseCurr": "AED", "baseAmt": -1575.28, "userID": "fzdb", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "850724", "reference": "23291394", "externalReference": "23291394", "tranId": "21671643", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": "1", "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671643}, {"paymentID": 210542834, "paxID": 270379714, "method": "IPAY", "status": "1", "paidDate": "2025-05-28T08:20:30", "cardNum": "************8897", "gateway": "EPS", "paidCurr": "AED", "paidAmt": -1575.28, "baseCurr": "AED", "baseAmt": -1575.28, "userID": "fzdb", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "850364", "reference": "23291401", "externalReference": "23291401", "tranId": "21671643", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": "1", "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671643}, {"paymentID": *********, "paxID": 270379714, "method": "IPAY", "status": "1", "paidDate": "2025-05-27T23:54:06", "cardNum": "************8897", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1575.28, "baseCurr": "AED", "baseAmt": 1575.28, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "850364", "reference": "23291401", "externalReference": "23291401", "tranId": "21671643", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671643}, {"paymentID": 210510318, "paxID": 270379660, "method": "IPAY", "status": "2", "paidDate": "2025-05-27T23:49:55", "cardNum": "************8897", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1575.28, "baseCurr": "AED", "baseAmt": 1575.28, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "reference": "23291401", "externalReference": "23291401", "tranId": "21671643", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671643}, {"paymentID": *********, "paxID": 270379658, "method": "IPAY", "status": "1", "paidDate": "2025-05-27T23:51:52", "cardNum": "************8897", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1575.28, "baseCurr": "AED", "baseAmt": 1575.28, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "850724", "reference": "23291394", "externalReference": "23291394", "tranId": "21671643", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21671643}], "OAFlights": null, "physicalFlights": [{"key": "16087487:181218:2025-05-29T12:10:00 PM", "LFID": 16087487, "PFID": 181218, "org": "DXB", "dest": "TBS", "depDate": "2025-05-29T12:10:00", "depTime": "2025-05-29T12:10:00", "arrTime": "2025-05-29T15:35:00", "carrier": "FZ", "flightNum": "711", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "711", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "4/25/2025 12:37:23 PM"}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1364930428, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930428:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-27T23:47:36"}, {"chargeID": 1367550562, "codeType": "INSU", "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "INSU manual cancel - radixx manual cancel - radixx", "comment": "INSU manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324504, "paymentMap": [{"key": "1367550562:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}]}, {"chargeID": 1365324504, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "INSU manual cancel - radixx", "comment": "INSU", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324504:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}]}, {"chargeID": 1364950294, "codeType": "INSU", "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "INSU", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930428, "paymentMap": [{"key": "1364950294:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}]}, {"chargeID": 1364930415, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364930411, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930415:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364930413, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364930411, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930413:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364930416, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364930411, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930416:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1364930414, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364930411, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930414:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364930412, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364930411, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930412:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1367550566, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Passenger Service Charge (Intl) manual cancel - radixx manual cancel - radixx", "comment": "Passenger Service Charge (Intl) manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324509, "paymentMap": [{"key": "1367550566:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1367550558, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Advanced passenger information fee manual cancel - radixx manual cancel - radixx", "comment": "Advanced passenger information fee manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324500, "paymentMap": [{"key": "1367550558:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1367550559, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Passenger Facilities Charge. manual cancel - radixx manual cancel - radixx", "comment": "Passenger Facilities Charge. manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324501, "paymentMap": [{"key": "1367550559:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1367550561, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "YQ - DUMMY manual cancel - radixx manual cancel - radixx", "comment": "YQ - DUMMY manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324503, "paymentMap": [{"key": "1367550561:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1367550565, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Passengers Security & Safety Service Fees manual cancel - radixx manual cancel - radixx", "comment": "Passengers Security & Safety Service Fees manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324508, "paymentMap": [{"key": "1367550565:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1365324500, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Advanced passenger information fee manual cancel - radixx", "comment": "Advanced passenger information fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324500:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1365324501, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Passenger Facilities Charge. manual cancel - radixx", "comment": "Passenger Facilities Charge.", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324501:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1365324503, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "YQ - DUMMY manual cancel - radixx", "comment": "YQ - DUMMY", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324503:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1365324508, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Passengers Security & Safety Service Fees manual cancel - radixx", "comment": "Passengers Security & Safety Service Fees", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324508:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1365324509, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Passenger Service Charge (Intl) manual cancel - radixx", "comment": "Passenger Service Charge (Intl)", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324509:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364950290, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930415, "paymentMap": [{"key": "1364950290:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364950291, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930413, "paymentMap": [{"key": "1364950291:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1364950293, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930416, "paymentMap": [{"key": "1364950293:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1364950297, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930414, "paymentMap": [{"key": "1364950297:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364950298, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930412, "paymentMap": [{"key": "1364950298:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1364930411, "codeType": "AIR", "amt": 409, "curr": "AED", "originalAmt": 409, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:47:37", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930411:*********", "paymentID": *********, "amt": 409, "approveCode": 0}]}, {"chargeID": 1367550560, "codeType": "AIR", "amt": -409, "curr": "AED", "originalAmt": -409, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "WEB:AIR manual cancel - radixx manual cancel - radixx", "comment": "WEB:AIR manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324502, "paymentMap": [{"key": "1367550560:*********", "paymentID": *********, "amt": -409, "approveCode": 0}]}, {"chargeID": 1365324502, "codeType": "AIR", "amt": 409, "curr": "AED", "originalAmt": 409, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "WEB:AIR manual cancel - radixx", "comment": "WEB:AIR", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324502:*********", "paymentID": *********, "amt": 409, "approveCode": 0}]}, {"chargeID": 1364950292, "codeType": "AIR", "amt": -409, "curr": "AED", "originalAmt": -409, "originalCurr": "AED", "status": 0, "billDate": "2025-05-28T00:59:17", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930411, "paymentMap": [{"key": "1364950292:*********", "paymentID": *********, "amt": -409, "approveCode": 0}]}, {"chargeID": 1367550557, "codeType": "PMNT", "amt": -45.88, "curr": "AED", "originalAmt": -45.88, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Payment Fee manual cancel - radixx", "comment": "Payment Fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364932436, "paymentMap": [{"key": "1367550557:*********", "paymentID": *********, "amt": -45.88, "approveCode": 0}]}, {"chargeID": 1364933213, "codeType": "PMNT", "amt": 45.88, "curr": "AED", "originalAmt": 45.88, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:54:08", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364933213:*********", "paymentID": *********, "amt": 45.88, "approveCode": 0}]}, {"chargeID": 1364932436, "codeType": "PMNT", "amt": 45.88, "curr": "AED", "originalAmt": 45.88, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:51:53", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364932436:*********", "paymentID": *********, "amt": 45.88, "approveCode": 0}]}, {"chargeID": 1365322542, "codeType": "PMNT", "amt": -45.88, "curr": "AED", "originalAmt": -45.88, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-28T08:14:16", "billDate": "2025-05-28T08:14:16", "desc": "Payment Fee manual cancel - radixx", "comment": "Payment Fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364933213, "paymentMap": [{"key": "1365322542:*********", "paymentID": *********, "amt": -45.88, "approveCode": 0}]}, {"chargeID": 1364930417, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364930411, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1367550563, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "20kg BAG INCLUDED IN FARE manual cancel - radixx manual cancel - radixx", "comment": "20kg BAG INCLUDED IN FARE manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324505, "paymentMap": []}, {"chargeID": 1365324505, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "20kg BAG INCLUDED IN FARE manual cancel - radixx", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364950295, "paymentMap": []}, {"chargeID": 1364950295, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930417, "paymentMap": []}, {"chargeID": 1364930431, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1367550564, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Standard meal manual cancel - radixx manual cancel - radixx", "comment": "Standard meal manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324507, "paymentMap": []}, {"chargeID": 1365324507, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Standard meal manual cancel - radixx", "comment": "Standard meal", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364950296, "paymentMap": []}, {"chargeID": 1364950296, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930431, "paymentMap": []}]}, {"recNum": 1, "charges": [{"chargeID": 1365325550, "codeType": "PYRF", "amt": 1575.28, "curr": "AED", "originalAmt": 1575.28, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-28T08:15:17", "billDate": "2025-05-28T08:15:17", "desc": "Overpayment refund charge", "comment": "Payment Charge", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365325550:*********", "paymentID": *********, "amt": 1575.28, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-05-28T08:15:17"}, {"chargeID": 1365329721, "codeType": "PYRF", "amt": -1575.28, "curr": "AED", "originalAmt": -1575.28, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-28T08:17:46", "billDate": "2025-05-28T08:17:46", "desc": "Overpayment refund charge manual cancel - radixx", "comment": "Overpayment refund charge", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365325550, "paymentMap": [{"key": "1365329721:210542834", "paymentID": 210542834, "amt": -1575.28, "approveCode": 0}]}, {"chargeID": 1364930430, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930430:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1367550551, "codeType": "INSU", "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "INSU manual cancel - radixx manual cancel - radixx", "comment": "INSU manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324494, "paymentMap": [{"key": "1367550551:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}]}, {"chargeID": 1365324494, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "INSU manual cancel - radixx", "comment": "INSU", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324494:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}]}, {"chargeID": 1364950284, "codeType": "INSU", "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "INSU", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930430, "paymentMap": [{"key": "1364950284:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}]}, {"chargeID": 1364930423, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364930419, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930423:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364930420, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1364930419, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930420:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1364930421, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1364930419, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930421:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1364930424, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364930419, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930424:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1364930422, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1364930419, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930422:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367550549, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Advanced passenger information fee manual cancel - radixx manual cancel - radixx", "comment": "Advanced passenger information fee manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324492, "paymentMap": [{"key": "1367550549:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1367550550, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Passenger Service Charge (Intl) manual cancel - radixx manual cancel - radixx", "comment": "Passenger Service Charge (Intl) manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324493, "paymentMap": [{"key": "1367550550:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1367550552, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Passenger Facilities Charge. manual cancel - radixx manual cancel - radixx", "comment": "Passenger Facilities Charge. manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324495, "paymentMap": [{"key": "1367550552:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1367550554, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "YQ - DUMMY manual cancel - radixx manual cancel - radixx", "comment": "YQ - DUMMY manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324497, "paymentMap": [{"key": "1367550554:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1367550555, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Passengers Security & Safety Service Fees manual cancel - radixx manual cancel - radixx", "comment": "Passengers Security & Safety Service Fees manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324498, "paymentMap": [{"key": "1367550555:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1365324492, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Advanced passenger information fee manual cancel - radixx", "comment": "Advanced passenger information fee", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324492:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1365324493, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Passenger Service Charge (Intl) manual cancel - radixx", "comment": "Passenger Service Charge (Intl)", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324493:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1365324495, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Passenger Facilities Charge. manual cancel - radixx", "comment": "Passenger Facilities Charge.", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324495:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1365324497, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "YQ - DUMMY manual cancel - radixx", "comment": "YQ - DUMMY", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324497:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1365324498, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Passengers Security & Safety Service Fees manual cancel - radixx", "comment": "Passengers Security & Safety Service Fees", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324498:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364950282, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930423, "paymentMap": [{"key": "1364950282:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364950283, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930420, "paymentMap": [{"key": "1364950283:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1364950285, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930421, "paymentMap": [{"key": "1364950285:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1364950287, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930424, "paymentMap": [{"key": "1364950287:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1364950288, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930422, "paymentMap": [{"key": "1364950288:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364930419, "codeType": "AIR", "amt": 409, "curr": "AED", "originalAmt": 409, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T23:47:37", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930419:*********", "paymentID": *********, "amt": 409, "approveCode": 0}]}, {"chargeID": 1367550553, "codeType": "AIR", "amt": -409, "curr": "AED", "originalAmt": -409, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "WEB:AIR manual cancel - radixx manual cancel - radixx", "comment": "WEB:AIR manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324496, "paymentMap": [{"key": "1367550553:*********", "paymentID": *********, "amt": -409, "approveCode": 0}]}, {"chargeID": 1365324496, "codeType": "AIR", "amt": 409, "curr": "AED", "originalAmt": 409, "originalCurr": "AED", "status": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "WEB:AIR manual cancel - radixx", "comment": "WEB:AIR", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1365324496:*********", "paymentID": *********, "amt": 409, "approveCode": 0}]}, {"chargeID": 1364950286, "codeType": "AIR", "amt": -409, "curr": "AED", "originalAmt": -409, "originalCurr": "AED", "status": 0, "billDate": "2025-05-28T00:59:17", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930419, "paymentMap": [{"key": "1364950286:*********", "paymentID": *********, "amt": -409, "approveCode": 0}]}, {"chargeID": 1364930425, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364930419, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364930425:*********", "paymentID": *********, "amt": -1529.4, "approveCode": 0}, {"key": "1364930425:*********", "paymentID": *********, "amt": 1529.4, "approveCode": 0}]}, {"chargeID": 1367550548, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "20kg BAG INCLUDED IN FARE manual cancel - radixx manual cancel - radixx", "comment": "20kg BAG INCLUDED IN FARE manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324491, "paymentMap": []}, {"chargeID": 1365324491, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "20kg BAG INCLUDED IN FARE manual cancel - radixx", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364950281, "paymentMap": []}, {"chargeID": 1364950281, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930425, "paymentMap": []}, {"chargeID": 1364930432, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T23:47:37", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181218"}, {"chargeID": 1367550556, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-29T12:59:54", "billDate": "2025-05-29T12:59:54", "desc": "Standard meal manual cancel - radixx manual cancel - radixx", "comment": "Standard meal manual cancel - radixx", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1365324499, "paymentMap": []}, {"chargeID": 1365324499, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "exchRateDate": "2025-05-28T08:14:43", "billDate": "2025-05-28T08:14:43", "desc": "Standard meal manual cancel - radixx", "comment": "Standard meal", "reasonID": 4, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364950289, "paymentMap": []}, {"chargeID": 1364950289, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-28T00:59:17", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 10, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1364930432, "paymentMap": []}]}], "parentPNRs": [], "childPNRs": []}