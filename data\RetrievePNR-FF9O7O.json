{"seriesNum": "299", "PNR": "FF9O7O", "bookAgent": "WEB_MOBILE", "resCurrency": "AED", "PNRPin": "83053841", "bookDate": "2025-05-22T15:49:05", "modifyDate": "2025-05-30T14:49:20", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "bbf35d4a3f8c47jat8x4r9u1d4786323g747ae5c7435", "securityGUID": "bbf35d4a3f8c47jat8x4r9u1d4786323g747ae5c7435", "lastLoadGUID": "2b9f9bd4-467b-42e3-be6f-b2c90660c64a", "isAsyncPNR": false, "MasterPNR": "FF9O7O", "segments": [{"segKey": "16087850:16087850:5/30/2025 8:55:00 PM", "LFID": 16087850, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087850", "org": "DXB", "dest": "TLV", "depTime": "2025-05-30T20:55:00", "depTimeGMT": "2025-05-30T16:55:00", "arrTime": "2025-05-30T23:35:00", "operCarrier": "FZ", "operFlightNum": "1807", "mrktCarrier": "FZ", "mrktFlightNum": "1807", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181580, "depDate": "2025-05-30T20:55:00", "legKey": "16087850:181580:5/30/2025 8:55:00 PM", "customerKey": "E25894D1917EEA82E91DF23050051EC53C3E5707C162A9492C375F94BDDDF8AA"}], "active": true, "changeType": "TK"}, {"segKey": "16659885:16659885:5/26/2025 12:05:00 PM", "LFID": 16659885, "depDate": "2025-05-26T00:00:00", "flightGroupId": "16659885", "org": "DXB", "dest": "TLV", "depTime": "2025-05-26T12:05:00", "depTimeGMT": "2025-05-26T08:05:00", "arrTime": "2025-05-26T14:35:00", "operCarrier": "FZ", "operFlightNum": "1081", "mrktCarrier": "FZ ", "mrktFlightNum": "1081", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 185104, "depDate": "2025-05-26T12:05:00", "legKey": "16659885:185104:5/26/2025 12:05:00 PM", "customerKey": "7A332BEFD216EE9E1DB4FE1DA294523EC58BC38D49ACB2A08CEE914375E532EC"}], "active": true}], "persons": [{"paxID": 269847984, "fName": "ASMAA", "lName": "AMER", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "R", "insuPurchasedate": "5/22/2025 3:49:05 PM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "UDSWM-N289C-INS", "insuTransID": "UDSWM-N289C-INS/e3cabb41-1bf2-41c2-bc20-6c21a0722029", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682f45c7000777000000f219#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-22T15:49:05"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "oday.ali", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/22/2025 3:49:05 PM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "UDSWM-N289C-INS/e3cabb41-1bf2-41c2-bc20-6c21a0722029", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68331111000778000001d381#269847984#1#ENT#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-25T12:49:09"}]}], "payments": [{"paymentID": *********, "paxID": 270104982, "method": "VISA", "status": "1", "paidDate": "2025-05-25T13:06:36", "cardNum": "************4725", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 72.1, "baseCurr": "AED", "baseAmt": 72.1, "userID": "paybylink", "channelID": 2, "cardHolderName": "Ahlam Nsassra", "authCode": "123158", "reference": "23242441", "externalReference": "23242441", "tranId": "21620534", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21620534}, {"paymentID": 210226343, "paxID": 270104977, "method": "VISA", "status": "2", "paidDate": "2025-05-25T13:00:57", "cardNum": "************1132", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 72.1, "baseCurr": "AED", "baseAmt": 72.1, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON>sha <PERSON>", "reference": "23242382", "externalReference": "23242382", "tranId": "21620534", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21620534}, {"paymentID": *********, "paxID": 269963373, "method": "VISA", "status": "1", "paidDate": "2025-05-23T18:59:49", "cardNum": "************4725", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 685.64, "baseCurr": "AED", "baseAmt": 685.64, "userID": "paybylink", "channelID": 2, "cardHolderName": "AHLAM NASASRA", "authCode": "296145", "reference": "23210687", "externalReference": "23210687", "tranId": "21592488", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21592488}, {"paymentID": *********, "paxID": 266416552, "method": "VCHR", "status": "1", "paidDate": "2025-05-22T15:49:08", "voucherNum": 3251413, "gateway": "EPS", "paidCurr": "AED", "paidAmt": 550.03, "baseCurr": "AED", "baseAmt": 550.03, "userID": "smartrez", "channelID": 5, "voucherNumFull": "Y7DVR8", "authCode": "Y7DVR8", "reference": "A5416779", "externalReference": "A5416779", "tranId": "21570920", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21570920}, {"paymentID": 209956481, "paxID": 269851716, "method": "MSCD", "status": "2", "paidDate": "2025-05-22T15:49:55", "cardNum": "************9691", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 685.64, "baseCurr": "AED", "baseAmt": 685.64, "userID": "smartrez", "channelID": 5, "cardHolderName": "ASMA AMER", "reference": "23187899", "externalReference": "23187899", "tranId": "21570920", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21570920}, {"paymentID": 209957278, "paxID": 269852482, "method": "MSCD", "status": "2", "paidDate": "2025-05-22T16:38:08", "cardNum": "************5211", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 685.64, "baseCurr": "AED", "baseAmt": 685.64, "userID": "reza.davo<PERSON>", "channelID": 1, "cardHolderName": "<PERSON>", "reference": "23188851", "externalReference": "23188851", "tranId": "21571750", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21571750}], "OAFlights": null, "physicalFlights": [{"key": "16659885:185104:2025-05-26T12:05:00 PM", "LFID": 16659885, "PFID": 185104, "org": "DXB", "dest": "TLV", "depDate": "2025-05-26T12:05:00", "depTime": "2025-05-26T12:05:00", "arrTime": "2025-05-26T14:35:00", "carrier": "FZ", "flightNum": "1081", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "1081", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 12600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false}, {"key": "16087850:181580:2025-05-30T08:55:00 PM", "LFID": 16087850, "PFID": 181580, "org": "DXB", "dest": "TLV", "depDate": "2025-05-30T20:55:00", "depTime": "2025-05-30T20:55:00", "arrTime": "2025-05-30T23:35:00", "carrier": "FZ", "flightNum": "1807", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "1807", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TLV", "operatingCarrier": "FZ", "flightDuration": 13200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tel Aviv Ben Gurion", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 2:47:18 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1361195324, "codeType": "INSU", "taxChargeID": 1361195315, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:10", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798051, "paymentMap": [{"key": "1361195324:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-25T12:49:10"}, {"chargeID": 1357798051, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798051:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1361195316, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1361195315, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:09", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798022, "paymentMap": [{"key": "1361195316:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1361195318, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361195315, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:09", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798025, "paymentMap": [{"key": "1361195318:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1361195319, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1361195315, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:09", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798023, "paymentMap": [{"key": "1361195319:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1361195321, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1361195315, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:10", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798024, "paymentMap": [{"key": "1361195321:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1361195322, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361195315, "amt": -180, "curr": "AED", "originalAmt": -180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:10", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798026, "paymentMap": [{"key": "1361195322:*********", "paymentID": *********, "amt": -180, "approveCode": 0}]}, {"chargeID": 1357798022, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1357798021, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798022:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1357798025, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1357798021, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798025:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357798023, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1357798021, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798023:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1357798024, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1357798021, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798024:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1357798026, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1357798021, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798026:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1361195315, "codeType": "AIR", "amt": -870, "curr": "AED", "originalAmt": -870, "originalCurr": "AED", "status": 0, "billDate": "2025-05-25T12:49:09", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798021, "paymentMap": [{"key": "1361195315:*********", "paymentID": *********, "amt": -204.33, "approveCode": 0}, {"key": "1361195315:*********", "paymentID": *********, "amt": -665.67, "approveCode": 0}]}, {"chargeID": 1357798021, "codeType": "AIR", "amt": 870, "curr": "AED", "originalAmt": 870, "originalCurr": "AED", "status": 0, "billDate": "2025-05-22T15:49:05", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1357798021:*********", "paymentID": *********, "amt": 665.67, "approveCode": 0}, {"key": "1357798021:*********", "paymentID": *********, "amt": 204.33, "approveCode": 0}]}, {"chargeID": 1359313268, "codeType": "PMNT", "amt": 19.97, "curr": "AED", "originalAmt": 19.97, "originalCurr": "AED", "status": 0, "billDate": "2025-05-23T18:59:53", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1359313268:*********", "paymentID": *********, "amt": 19.97, "approveCode": 0}]}, {"chargeID": 1361195323, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1361195315, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:10", "desc": "Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798028, "paymentMap": []}, {"chargeID": 1357798028, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1357798021, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361195320, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1361195315, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:10", "desc": "30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798027, "paymentMap": []}, {"chargeID": 1357798027, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1357798021, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361195317, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1361195315, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-25T12:49:09", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1357798052, "paymentMap": [], "PFID": "185104"}, {"chargeID": 1357798052, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-22T15:49:05", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185104"}]}, {"recNum": 2, "charges": [{"chargeID": 1361195337, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:46:13", "billDate": "2025-05-25T12:49:10", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195337:*********", "paymentID": *********, "amt": 10, "approveCode": 0}, {"key": "1361195337:*********", "paymentID": *********, "amt": 25.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-25T12:49:10"}, {"chargeID": 1361195326, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1361195325, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195326:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1361195327, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1361195325, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195327:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1361195328, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1361195325, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195328:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1361195329, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361195325, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195329:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1361195331, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361195325, "amt": 180, "curr": "AED", "originalAmt": 180, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195331:*********", "paymentID": *********, "amt": 180, "approveCode": 0}]}, {"chargeID": 1361195325, "codeType": "AIR", "amt": 880, "curr": "AED", "originalAmt": 880, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "FZ 1807 DXB-TLV 30May2025 Fri 20:55 23:35\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195325:*********", "paymentID": *********, "amt": 550.03, "approveCode": 0}, {"key": "1361195325:*********", "paymentID": *********, "amt": 329.97, "approveCode": 0}]}, {"chargeID": 1361215267, "codeType": "PMNT", "amt": 2.1, "curr": "AED", "originalAmt": 2.1, "originalCurr": "AED", "status": 1, "billDate": "2025-05-25T13:06:46", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361215267:*********", "paymentID": *********, "amt": 2.1, "approveCode": 0}]}, {"chargeID": 1361195336, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361195336:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1361195338, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:47:42", "billDate": "2025-05-25T12:49:10", "desc": "Special Service Request:FRST-7F", "comment": "FLXID:SPST_ZONE1_WIN_AIS:\r\nFRONT ROW SEAT SELECTION", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580"}, {"chargeID": 1361195335, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1361195325, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361195332, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1361195325, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361195333, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1361195325, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-25T12:49:10", "billDate": "2025-05-25T12:49:10", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580"}, {"chargeID": 1369200774, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-30T14:49:20", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181580", "ssrCommentId": "*********"}]}], "parentPNRs": [], "childPNRs": []}