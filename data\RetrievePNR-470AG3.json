{"seriesNum": "299", "PNR": "470AG3", "bookAgent": "MOBILE_APP", "resCurrency": "KZT", "PNRPin": "82120522", "bookDate": "2025-04-18T20:37:13", "modifyDate": "2025-05-29T15:02:10", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "f25d7c0518hdp2n4fb4cblt9taf2y9m24e673fcf5db6", "securityGUID": "f25d7c0518hdp2n4fb4cblt9taf2y9m24e673fcf5db6", "lastLoadGUID": "6ba6e068-9f51-49b8-8de3-db4407b4036b", "isAsyncPNR": false, "MasterPNR": "470AG3", "segments": [{"segKey": "16087832:16087832:5/23/2025 3:45:00 AM", "LFID": 16087832, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087832", "org": "ALA", "dest": "DXB", "depTime": "2025-05-23T03:45:00", "depTimeGMT": "2025-05-22T22:45:00", "arrTime": "2025-05-23T07:25:00", "operCarrier": "FZ", "operFlightNum": "1722", "mrktCarrier": "FZ ", "mrktFlightNum": "1722", "persons": [{"recNum": 4, "status": 5}, {"recNum": 2, "status": 5}, {"recNum": 3, "status": 5}, {"recNum": 5, "status": 5}, {"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181562, "depDate": "2025-05-23T03:45:00", "legKey": "16087832:181562:5/23/2025 3:45:00 AM", "customerKey": "2FA295C7AD4F558A2FF2580C7E3FB1825BC4F8F4A62D589C39BF3B16E2FCCBB3"}], "active": true, "changeType": "TK"}, {"segKey": "16087820:16087820:5/29/2025 9:35:00 PM", "LFID": 16087820, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16087820", "org": "DXB", "dest": "ALA", "depTime": "2025-05-29T21:35:00", "depTimeGMT": "2025-05-29T17:35:00", "arrTime": "2025-05-30T02:45:00", "operCarrier": "FZ", "operFlightNum": "1721", "mrktCarrier": "FZ ", "mrktFlightNum": "1721", "persons": [{"recNum": 9, "status": 5}, {"recNum": 7, "status": 5}, {"recNum": 8, "status": 5}, {"recNum": 10, "status": 5}, {"recNum": 6, "status": 5}], "legDetails": [{"PFID": 181594, "depDate": "2025-05-29T21:35:00", "legKey": "16087820:181594:5/29/2025 9:35:00 PM", "customerKey": "C7FA453F603F6DF19F4871455BFE652C6F56EDF9E7306F7A6E312F4B86C9229C"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 266253915, "fName": "YEKATERINA", "lName": "AKIMOVA", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2015-01-11T00:00:00", "recNum": [4, 9]}, {"paxID": 266253916, "fName": "ANNA", "lName": "AKIMOVA", "title": "MISS", "PTCID": 6, "gender": "F", "DOB": "2018-08-02T00:00:00", "recNum": [2, 7]}, {"paxID": 266253913, "fName": "DMITRIY", "lName": "AKIMOV", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1987-03-02T00:00:00", "recNum": [3, 8]}, {"paxID": 266253914, "fName": "NADEZHDA", "lName": "SHMELEVA", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1954-11-26T00:00:00", "recNum": [5, 10]}, {"paxID": 266253912, "fName": "MARIYA", "lName": "AKIMOVA", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1989-01-14T00:00:00", "FFNum": "716135486", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 6]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6802b50a00077800000064ca#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#5#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#2#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#4#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#3#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/21/2025 5:57:22 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "SZF9D-LUSXU-INS", "insuTransID": "SZF9D-LUSXU-INS/8e487e48-2e27-4d70-9612-9d72bb26a417", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6802b50a00077800000064ca#1#2#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/21/2025 5:57:22 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "SZF9D-LUSXU-INS", "insuTransID": "SZF9D-LUSXU-INS/8e487e48-2e27-4d70-9612-9d72bb26a417", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#5#2#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/21/2025 5:57:22 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "SZF9D-LUSXU-INS", "insuTransID": "SZF9D-LUSXU-INS/8e487e48-2e27-4d70-9612-9d72bb26a417", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#2#2#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 9, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/21/2025 5:57:22 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "SZF9D-LUSXU-INS", "insuTransID": "SZF9D-LUSXU-INS/8e487e48-2e27-4d70-9612-9d72bb26a417", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#4#2#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-18T20:37:13"}]}, {"recNum": 10, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/21/2025 5:57:22 PM", "provider": "<PERSON>", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7KZ2", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "SZF9D-LUSXU-INS", "insuTransID": "SZF9D-LUSXU-INS/8e487e48-2e27-4d70-9612-9d72bb26a417", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6802b50a00077800000064ca#3#2#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-18T20:37:13"}]}], "payments": [{"paymentID": *********, "paxID": 266254171, "method": "VISA", "status": "1", "paidDate": "2025-04-18T20:42:41", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 1123230.45, "baseCurr": "KZT", "baseAmt": 1123230.45, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "713514", "reference": "22501480", "externalReference": "22501480", "tranId": "20879831", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 20879831}, {"paymentID": 206225130, "paxID": 266254163, "method": "VISA", "status": "2", "paidDate": "2025-04-18T20:40:21", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 1123230.45, "baseCurr": "KZT", "baseAmt": 1123230.45, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "reference": "22501435", "externalReference": "22501435", "tranId": "20879831", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 20879831}, {"paymentID": *********, "paxID": 270356889, "method": "MILE", "status": "1", "paidDate": "2025-05-27T17:19:14", "gateway": "EPS", "paidCurr": "PNT", "paidAmt": 5000, "baseCurr": "KZT", "baseAmt": 19925.34, "userID": "MOBILE_APP", "channelID": 12, "FFNum": "716135486", "tierID": "3", "authCode": "23962414", "reference": "A5444382", "externalReference": "A5444382", "tranId": "21667313", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "0.25093670", "resExternalPaymentID": 21667313}, {"paymentID": 209838654, "paxID": 269739713, "method": "VISA", "status": "2", "paidDate": "2025-05-21T17:38:58", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 36528.95, "baseCurr": "KZT", "baseAmt": 36528.95, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "reference": "23170361", "externalReference": "23170361", "tranId": "21550965", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21550965}, {"paymentID": *********, "paxID": 269739756, "method": "VISA", "status": "1", "paidDate": "2025-05-21T17:41:23", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 36528.95, "baseCurr": "KZT", "baseAmt": 36528.95, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON><PERSON>", "authCode": "726356", "reference": "23170387", "externalReference": "23170387", "tranId": "21550965", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21550965}, {"paymentID": *********, "paxID": 270356893, "method": "IPAY", "status": "1", "paidDate": "2025-05-27T17:19:17", "cardNum": "************2822", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 71900.86, "baseCurr": "KZT", "baseAmt": 71900.86, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "542380", "reference": "23286505", "externalReference": "23286505", "tranId": "21667313", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21667313}, {"paymentID": 209708570, "paxID": 269615502, "method": "VISA", "status": "2", "paidDate": "2025-05-20T17:09:54", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 26865.49, "baseCurr": "KZT", "baseAmt": 26865.49, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "reference": "23150024", "externalReference": "23150024", "tranId": "21528379", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21528379}, {"paymentID": *********, "paxID": 269741125, "method": "VISA", "status": "1", "paidDate": "2025-05-21T17:57:28", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 25513.72, "baseCurr": "KZT", "baseAmt": 25513.72, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "412498", "reference": "23170617", "externalReference": "23170617", "tranId": "21551306", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21551306}, {"paymentID": *********, "paxID": 269615486, "method": "VISA", "status": "1", "paidDate": "2025-05-20T17:10:31", "cardNum": "************1235", "gateway": "EPS", "paidCurr": "KZT", "paidAmt": 26865.49, "baseCurr": "KZT", "baseAmt": 26865.49, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "452328", "reference": "23150036", "externalReference": "23150036", "tranId": "21528379", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomkztallniall001", "exchangeRate": "1", "resExternalPaymentID": 21528379}], "OAFlights": null, "physicalFlights": [{"key": "16087832:181562:2025-05-23T03:45:00 AM", "LFID": 16087832, "PFID": 181562, "org": "ALA", "dest": "DXB", "depDate": "2025-05-23T03:45:00", "depTime": "2025-05-23T03:45:00", "arrTime": "2025-05-23T07:25:00", "carrier": "FZ", "flightNum": "1722", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1722", "flightStatus": "CLOSED", "originMetroGroup": "ALA", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16800, "reaccomChangeAlert": false, "originName": "Almaty", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:07:10 AM"}, {"key": "16087820:181594:2025-05-29T09:35:00 PM", "LFID": 16087820, "PFID": 181594, "org": "DXB", "dest": "ALA", "depDate": "2025-05-29T21:35:00", "depTime": "2025-05-29T21:35:00", "arrTime": "2025-05-30T02:45:00", "carrier": "FZ", "flightNum": "1721", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1721", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "ALA", "operatingCarrier": "FZ", "flightDuration": 15000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Almaty", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:05:21 AM"}], "chargeInfos": [{"recNum": 9, "charges": [{"chargeID": 1356353531, "codeType": "INSU", "amt": 4954.12, "curr": "KZT", "originalAmt": 4954.12, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-21T17:57:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356353531:*********", "paymentID": *********, "amt": 4954.12, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.0\",\"Premium\":\"48.6\",\"Tax\":\"2.31\",\"SegPaxCount\":\"5\"}", "ChargeBookDate": "2025-05-21T17:57:22"}, {"chargeID": 1311168159, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168157, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168159:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168161, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1311168157, "amt": 10684, "curr": "KZT", "originalAmt": 10684, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168161:*********", "paymentID": *********, "amt": 10684, "approveCode": 0}]}, {"chargeID": 1311168160, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168157, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168160:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168163, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1311168157, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168163:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168162, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1311168157, "amt": 6411, "curr": "KZT", "originalAmt": 6411, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168162:*********", "paymentID": *********, "amt": 6411, "approveCode": 0}]}, {"chargeID": 1311168158, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168157, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168158:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168157, "codeType": "AIR", "amt": 55196, "curr": "KZT", "originalAmt": 55196, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168157:*********", "paymentID": *********, "amt": 55196, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364606487, "codeType": "FRST", "amt": 8347, "curr": "KZT", "originalAmt": 8347, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_MID::181594", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364606487:*********", "paymentID": *********, "amt": 8347, "approveCode": 0}], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168164, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168157, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168200, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606051, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-27T17:19:06", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168200, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606488, "codeType": "FPML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "FPML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1311168154, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168149, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168154:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-18T20:37:13"}, {"chargeID": 1311168153, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168149, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168153:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168151, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168149, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168151:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168150, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": 1311168149, "amt": 1732, "curr": "KZT", "originalAmt": 1732, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Aviation Security Fee", "comment": "Aviation Security Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168150:*********", "paymentID": *********, "amt": 1732, "approveCode": 0}]}, {"chargeID": 1311168152, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": 1311168149, "amt": 7220, "curr": "KZT", "originalAmt": 7220, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168152:*********", "paymentID": *********, "amt": 7220, "approveCode": 0}]}, {"chargeID": 1311168149, "codeType": "AIR", "amt": 55195, "curr": "KZT", "originalAmt": 55195, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168149:*********", "paymentID": *********, "amt": 55195, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356333997, "codeType": "FRST", "amt": 9040, "curr": "KZT", "originalAmt": 9040, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-21T17:35:19", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1356333997:*********", "paymentID": *********, "amt": 9040, "approveCode": 0}], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1311168155, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168149, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168199, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665009, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-20T17:06:46", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168199, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665096, "codeType": "CHML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-20T17:06:46", "desc": "CHML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 7, "charges": [{"chargeID": 1356353532, "codeType": "INSU", "amt": 4954.12, "curr": "KZT", "originalAmt": 4954.12, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-21T17:57:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356353532:*********", "paymentID": *********, "amt": 4954.12, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.0\",\"Premium\":\"48.6\",\"Tax\":\"2.31\",\"SegPaxCount\":\"5\"}", "ChargeBookDate": "2025-05-21T17:57:22"}, {"chargeID": 1311168177, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168174, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168177:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168175, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168174, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168175:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168178, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1311168174, "amt": 10684, "curr": "KZT", "originalAmt": 10684, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168178:*********", "paymentID": *********, "amt": 10684, "approveCode": 0}]}, {"chargeID": 1311168179, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1311168174, "amt": 6411, "curr": "KZT", "originalAmt": 6411, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168179:*********", "paymentID": *********, "amt": 6411, "approveCode": 0}]}, {"chargeID": 1311168180, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1311168174, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168180:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168176, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168174, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168176:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168174, "codeType": "AIR", "amt": 55196, "curr": "KZT", "originalAmt": 55196, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168174:*********", "paymentID": *********, "amt": 55196, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364606509, "codeType": "FRST", "amt": 9043, "curr": "KZT", "originalAmt": 9043, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "FRST", "comment": "FLXID:FRST_Zone3_73B_73M_WIN_AIS::181594", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364606509:*********", "paymentID": *********, "amt": 9043, "approveCode": 0}], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168181, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168174, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168202, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606052, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-27T17:19:06", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168202, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606510, "codeType": "FPML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "FPML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1311168170, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168166, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168170:*********", "paymentID": *********, "amt": 713, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-18T20:37:13"}, {"chargeID": 1311168168, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168166, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168168:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168167, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": 1311168166, "amt": 1732, "curr": "KZT", "originalAmt": 1732, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Aviation Security Fee", "comment": "Aviation Security Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168167:*********", "paymentID": *********, "amt": 1732, "approveCode": 0}]}, {"chargeID": 1311168171, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168166, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168171:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168169, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": 1311168166, "amt": 7220, "curr": "KZT", "originalAmt": 7220, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168169:*********", "paymentID": *********, "amt": 7220, "approveCode": 0}]}, {"chargeID": 1311168166, "codeType": "AIR", "amt": 55195, "curr": "KZT", "originalAmt": 55195, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168166:*********", "paymentID": *********, "amt": 55195, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356333996, "codeType": "FRST", "amt": 8345, "curr": "KZT", "originalAmt": 8345, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-21T17:35:19", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1356333996:*********", "paymentID": *********, "amt": 8345, "approveCode": 0}], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1311168172, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168166, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168201, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665010, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-20T17:06:45", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168201, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665097, "codeType": "CHML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-20T17:06:45", "desc": "CHML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 8, "charges": [{"chargeID": 1356353529, "codeType": "INSU", "amt": 4954.12, "curr": "KZT", "originalAmt": 4954.12, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-21T17:57:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356353529:*********", "paymentID": *********, "amt": 4954.12, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.0\",\"Premium\":\"48.6\",\"Tax\":\"2.31\",\"SegPaxCount\":\"5\"}", "ChargeBookDate": "2025-05-21T17:57:22"}, {"chargeID": 1311168079, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168077, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168079:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168081, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1311168077, "amt": 10684, "curr": "KZT", "originalAmt": 10684, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168081:*********", "paymentID": *********, "amt": 10684, "approveCode": 0}]}, {"chargeID": 1311168080, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168077, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168080:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168082, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1311168077, "amt": 6411, "curr": "KZT", "originalAmt": 6411, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168082:*********", "paymentID": *********, "amt": 6411, "approveCode": 0}]}, {"chargeID": 1311168078, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168077, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168078:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168083, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1311168077, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168083:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168077, "codeType": "AIR", "amt": 55196, "curr": "KZT", "originalAmt": 55196, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168077:*********", "paymentID": *********, "amt": 55196, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364606484, "codeType": "XLGR", "amt": 23094, "curr": "KZT", "originalAmt": 23094, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_MID::181594", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364606484:*********", "paymentID": *********, "amt": 23094, "approveCode": 0}], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168084, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168077, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168196, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1311168065, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168061, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168065:*********", "paymentID": *********, "amt": 713, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-18T20:37:13"}, {"chargeID": 1311168062, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": 1311168061, "amt": 1732, "curr": "KZT", "originalAmt": 1732, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Aviation Security Fee", "comment": "Aviation Security Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168062:*********", "paymentID": *********, "amt": 1732, "approveCode": 0}]}, {"chargeID": 1311168063, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168061, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168063:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168066, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168061, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168066:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168064, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": 1311168061, "amt": 7220, "curr": "KZT", "originalAmt": 7220, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168064:*********", "paymentID": *********, "amt": 7220, "approveCode": 0}]}, {"chargeID": 1311168061, "codeType": "AIR", "amt": 55195, "curr": "KZT", "originalAmt": 55195, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168061:*********", "paymentID": *********, "amt": 55195, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356333998, "codeType": "FRST", "amt": 9040, "curr": "KZT", "originalAmt": 9040, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-21T17:35:19", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1356333998:*********", "paymentID": *********, "amt": 9040, "approveCode": 0}], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1311168067, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168061, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168195, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354664887, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-20T17:06:45", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168195, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665094, "codeType": "LSML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-20T17:06:46", "desc": "LSML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 10, "charges": [{"chargeID": 1356353530, "codeType": "INSU", "amt": 4954.12, "curr": "KZT", "originalAmt": 4954.12, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-21T17:57:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356353530:*********", "paymentID": *********, "amt": 4954.12, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.0\",\"Premium\":\"48.6\",\"Tax\":\"2.31\",\"SegPaxCount\":\"5\"}", "ChargeBookDate": "2025-05-21T17:57:22"}, {"chargeID": 1311168143, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168140, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168143:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168145, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1311168140, "amt": 6411, "curr": "KZT", "originalAmt": 6411, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168145:*********", "paymentID": *********, "amt": 6411, "approveCode": 0}]}, {"chargeID": 1311168142, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168140, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168142:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168144, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1311168140, "amt": 10684, "curr": "KZT", "originalAmt": 10684, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168144:*********", "paymentID": *********, "amt": 10684, "approveCode": 0}]}, {"chargeID": 1311168146, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1311168140, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168146:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168141, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168140, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168141:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168140, "codeType": "AIR", "amt": 55196, "curr": "KZT", "originalAmt": 55196, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168140:*********", "paymentID": *********, "amt": 55196, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364606485, "codeType": "XLGR", "amt": 23094, "curr": "KZT", "originalAmt": 23094, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_MID::181594", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364606485:*********", "paymentID": *********, "amt": 23094, "approveCode": 0}], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168147, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168140, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168198, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606046, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-27T17:19:06", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168198, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606486, "codeType": "FPML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "FPML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 5, "charges": [{"chargeID": 1311168134, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168132, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168134:*********", "paymentID": *********, "amt": 524, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-18T20:37:13"}, {"chargeID": 1311168133, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": 1311168132, "amt": 1732, "curr": "KZT", "originalAmt": 1732, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Aviation Security Fee", "comment": "Aviation Security Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168133:*********", "paymentID": *********, "amt": 1732, "approveCode": 0}]}, {"chargeID": 1311168135, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": 1311168132, "amt": 7220, "curr": "KZT", "originalAmt": 7220, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168135:*********", "paymentID": *********, "amt": 7220, "approveCode": 0}]}, {"chargeID": 1311168137, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168132, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168137:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168136, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168132, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168136:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168132, "codeType": "AIR", "amt": 55195, "curr": "KZT", "originalAmt": 55195, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168132:*********", "paymentID": *********, "amt": 55195, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1354666892, "codeType": "XLGR", "amt": 26083, "curr": "KZT", "originalAmt": 26083, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-20T17:09:44", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181562", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1354666892:*********", "paymentID": *********, "amt": 26083, "approveCode": 0}], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168138, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168132, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168197, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354664888, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-20T17:06:46", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168197, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665095, "codeType": "DBML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-20T17:06:46", "desc": "DBML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 6, "charges": [{"chargeID": 1356353528, "codeType": "INSU", "amt": 4954.12, "curr": "KZT", "originalAmt": 4954.12, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-21T17:57:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356353528:*********", "paymentID": *********, "amt": 4954.12, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.0\",\"Premium\":\"48.6\",\"Tax\":\"2.31\",\"SegPaxCount\":\"5\"}", "ChargeBookDate": "2025-05-21T17:57:22"}, {"chargeID": 1311168039, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1311168034, "amt": 6411, "curr": "KZT", "originalAmt": 6411, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168039:*********", "paymentID": *********, "amt": 6411, "approveCode": 0}]}, {"chargeID": 1311168040, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1311168034, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168040:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168037, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168034, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168037:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168035, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168034, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168035:*********", "paymentID": *********, "amt": 524, "approveCode": 0}]}, {"chargeID": 1311168036, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168034, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168036:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168038, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1311168034, "amt": 10684, "curr": "KZT", "originalAmt": 10684, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168038:*********", "paymentID": *********, "amt": 10684, "approveCode": 0}]}, {"chargeID": 1311168034, "codeType": "AIR", "amt": 55196, "curr": "KZT", "originalAmt": 55196, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 200, "tierPoints": 200, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168034:*********", "paymentID": *********, "amt": 55196, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1364614710, "codeType": "PMNT", "amt": 2094.2, "curr": "KZT", "originalAmt": 2094.2, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-27T17:19:30", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364614710:*********", "paymentID": *********, "amt": 2094.2, "approveCode": 0}]}, {"chargeID": 1364606482, "codeType": "XLGR", "amt": 26154, "curr": "KZT", "originalAmt": 26154, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181594", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1364606482:*********", "paymentID": *********, "amt": 6228.66, "approveCode": 0}, {"key": "1364606482:*********", "paymentID": *********, "amt": 19925.34, "approveCode": 0}], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168041, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168034, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168194, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606043, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-27T17:19:06", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168194, "paymentMap": [], "PFID": "181594"}, {"chargeID": 1364606483, "codeType": "FPML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-27T17:19:06", "desc": "FPML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181594", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1367760153, "codeType": "CKIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-29T15:02:10", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181594", "ssrCommentId": "*********"}]}, {"recNum": 1, "charges": [{"chargeID": 1311168010, "codeType": "TAX", "taxID": 4905, "taxCode": "JN", "taxChargeID": 1311168008, "amt": 524, "curr": "KZT", "originalAmt": 524, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "International Advanced Passenger Information Fee.", "comment": "International Advanced Passenger Information Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168010:*********", "paymentID": *********, "amt": 524, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-18T20:37:13"}, {"chargeID": 1311168011, "codeType": "TAX", "taxID": 11986, "taxCode": "UJ", "taxChargeID": 1311168008, "amt": 7220, "curr": "KZT", "originalAmt": 7220, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168011:*********", "paymentID": *********, "amt": 7220, "approveCode": 0}]}, {"chargeID": 1311168009, "codeType": "TAX", "taxID": 10986, "taxCode": "CS", "taxChargeID": 1311168008, "amt": 1732, "curr": "KZT", "originalAmt": 1732, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Aviation Security Fee", "comment": "Aviation Security Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168009:*********", "paymentID": *********, "amt": 1732, "approveCode": 0}]}, {"chargeID": 1311168012, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1311168008, "amt": 713, "curr": "KZT", "originalAmt": 713, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168012:*********", "paymentID": *********, "amt": 713, "approveCode": 0}]}, {"chargeID": 1311168013, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1311168008, "amt": 39239, "curr": "KZT", "originalAmt": 39239, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311168013:*********", "paymentID": *********, "amt": 39239, "approveCode": 0}]}, {"chargeID": 1311168008, "codeType": "AIR", "amt": 55195, "curr": "KZT", "originalAmt": 55195, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:37:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 200, "tierPoints": 200, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1311168008:*********", "paymentID": *********, "amt": 55195, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311175264, "codeType": "PMNT", "amt": 32715.45, "curr": "KZT", "originalAmt": 32715.45, "originalCurr": "KZT", "status": 1, "billDate": "2025-04-18T20:42:54", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1311175264:*********", "paymentID": *********, "amt": 32715.45, "approveCode": 0}]}, {"chargeID": 1354671133, "codeType": "PMNT", "amt": 782.49, "curr": "KZT", "originalAmt": 782.49, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-20T17:10:43", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1354671133:*********", "paymentID": *********, "amt": 782.49, "approveCode": 0}]}, {"chargeID": 1356340134, "codeType": "PMNT", "amt": 1063.95, "curr": "KZT", "originalAmt": 1063.95, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-21T17:41:36", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356340134:*********", "paymentID": *********, "amt": 1063.95, "approveCode": 0}]}, {"chargeID": 1356360275, "codeType": "PMNT", "amt": 743.12, "curr": "KZT", "originalAmt": 743.12, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-21T17:57:34", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1356360275:*********", "paymentID": *********, "amt": 743.12, "approveCode": 0}]}, {"chargeID": 1356333995, "codeType": "FRST", "amt": 9040, "curr": "KZT", "originalAmt": 9040, "originalCurr": "KZT", "status": 1, "billDate": "2025-05-21T17:35:19", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1356333995:*********", "paymentID": *********, "amt": 9040, "approveCode": 0}], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1311168014, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1311168008, "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1311168193, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-04-18T20:37:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354664886, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 0, "exchRate": 0, "billDate": "2025-05-20T17:06:45", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1311168193, "paymentMap": [], "PFID": "181562"}, {"chargeID": 1354665093, "codeType": "LSML", "amt": 0, "curr": "KZT", "originalAmt": 0, "originalCurr": "KZT", "status": 1, "exchRate": 1, "billDate": "2025-05-20T17:06:45", "desc": "LSML", "comment": "Overridden from WEB KZT 0 ", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181562", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}