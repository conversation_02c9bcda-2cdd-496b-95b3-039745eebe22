{"seriesNum": "299", "PNR": "26BN9F", "bookAgent": "iFly:FZ07712", "IATA": "80008002", "resCurrency": "AED", "PNRPin": "82635465", "bookDate": "2025-05-07T18:08:34", "modifyDate": "2025-05-14T12:06:42", "resType": "STAFF_TRAVEL", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "e9f8yfuf49w1i662cbpzqbw4x3dfa52b8d53c4f0e554", "securityGUID": "e9f8yfuf49w1i662cbpzqbw4x3dfa52b8d53c4f0e554", "lastLoadGUID": "9dd700bc-d5c4-4a79-8df0-d99a013084ae", "isAsyncPNR": false, "MasterPNR": "26BN9F", "segments": [{"segKey": "16087693:16087693:5/12/2025 10:40:00 PM", "LFID": 16087693, "depDate": "2025-05-12T00:00:00", "flightGroupId": "16087693", "org": "BUD", "dest": "DXB", "depTime": "2025-05-12T22:40:00", "depTimeGMT": "2025-05-12T20:40:00", "arrTime": "2025-05-13T06:15:00", "operCarrier": "FZ", "operFlightNum": "1406", "mrktCarrier": "FZ ", "mrktFlightNum": "1406", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181454, "depDate": "2025-05-12T22:40:00", "legKey": "16087693:181454:5/12/2025 10:40:00 PM", "customerKey": "6F276EB34579CABB67E5F1134E6AD281C7327362A14243D5E18C11B0BB6FDF56"}], "active": true, "changeType": "TK"}, {"segKey": "16087657:16087657:5/14/2025 5:40:00 PM", "LFID": 16087657, "depDate": "2025-05-14T00:00:00", "flightGroupId": "16087657", "org": "DXB", "dest": "BUD", "depTime": "2025-05-14T17:40:00", "depTimeGMT": "2025-05-14T13:40:00", "arrTime": "2025-05-14T21:40:00", "operCarrier": "FZ", "operFlightNum": "1405", "mrktCarrier": "FZ ", "mrktFlightNum": "1405", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181428, "depDate": "2025-05-14T17:40:00", "legKey": "16087657:181428:5/14/2025 5:40:00 PM", "customerKey": "8C766B6FB894B9D05C4D7A3CC1CFA685101A73B709F1CF760780137168A41DFE"}], "active": true}], "persons": [{"paxID": 268234619, "fName": "RITA", "lName": "STOCKER", "title": "MISS", "PTCID": 1, "gender": "F", "DOB": "1991-05-18T00:00:00", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "iFly:FZ07712", "statusReasonID": 0, "markFareClass": "S", "status": 5, "fareClass": "S", "operFareClass": "S", "FBC": "SID50", "fareBrand": "Pay To Change", "cabin": "ECONOMY", "emergencyContactID": *********, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "null#1#1#STAFF_TRAVEL#VAYANT#CREATE", "fareTypeID": 1, "channelID": 29, "bookDate": "2025-05-07T18:08:34"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "iFly:FZ07712", "statusReasonID": 0, "markFareClass": "S", "insuPurchasedate": "5/12/2025 3:27:41 PM", "provider": "<PERSON>", "status": 5, "fareClass": "S", "operFareClass": "S", "FBC": "SID50", "fareBrand": "Pay To Change", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "A77HF-XS8AS-INS/092074ed-109d-46e6-862d-7e3fd9fabb02", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "null#1#2#STAFF_TRAVEL#VAYANT#CREATE", "fareTypeID": 1, "channelID": 29, "cancelReasonID": 0, "bookDate": "2025-05-07T18:08:34"}]}], "payments": [{"paymentID": *********, "paxID": *********, "method": "IPAY", "status": "1", "paidDate": "2025-05-12T15:27:45", "cardNum": "************8002", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 36.77, "baseCurr": "AED", "baseAmt": 36.77, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "310723", "reference": "22994299", "externalReference": "22994299", "tranId": "21364852", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21364852}, {"paymentID": *********, "paxID": *********, "method": "IPAY", "status": "1", "paidDate": "2025-05-07T19:31:43", "cardNum": "************8002", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 422.3, "baseCurr": "AED", "baseAmt": 422.3, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "802730", "reference": "22900861", "externalReference": "22900861", "tranId": "21274740", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21274740}, {"paymentID": *********, "paxID": 268536258, "method": "IPAY", "status": "1", "paidDate": "2025-05-10T17:59:54", "cardNum": "************8002", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 36.05, "baseCurr": "AED", "baseAmt": 36.05, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "821309", "reference": "22957874", "externalReference": "22957874", "tranId": "21328273", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21328273}, {"paymentID": *********, "paxID": 49038559, "method": "INVC", "status": "1", "paidDate": "2025-05-07T18:08:38", "IATANum": "80008002", "paidCurr": "AED", "paidAmt": 775, "baseCurr": "AED", "baseAmt": 775, "userID": "ltfirm.travel", "channelID": 29, "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1", "resExternalPaymentID": 1}], "OAFlights": null, "physicalFlights": [{"key": "16087693:181454:2025-05-12T10:40:00 PM", "LFID": 16087693, "PFID": 181454, "org": "BUD", "dest": "DXB", "depDate": "2025-05-12T22:40:00", "depTime": "2025-05-12T22:40:00", "arrTime": "2025-05-13T06:15:00", "carrier": "FZ", "flightNum": "1406", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1406", "flightStatus": "CLOSED", "originMetroGroup": "BUD", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20100, "reaccomChangeAlert": false, "originName": "Budapest", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:11:13 AM"}, {"key": "16087657:181428:2025-05-14T05:40:00 PM", "LFID": 16087657, "PFID": 181428, "org": "DXB", "dest": "BUD", "depDate": "2025-05-14T17:40:00", "depTime": "2025-05-14T17:40:00", "arrTime": "2025-05-14T21:40:00", "carrier": "FZ", "flightNum": "1405", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1405", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BUD", "operatingCarrier": "FZ", "flightDuration": 21600, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Budapest", "isActive": false}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1336464425, "codeType": "NSKB", "taxID": 11006, "taxCode": "NSKB", "taxChargeID": 1336464422, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "NO SKYWARDS TIER BENEFITS", "comment": "NO SKYWARDS TIER BENEFITS", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "isSSR": true, "ChargeBookDate": "2025-05-07T18:08:34"}, {"chargeID": 1336464426, "codeType": "TAX", "taxID": 11286, "taxCode": "HU", "taxChargeID": 1336464422, "amt": 130, "curr": "AED", "originalAmt": 130, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Airport Departure Tax (International)", "comment": "Airport Departure Tax (International)", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464426:*********", "paymentID": *********, "amt": 130, "approveCode": 0}]}, {"chargeID": 1336464427, "codeType": "TAX", "taxID": 11287, "taxCode": "FE", "taxChargeID": 1336464422, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464427:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1336464424, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336464422, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464424:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336464422, "codeType": "AIR", "amt": 240, "curr": "AED", "originalAmt": 240, "originalCurr": "AED", "status": 1, "billDate": "2025-05-07T18:08:34", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464422:*********", "paymentID": *********, "amt": 240, "approveCode": 0}]}, {"chargeID": 1342999292, "codeType": "PMNT", "amt": 1.07, "curr": "AED", "originalAmt": 1.07, "originalCurr": "AED", "status": 1, "billDate": "2025-05-12T15:27:49", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342999292:*********", "paymentID": *********, "amt": 1.07, "approveCode": 0}]}, {"chargeID": 1340438183, "codeType": "PMNT", "amt": 1.05, "curr": "AED", "originalAmt": 1.05, "originalCurr": "AED", "status": 1, "billDate": "2025-05-10T17:59:58", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340438183:*********", "paymentID": *********, "amt": 1.05, "approveCode": 0}]}, {"chargeID": 1336549287, "codeType": "PMNT", "amt": 12.3, "curr": "AED", "originalAmt": 12.3, "originalCurr": "AED", "status": 1, "billDate": "2025-05-07T19:31:47", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336549287:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}]}, {"chargeID": 1336548716, "codeType": "SPST", "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-07T19:31:39", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181454", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336548716:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181454"}, {"chargeID": 1336464423, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1336464422, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340437084, "codeType": "MLIN", "amt": 35, "curr": "AED", "originalAmt": 35, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-10T17:59:49", "desc": "MLIN", "comment": "FLXID:35 AED-Fare brand rule:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340437084:*********", "paymentID": *********, "amt": 35, "approveCode": 0}], "PFID": "181454"}, {"chargeID": 1336464477, "codeType": "LNGN", "taxID": 13790, "taxCode": "LNGN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "NO LOUNGE ACCESS.", "comment": "SSR Added", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181454"}]}, {"recNum": 2, "charges": [{"chargeID": 1336464474, "codeType": "NSKB", "taxID": 11006, "taxCode": "NSKB", "taxChargeID": 1336464469, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "NO SKYWARDS TIER BENEFITS", "comment": "NO SKYWARDS TIER BENEFITS", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "isSSR": true, "ChargeBookDate": "2025-05-07T18:08:34"}, {"chargeID": 1342998326, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-12T15:27:41", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342998326:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}"}, {"chargeID": 1336464471, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1336464469, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464471:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336464475, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1336464469, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464475:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1336464470, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1336464469, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464470:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1336464473, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1336464469, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464473:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1336464469, "codeType": "AIR", "amt": 240, "curr": "AED", "originalAmt": 240, "originalCurr": "AED", "status": 1, "billDate": "2025-05-07T18:08:34", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336464469:*********", "paymentID": *********, "amt": 240, "approveCode": 0}]}, {"chargeID": 1343772737, "codeType": "SPST", "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "billDate": "2025-05-13T07:48:27", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343772737:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181428"}, {"chargeID": 1343772736, "codeType": "SPST", "amt": -50, "curr": "AED", "originalAmt": -50, "originalCurr": "AED", "status": 0, "billDate": "2025-05-13T07:48:27", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1336548717, "paymentMap": [{"key": "1343772736:*********", "paymentID": *********, "amt": -50, "approveCode": 0}], "PFID": "181428"}, {"chargeID": 1336548717, "codeType": "SPST", "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-07T19:31:39", "desc": "SPST", "comment": "FLXID:73X_SPST_ZONE3_WIN_AIS::181428", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336548717:*********", "paymentID": *********, "amt": 50, "approveCode": 0}], "PFID": "181428"}, {"chargeID": 1336548718, "codeType": "BUPX", "amt": 310, "curr": "AED", "originalAmt": 310, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-07T19:31:39", "desc": "BUPX", "comment": "FLXID:BUPX_GLOBAL_Hub_Z3:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1336548718:*********", "paymentID": *********, "amt": 310, "approveCode": 0}]}, {"chargeID": 1336464472, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1336464469, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1336464478, "codeType": "LNGN", "taxID": 13790, "taxCode": "LNGN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-07T18:08:34", "desc": "NO LOUNGE ACCESS.", "comment": "SSR Added", "reasonID": 0, "channelID": 29, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181428"}, {"chargeID": 1345972803, "codeType": "CKIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-14T12:06:42", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181428", "ssrCommentId": "*********"}]}], "parentPNRs": [], "childPNRs": []}