{"seriesNum": "299", "PNR": "NQ0IAV", "bookAgent": "WEB2_LIVE", "resCurrency": "EUR", "PNRPin": "82852397", "bookDate": "2025-05-15T11:25:40", "modifyDate": "2025-05-30T11:39:30", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 4, "activeSegCount": 1, "webBookingID": "87b5a897f833042cqae5ya42idq4t46c1bua7c0da7cb", "securityGUID": "87b5a897f833042cqae5ya42idq4t46c1bua7c0da7cb", "lastLoadGUID": "7c5be33e-68af-43ba-866c-633cd48675b9", "isAsyncPNR": false, "MasterPNR": "NQ0IAV", "segments": [{"segKey": "16087804:16087804:5/30/2025 2:30:00 PM", "LFID": 16087804, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087804", "org": "LJU", "dest": "DXB", "depTime": "2025-05-30T14:30:00", "depTimeGMT": "2025-05-30T12:30:00", "arrTime": "2025-05-30T22:15:00", "operCarrier": "FZ", "operFlightNum": "1790", "mrktCarrier": "FZ ", "mrktFlightNum": "1790", "persons": [{"recNum": 2, "status": 5}, {"recNum": 1, "status": 5}, {"recNum": 4, "status": 5}, {"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181540, "depDate": "2025-05-30T14:30:00", "legKey": "16087804:181540:5/30/2025 2:30:00 PM", "customerKey": "E1FB7913B3C5123AE253B18013A0EE42886E709202B4292A1382CAB5205F8DF8"}], "active": true, "changeType": "TK"}, {"segKey": "16087826:16087826:6/4/2025 9:10:00 AM", "LFID": 16087826, "depDate": "2025-06-04T00:00:00", "flightGroupId": "16087826", "org": "DXB", "dest": "LJU", "depTime": "2025-06-04T09:10:00", "depTimeGMT": "2025-06-04T05:10:00", "arrTime": "2025-06-04T13:30:00", "operCarrier": "FZ", "operFlightNum": "1789", "mrktCarrier": "FZ ", "mrktFlightNum": "1789", "persons": [{"recNum": 6, "status": 1}, {"recNum": 5, "status": 1}, {"recNum": 8, "status": 1}, {"recNum": 7, "status": 1}], "legDetails": [{"PFID": 181556, "depDate": "2025-06-04T09:10:00", "legKey": "16087826:181556:6/4/2025 9:10:00 AM", "customerKey": "51557D3C03BC2F356A068226F8D94D82403D842C34A0AA565FEA7541981D0B5C"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 269071942, "fName": "TOMMASO", "lName": "TACCHEO", "title": "MSTR", "PTCID": 6, "gender": "M", "DOB": "2017-03-07T00:00:00", "recNum": [2, 6]}, {"paxID": 269071941, "fName": "FRANCESCA ANNA", "lName": "TACCHEO", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 5]}, {"paxID": 269071940, "fName": "ELISABETTA", "lName": "CAROTENUTO", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1978-07-16T00:00:00", "recNum": [4, 8]}, {"paxID": 269071939, "fName": "GIULIO", "lName": "TACCHEO", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1978-07-27T00:00:00", "FFNum": "774523190", "FFTier": "BLUE", "TierID": "3", "recNum": [3, 7]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "V", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VRL8SI5", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 270505943, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6825cd030007780000005a57#3#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "V", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VRL8SI5", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 270505944, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6825cd030007780000005a57#4#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "V", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VRL8SI5", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 270505945, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6825cd030007780000005a57#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "V", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VRL8SI5", "fareBrand": "Flex", "cabin": "ECONOMY", "emergencyContactID": 270505947, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6825cd030007780000005a57#2#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/29/2025 8:37:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8SI2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "LTDNV-TCYD7-INS/8b2d5b94-30ad-4927-887d-3743c6cc2fc1", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6825cd030007780000005a57#3#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/29/2025 8:37:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8SI2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "LTDNV-TCYD7-INS/8b2d5b94-30ad-4927-887d-3743c6cc2fc1", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6825cd030007780000005a57#4#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/29/2025 8:37:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8SI2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "LTDNV-TCYD7-INS/8b2d5b94-30ad-4927-887d-3743c6cc2fc1", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6825cd030007780000005a57#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-15T11:25:40"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/29/2025 8:37:41 AM", "provider": "<PERSON>", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8SI2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "LTDNV-TCYD7-INS/8b2d5b94-30ad-4927-887d-3743c6cc2fc1", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6825cd030007780000005a57#2#2#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-15T11:25:40"}]}], "payments": [{"paymentID": *********, "paxID": 270535598, "method": "MSCD", "status": "1", "paidDate": "2025-05-29T08:38:29", "cardNum": "************6928", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 35.51, "baseCurr": "EUR", "baseAmt": 35.51, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "GIULIO TACCHEO", "authCode": "784823", "reference": "23318720", "externalReference": "23318720", "tranId": "21700304", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "SVNMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21700304}, {"paymentID": *********, "paxID": 269072079, "method": "MSCD", "status": "1", "paidDate": "2025-05-15T11:26:31", "cardNum": "************6928", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 2012.08, "baseCurr": "EUR", "baseAmt": 2012.08, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "337969", "reference": "23047103", "externalReference": "23047103", "tranId": "21425346", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "SVNMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21425346}], "OAFlights": null, "physicalFlights": [{"key": "16087804:181540:2025-05-30T02:30:00 PM", "LFID": 16087804, "PFID": 181540, "org": "LJU", "dest": "DXB", "depDate": "2025-05-30T14:30:00", "depTime": "2025-05-30T14:30:00", "arrTime": "2025-05-30T22:15:00", "carrier": "FZ", "flightNum": "1790", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1790", "flightStatus": "CLOSED", "originMetroGroup": "LJU", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 20700, "reaccomChangeAlert": false, "originName": "Ljubljana", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 2:44:21 PM"}, {"key": "16087826:181556:2025-06-04T09:10:00 AM", "LFID": 16087826, "PFID": 181556, "org": "DXB", "dest": "LJU", "depDate": "2025-06-04T09:10:00", "depTime": "2025-06-04T09:10:00", "arrTime": "2025-06-04T13:30:00", "carrier": "FZ", "flightNum": "1789", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1789", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "LJU", "operatingCarrier": "FZ", "flightDuration": 22800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Ljubljana", "isActive": true, "changeType": "TK", "flightChangeTime": "5/1/2025 2:12:16 PM"}], "chargeInfos": [{"recNum": 6, "charges": [{"chargeID": 1367057001, "codeType": "INSU", "amt": 8.62, "curr": "EUR", "originalAmt": 8.62, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-29T08:37:41", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367057001:*********", "paymentID": *********, "amt": 8.62, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-29T08:37:41"}, {"chargeID": 1347496498, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496497, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496498:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496500, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496497, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496500:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347496501, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347496497, "amt": 18.34, "curr": "EUR", "originalAmt": 18.34, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496501:*********", "paymentID": *********, "amt": 18.34, "approveCode": 0}]}, {"chargeID": 1347496502, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347496497, "amt": 11, "curr": "EUR", "originalAmt": 11, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496502:*********", "paymentID": *********, "amt": 11, "approveCode": 0}]}, {"chargeID": 1347496503, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347496497, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496503:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496497, "codeType": "AIR", "amt": 157.5, "curr": "EUR", "originalAmt": 157.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496497:*********", "paymentID": *********, "amt": 157.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496521, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181556"}, {"chargeID": 1368904976, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 25, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1347496521, "paymentMap": [], "PFID": "181556", "POSAirport": "LJU"}, {"chargeID": 1347496504, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496497, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496499, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496497, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496520, "codeType": "CHML", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181556", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1347496490, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496489, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496490:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-15T11:25:40"}, {"chargeID": 1347496493, "codeType": "TAX", "taxID": 12933, "taxCode": "JJ", "taxChargeID": 1347496489, "amt": 7.45, "curr": "EUR", "originalAmt": 7.45, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Security Fee", "comment": "Security Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496493:*********", "paymentID": *********, "amt": 7.45, "approveCode": 0}]}, {"chargeID": 1347496494, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496489, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496494:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347496492, "codeType": "TAX", "taxID": 12932, "taxCode": "SI", "taxChargeID": 1347496489, "amt": 18.89, "curr": "EUR", "originalAmt": 18.89, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496492:*********", "paymentID": *********, "amt": 18.89, "approveCode": 0}]}, {"chargeID": 1347496489, "codeType": "AIR", "amt": 121.5, "curr": "EUR", "originalAmt": 121.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496489:*********", "paymentID": *********, "amt": 121.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": *********9, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181540", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496495, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496489, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496491, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496489, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": *********8, "codeType": "CHML", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "CHML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 5, "charges": [{"chargeID": 1367057000, "codeType": "INSU", "amt": 8.62, "curr": "EUR", "originalAmt": 8.62, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-29T08:37:41", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367057000:*********", "paymentID": *********, "amt": 8.62, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-29T08:37:41"}, {"chargeID": 1347496484, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347496480, "amt": 18.34, "curr": "EUR", "originalAmt": 18.34, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496484:*********", "paymentID": *********, "amt": 18.34, "approveCode": 0}]}, {"chargeID": 1347496483, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496480, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496483:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347496486, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347496480, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496486:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496485, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347496480, "amt": 11, "curr": "EUR", "originalAmt": 11, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496485:*********", "paymentID": *********, "amt": 11, "approveCode": 0}]}, {"chargeID": 1347496481, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496480, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496481:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496480, "codeType": "AIR", "amt": 157.5, "curr": "EUR", "originalAmt": 157.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496480:*********", "paymentID": *********, "amt": 157.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": *********7, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181556"}, {"chargeID": 1368905633, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 25, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********7, "paymentMap": [], "PFID": "181556", "POSAirport": "LJU"}, {"chargeID": 1347496487, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496480, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496482, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496480, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496527, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181556", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": 1347496473, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496472, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496473:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-15T11:25:40"}, {"chargeID": 1347496477, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496472, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496477:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347496475, "codeType": "TAX", "taxID": 12932, "taxCode": "SI", "taxChargeID": 1347496472, "amt": 18.89, "curr": "EUR", "originalAmt": 18.89, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496475:*********", "paymentID": *********, "amt": 18.89, "approveCode": 0}]}, {"chargeID": 1347496476, "codeType": "TAX", "taxID": 12933, "taxCode": "JJ", "taxChargeID": 1347496472, "amt": 7.45, "curr": "EUR", "originalAmt": 7.45, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Security Fee", "comment": "Security Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496476:*********", "paymentID": *********, "amt": 7.45, "approveCode": 0}]}, {"chargeID": 1347496472, "codeType": "AIR", "amt": 121.5, "curr": "EUR", "originalAmt": 121.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496472:*********", "paymentID": *********, "amt": 121.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": *********5, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181540", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496478, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496472, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496474, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496472, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496526, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 8, "charges": [{"chargeID": 1367056999, "codeType": "INSU", "amt": 8.62, "curr": "EUR", "originalAmt": 8.62, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-29T08:37:41", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367056999:*********", "paymentID": *********, "amt": 8.62, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-29T08:37:41"}, {"chargeID": 1347496466, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496463, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496466:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347496464, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496463, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496464:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496467, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347496463, "amt": 18.34, "curr": "EUR", "originalAmt": 18.34, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496467:*********", "paymentID": *********, "amt": 18.34, "approveCode": 0}]}, {"chargeID": 1347496468, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347496463, "amt": 11, "curr": "EUR", "originalAmt": 11, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496468:*********", "paymentID": *********, "amt": 11, "approveCode": 0}]}, {"chargeID": 1347496469, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347496463, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496469:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496463, "codeType": "AIR", "amt": 157.5, "curr": "EUR", "originalAmt": 157.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496463:*********", "paymentID": *********, "amt": 157.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": *********3, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181556"}, {"chargeID": 1368904736, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 25, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": *********3, "paymentMap": [], "PFID": "181556", "POSAirport": "LJU"}, {"chargeID": 1347496470, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496463, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496465, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496463, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496525, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181556", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 4, "charges": [{"chargeID": 1347496460, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496455, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496460:*********", "paymentID": *********, "amt": 75, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-15T11:25:40"}, {"chargeID": 1347496458, "codeType": "TAX", "taxID": 12932, "taxCode": "SI", "taxChargeID": 1347496455, "amt": 18.89, "curr": "EUR", "originalAmt": 18.89, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496458:*********", "paymentID": *********, "amt": 18.89, "approveCode": 0}]}, {"chargeID": 1347496459, "codeType": "TAX", "taxID": 12933, "taxCode": "JJ", "taxChargeID": 1347496455, "amt": 7.45, "curr": "EUR", "originalAmt": 7.45, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Security Fee", "comment": "Security Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496459:*********", "paymentID": *********, "amt": 7.45, "approveCode": 0}]}, {"chargeID": 1347496456, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496455, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496456:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496455, "codeType": "AIR", "amt": 121.5, "curr": "EUR", "originalAmt": 121.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496455:*********", "paymentID": *********, "amt": 121.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": *********1, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181540", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496461, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496455, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496457, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496455, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496524, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 7, "charges": [{"chargeID": 1367056998, "codeType": "INSU", "amt": 8.62, "curr": "EUR", "originalAmt": 8.62, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-29T08:37:41", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367056998:*********", "paymentID": *********, "amt": 8.62, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.13\",\"Premium\":\"38.88\",\"Tax\":\"1.85\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-29T08:37:41"}, {"chargeID": 1347496452, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347496446, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496452:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496451, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347496446, "amt": 11, "curr": "EUR", "originalAmt": 11, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496451:*********", "paymentID": *********, "amt": 11, "approveCode": 0}]}, {"chargeID": 1347496449, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496446, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496449:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1347496447, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496446, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496447:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496450, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347496446, "amt": 18.34, "curr": "EUR", "originalAmt": 18.34, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496450:*********", "paymentID": *********, "amt": 18.34, "approveCode": 0}]}, {"chargeID": 1347496446, "codeType": "AIR", "amt": 157.5, "curr": "EUR", "originalAmt": 157.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496446:*********", "paymentID": *********, "amt": 157.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496509, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181556"}, {"chargeID": 1368901382, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181556", "reasonID": 25, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1347496509, "paymentMap": [], "PFID": "181556", "POSAirport": "LJU"}, {"chargeID": 1347496453, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496446, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496448, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496446, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496523, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181556", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1347496443, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347496438, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496443:*********", "paymentID": *********, "amt": 75, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-15T11:25:40"}, {"chargeID": 1347496442, "codeType": "TAX", "taxID": 12933, "taxCode": "JJ", "taxChargeID": 1347496438, "amt": 7.45, "curr": "EUR", "originalAmt": 7.45, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Security Fee", "comment": "Security Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496442:*********", "paymentID": *********, "amt": 7.45, "approveCode": 0}]}, {"chargeID": 1347496439, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347496438, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496439:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1347496441, "codeType": "TAX", "taxID": 12932, "taxCode": "SI", "taxChargeID": 1347496438, "amt": 18.89, "curr": "EUR", "originalAmt": 18.89, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347496441:*********", "paymentID": *********, "amt": 18.89, "approveCode": 0}]}, {"chargeID": 1347496438, "codeType": "AIR", "amt": 121.5, "curr": "EUR", "originalAmt": 121.5, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:25:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 900, "tierPoints": 900, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1347496438:*********", "paymentID": *********, "amt": 121.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347502450, "codeType": "PMNT", "amt": 58.6, "curr": "EUR", "originalAmt": 58.6, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-15T11:26:37", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347502450:*********", "paymentID": *********, "amt": 58.6, "approveCode": 0}]}, {"chargeID": 1367062023, "codeType": "PMNT", "amt": 1.03, "curr": "EUR", "originalAmt": 1.03, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-29T08:38:35", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367062023:*********", "paymentID": *********, "amt": 1.03, "approveCode": 0}]}, {"chargeID": 1347496507, "codeType": "FRST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181540", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496444, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1347496438, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496440, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1347496438, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1347496522, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T11:25:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181540", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}