{"seriesNum": "299", "PNR": "Z175YU", "bookAgent": "maryam.g", "IATA": "091120DP", "resCurrency": "AED", "PNRPin": "82506099", "bookDate": "2025-05-03T09:29:04", "modifyDate": "2025-05-13T06:43:38", "resType": "STANDARD", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "", "securityGUID": "", "lastLoadGUID": "8e604315-f1c2-4bc6-abc8-2785dac88d98", "isAsyncPNR": false, "MasterPNR": "Z175YU", "segments": [{"segKey": "16087476:16087476:5/18/2025 4:35:00 PM", "LFID": 16087476, "depDate": "2025-05-18T00:00:00", "flightGroupId": "16087476", "org": "TBS", "dest": "DXB", "depTime": "2025-05-18T16:35:00", "depTimeGMT": "2025-05-18T12:35:00", "arrTime": "2025-05-18T19:50:00", "operCarrier": "FZ", "operFlightNum": "712", "mrktCarrier": "FZ", "mrktFlightNum": "712", "persons": [{"recNum": 3, "status": 5}], "legDetails": [{"PFID": 181237, "depDate": "2025-05-18T16:35:00", "legKey": "16087476:181237:5/18/2025 4:35:00 PM", "customerKey": "03FD8720B611C246227B8C2A9679DF0FB067C3DF67440DC1F1B1C5C29B4144BA"}], "active": true, "changeType": "TK"}, {"segKey": "16087456:16087456:5/8/2025 1:40:00 AM", "LFID": 16087456, "depDate": "2025-05-08T00:00:00", "flightGroupId": "16087456", "org": "DXB", "dest": "TBS", "depTime": "2025-05-08T01:40:00", "depTimeGMT": "2025-05-07T21:40:00", "arrTime": "2025-05-08T05:05:00", "operCarrier": "FZ", "operFlightNum": "713", "mrktCarrier": "FZ", "mrktFlightNum": "713", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181207, "depDate": "2025-05-08T01:40:00", "legKey": "16087456:181207:5/8/2025 1:40:00 AM", "customerKey": "5E64137C5482E49133815222E42FB6FE410624689F6D5F571D3EFDD4E454960A"}], "active": true, "changeType": "TK"}, {"segKey": "16087476:16087476:5/13/2025 4:35:00 PM", "LFID": 16087476, "depDate": "2025-05-13T00:00:00", "flightGroupId": "16087476", "org": "TBS", "dest": "DXB", "depTime": "2025-05-13T16:35:00", "depTimeGMT": "2025-05-13T12:35:00", "arrTime": "2025-05-13T19:50:00", "operCarrier": "FZ", "operFlightNum": "712", "mrktCarrier": "FZ", "mrktFlightNum": "712", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181237, "depDate": "2025-05-13T16:35:00", "legKey": "16087476:181237:5/13/2025 4:35:00 PM", "customerKey": "05CBDDF88D0B3A65FA5D8088BF1ECE87AE19584FA6E6F05FEAC01DA824E6B3D0"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 267748353, "fName": "MAHKAMEH", "lName": "KAZEMI", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1993-08-27T00:00:00", "FFNum": "601563712", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "maryam.g", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/3/2025 9:29:10 AM", "provider": "AIG", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987121976", "insuTransID": "cf7fdd68-487d-4b0a-85e2-9cb17cad7823", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6815e17a0007780000001e20#1#1#ENT#VAYANT#CREATE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-03T09:29:04"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "maryam.g", "statusReasonID": 0, "userResponseId": 1, "markFareClass": "N", "insuPurchasedate": "5/3/2025 9:29:10 AM", "provider": "AIG", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 2, "insuConfNum": "987121976", "insuTransID": "cf7fdd68-487d-4b0a-85e2-9cb17cad7823", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6815e17a0007780000001e20#1#2#ENT#VAYANT#CREATE", "fareTypeID": 13, "channelID": 1, "cancelReasonID": 1, "bookDate": "2025-05-03T09:29:04"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "fatemeh.hajebi", "statusReasonID": 0, "markFareClass": "T", "insuPurchasedate": "5/13/2025 6:40:35 AM", "provider": "<PERSON>", "status": 5, "fareClass": "T", "operFareClass": "T", "FBC": "TRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "W37GX-JFBVR-INS/1918a42b-683d-4633-82cd-749cedd0eb1c", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6822e8a80007780000005a2f#267748353#2#ENT#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 1, "bookDate": "2025-05-13T06:40:29"}]}], "payments": [{"paymentID": *********, "paxID": 268781965, "method": "VISA", "status": "1", "paidDate": "2025-05-13T06:43:33", "cardNum": "************5751", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 392.12, "baseCurr": "AED", "baseAmt": 392.12, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authCode": "958613", "reference": "23004059", "externalReference": "23004059", "tranId": "21373933", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21373933}, {"paymentID": *********, "paxID": 267748353, "method": "RPOS", "status": "1", "paidDate": "2025-05-03T09:47:18", "paidCurr": "AED", "paidAmt": 1748.3, "baseCurr": "AED", "baseAmt": 1748.3, "userID": "cashier.nib1", "channelID": 1, "paymentComment": "22593", "tierID": "3", "authCode": "769610", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "16087456:181207:2025-05-08T01:40:00 AM", "LFID": 16087456, "PFID": 181207, "org": "DXB", "dest": "TBS", "depDate": "2025-05-08T01:40:00", "depTime": "2025-05-08T01:40:00", "arrTime": "2025-05-08T05:05:00", "carrier": "FZ", "flightNum": "713", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "713", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false, "changeType": "TK", "flightChangeTime": "3/5/2025 1:42:49 PM"}, {"key": "16087476:181237:2025-05-13T04:35:00 PM", "LFID": 16087476, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-05-13T16:35:00", "depTime": "2025-05-13T16:35:00", "arrTime": "2025-05-13T19:50:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/8/2025 1:45:57 PM"}, {"key": "16087476:181237:2025-05-18T04:35:00 PM", "LFID": 16087476, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-05-18T16:35:00", "depTime": "2025-05-18T16:35:00", "arrTime": "2025-05-18T19:50:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "CLOSED", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/14/2025 7:38:54 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:22", "billDate": "2025-05-03T09:30:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-03T09:30:06"}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1330187990, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330187990:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1330187991, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330187991:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1330187992, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330187992:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 320, "curr": "AED", "originalAmt": 320, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "FZ 713 DXB-TBS 08May2025 Thu 01:40 05:05\r\n", "reasonID": 0, "channelID": 1, "basePoints": 250, "tierPoints": 250, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 320, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1330187995, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:28:01", "billDate": "2025-05-03T09:29:04", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330187995:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1330187993, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1330187994, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181207", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 24.15, "curr": "AED", "originalAmt": 24.15, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:22", "billDate": "2025-05-03T09:30:06", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 24.15, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-03T09:30:06"}, {"chargeID": **********, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": **********, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": **********, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1330187999, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330187999:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1330188000, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330188000:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1343647804, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343647799, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "YQ: YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330188000, "paymentMap": [{"key": "1343647804:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1343647805, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343647799, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "ZR: Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330187999, "paymentMap": [{"key": "1343647805:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1343647806, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1343647799, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "GE: Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1343647806:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1343647807, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1343647799, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1343647807:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 615, "curr": "AED", "originalAmt": 615, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "FZ 712 TBS-DXB 13May2025 Tue 16:35 19:45\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 615, "approveCode": 0}]}, {"chargeID": 1343647799, "codeType": "AIR", "amt": -615, "curr": "AED", "originalAmt": -615, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:30", "desc": "FZ 712 TBS-DXB 13May2025 Tue 16:35 19:45\r\n", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1343647799:*********", "paymentID": *********, "amt": -615, "approveCode": 0}]}, {"chargeID": 1330188004, "codeType": "BCHG", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:28:01", "billDate": "2025-05-03T09:29:04", "desc": "Special Service Request", "comment": "BOOKING CHARGES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330188004:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1330188003, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343647801, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1343647799, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "INST: Included seat", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330188003, "paymentMap": []}, {"chargeID": 1330188001, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1343647808, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1343647799, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330188001, "paymentMap": []}, {"chargeID": 1330188002, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-03T09:29:04", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1343647803, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343647799, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 1, "exchRateDate": "2025-05-03T09:29:03", "billDate": "2025-05-13T06:40:31", "desc": "MLIN: Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1330188002, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 3, "charges": [{"chargeID": 1343647870, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:37:31", "billDate": "2025-05-13T06:40:31", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343647870:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-13T06:40:31"}, {"chargeID": 1343647830, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1343647829, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343647830:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1343647831, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1343647829, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343647831:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343647832, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1343647829, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343647832:*********", "paymentID": *********, "amt": 185, "approveCode": 0}, {"key": "1343647832:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1343647836, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1343647829, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343647836:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1343647829, "codeType": "AIR", "amt": 900, "curr": "AED", "originalAmt": 900, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "FZ 712 TBS-DXB 18May2025 Sun 16:35 19:45\r\n", "reasonID": 2, "channelID": 1, "basePoints": 500, "tierPoints": 500, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1343647829:*********", "paymentID": *********, "amt": 900, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1343652209, "codeType": "PMNT", "amt": 11.42, "curr": "AED", "originalAmt": 11.42, "originalCurr": "AED", "status": 1, "billDate": "2025-05-13T06:43:38", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343652209:*********", "paymentID": *********, "amt": 11.42, "approveCode": 0}]}, {"chargeID": 1343647861, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1343647861:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1343647854, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1343647829, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "INST: Included seat", "comment": "INST: Included seat", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1343647839, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1343647829, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1343647847, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1343647829, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-13T06:40:31", "billDate": "2025-05-13T06:40:31", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181237", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}