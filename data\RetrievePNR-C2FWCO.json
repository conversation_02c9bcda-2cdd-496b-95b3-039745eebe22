{"seriesNum": "299", "PNR": "C2FWCO", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "82703308", "bookDate": "2025-05-10T12:08:04", "modifyDate": "2025-05-10T12:53:55", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "04c12e01v540wiq2u8e49e6bu5z84aa4af8dc875d9a7", "securityGUID": "04c12e01v540wiq2u8e49e6bu5z84aa4af8dc875d9a7", "lastLoadGUID": "d0b71d03-19c2-487f-a43c-525d69376d7e", "isAsyncPNR": false, "MasterPNR": "C2FWCO", "segments": [{"segKey": "16087532:16087532:5/15/2025 9:15:00 PM", "LFID": 16087532, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087532", "org": "DXB", "dest": "IST", "depTime": "2025-05-15T21:15:00", "depTimeGMT": "2025-05-15T17:15:00", "arrTime": "2025-05-16T01:10:00", "operCarrier": "FZ", "operFlightNum": "755", "mrktCarrier": "FZ", "mrktFlightNum": "755", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181283, "depDate": "2025-05-15T21:15:00", "legKey": "16087532:181283:5/15/2025 9:15:00 PM", "customerKey": "3641DDCA88327068FAA2CF52922B79FD06D915CD19F20370DC67DB485C3C3EE5"}], "active": true, "changeType": "AC"}, {"segKey": "16087478:16087478:5/15/2025 1:30:00 PM", "LFID": 16087478, "depDate": "2025-05-15T00:00:00", "flightGroupId": "16087478", "org": "DXB", "dest": "IST", "depTime": "2025-05-15T13:30:00", "depTimeGMT": "2025-05-15T09:30:00", "arrTime": "2025-05-15T17:25:00", "operCarrier": "FZ", "operFlightNum": "727", "mrktCarrier": "FZ ", "mrktFlightNum": "727", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181239, "depDate": "2025-05-15T13:30:00", "legKey": "16087478:181239:5/15/2025 1:30:00 PM", "customerKey": "63EEF34714B5B5D27474D80ADDC1A4A08CB24ADEA5BB9F99AE83B044D449E18C"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268505755, "fName": "MOHAMMADALI", "lName": "SALEHI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "K", "insuPurchasedate": "5/10/2025 12:08:04 PM", "provider": "<PERSON>", "status": 0, "fareClass": "K", "operFareClass": "K", "FBC": "KOB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "TA7JK-J3B4T-INS", "insuTransID": "TA7JK-J3B4T-INS/b62d7bae-50b6-4a90-9833-de29c34ce008", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f40eb000778000000892a#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-10T12:08:04"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ahmed.elamin", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/10/2025 12:08:04 PM", "provider": "<PERSON>", "status": 5, "fareClass": "K", "operFareClass": "K", "FBC": "KOB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "TA7JK-J3B4T-INS/b62d7bae-50b6-4a90-9833-de29c34ce008", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681f4bc30007780000009a1f#268505755#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-10T12:52:59"}]}], "payments": [{"paymentID": *********, "paxID": 268509660, "method": "IPAY", "status": "1", "paidDate": "2025-05-10T12:53:48", "cardNum": "************5546", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 226.6, "baseCurr": "AED", "baseAmt": 226.6, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON> G", "authCode": "T54060", "reference": "22951519", "externalReference": "22951519", "tranId": "21323028", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21323028}, {"paymentID": *********, "paxID": 268505783, "method": "IPAY", "status": "1", "paidDate": "2025-05-10T12:08:08", "cardNum": "************5546", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 835.02, "baseCurr": "AED", "baseAmt": 835.02, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON> G", "authCode": "T49328", "reference": "22951022", "externalReference": "22951022", "tranId": "21322265", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21322265}], "OAFlights": null, "physicalFlights": [{"key": "16087478:181239:2025-05-15T01:30:00 PM", "LFID": 16087478, "PFID": 181239, "org": "DXB", "dest": "IST", "depDate": "2025-05-15T13:30:00", "depTime": "2025-05-15T13:30:00", "arrTime": "2025-05-15T17:25:00", "carrier": "FZ", "flightNum": "727", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "727", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:55:32 AM"}, {"key": "16087532:181283:2025-05-15T09:15:00 PM", "LFID": 16087532, "PFID": 181283, "org": "DXB", "dest": "IST", "depDate": "2025-05-15T21:15:00", "depTime": "2025-05-15T21:15:00", "arrTime": "2025-05-16T01:10:00", "carrier": "FZ", "flightNum": "755", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "755", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:56:28 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1340012226, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012226:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-10T12:08:04"}, {"chargeID": 1340070445, "codeType": "INSU", "taxChargeID": 1340070439, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:53:00", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012226, "paymentMap": [{"key": "1340070445:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1340012221, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1340012217, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012221:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1340012220, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340012217, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012220:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1340012223, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1340012217, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012223:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340012222, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340012217, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012222:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1340012219, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340012217, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012219:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340070440, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1340070439, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:52:59", "desc": "Passenger Service Charge (Intl)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012221, "paymentMap": [{"key": "1340070440:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1340070442, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340070439, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:53:00", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012220, "paymentMap": [{"key": "1340070442:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1340070443, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340070439, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:53:00", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012219, "paymentMap": [{"key": "1340070443:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1340070446, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1340070439, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:53:00", "desc": "Passengers Security & Safety Service Fees", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012223, "paymentMap": [{"key": "1340070446:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1340070447, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340070439, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:53:00", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012222, "paymentMap": [{"key": "1340070447:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1340012217, "codeType": "AIR", "amt": 455, "curr": "AED", "originalAmt": 455, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:08:04", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340012217:*********", "paymentID": *********, "amt": 455, "approveCode": 0}]}, {"chargeID": 1340070439, "codeType": "AIR", "amt": -455, "curr": "AED", "originalAmt": -455, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:52:59", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012217, "paymentMap": [{"key": "1340070439:*********", "paymentID": *********, "amt": -455, "approveCode": 0}]}, {"chargeID": 1340013276, "codeType": "PMNT", "amt": 24.32, "curr": "AED", "originalAmt": 24.32, "originalCurr": "AED", "status": 0, "billDate": "2025-05-10T12:08:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340013276:*********", "paymentID": *********, "amt": 24.32, "approveCode": 0}]}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "CancelNoRefund FZ 727 DXB - IST 15.05.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1340012218, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1340012217, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340070441, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1340070439, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:52:59", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012218, "paymentMap": []}, {"chargeID": 1340012227, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:08:04", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181239"}, {"chargeID": 1340070444, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1340070439, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-10T12:53:00", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1340012227, "paymentMap": [], "PFID": "181239"}]}, {"recNum": 2, "charges": [{"chargeID": 1340070459, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:51:20", "billDate": "2025-05-10T12:53:00", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070459:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-10T12:53:00"}, {"chargeID": 1340070451, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1340070449, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070451:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1340070452, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1340070449, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070452:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340070453, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1340070449, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070453:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1340070454, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1340070449, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070454:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1340070455, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1340070449, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070455:*********", "paymentID": *********, "amt": 124.3, "approveCode": 0}, {"key": "1340070455:*********", "paymentID": *********, "amt": 65.7, "approveCode": 0}]}, {"chargeID": 1340070449, "codeType": "AIR", "amt": 465, "curr": "AED", "originalAmt": 465, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "FZ 755 DXB-IST 15May2025 Thu 21:15 01:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070449:*********", "paymentID": *********, "amt": 465, "approveCode": 0}]}, {"chargeID": 1340071263, "codeType": "PMNT", "amt": 6.6, "curr": "AED", "originalAmt": 6.6, "originalCurr": "AED", "status": 1, "billDate": "2025-05-10T12:53:55", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340071263:*********", "paymentID": *********, "amt": 6.6, "approveCode": 0}]}, {"chargeID": 1340070458, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340070458:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1340070456, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1340070449, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1340070457, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1340070449, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-10T12:53:00", "billDate": "2025-05-10T12:53:00", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181283"}]}], "parentPNRs": [], "childPNRs": []}