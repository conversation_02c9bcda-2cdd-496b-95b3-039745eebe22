{"seriesNum": "299", "PNR": "J5KGAD", "bookAgent": "ANDROID_APP", "resCurrency": "AED", "PNRPin": "83312809", "bookDate": "2025-06-01T11:51:51", "modifyDate": "2025-06-01T11:52:22", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 4, "activeSegCount": 2, "webBookingID": "3e1444l4zeh2ubueh9td64x9s8g1a814ef9b13f84acd", "securityGUID": "3e1444l4zeh2ubueh9td64x9s8g1a814ef9b13f84acd", "lastLoadGUID": "5bf6527a-c289-49a8-a23e-aa3885035d35", "isAsyncPNR": false, "MasterPNR": "J5KGAD", "segments": [{"segKey": "16743841:16743841:7/21/2025 9:20:00 AM", "LFID": 16743841, "depDate": "2025-07-21T00:00:00", "flightGroupId": "16743841", "org": "DXB", "dest": "SZG", "depTime": "2025-07-21T09:20:00", "depTimeGMT": "2025-07-21T05:20:00", "arrTime": "2025-07-21T14:00:00", "operCarrier": "FZ", "operFlightNum": "1449", "mrktCarrier": "FZ ", "mrktFlightNum": "1449", "persons": [{"recNum": 2, "status": 1}, {"recNum": 3, "status": 1}, {"recNum": 4, "status": 1}, {"recNum": 1, "status": 1}], "legDetails": [{"PFID": 185614, "depDate": "2025-07-21T09:20:00", "legKey": "16743841:185614:7/21/2025 9:20:00 AM", "customerKey": "8444195869BEA635C84B06879CF50754247134AE19715676646C0A56C62CBA53"}], "active": true}, {"segKey": "16746117:16746117:7/28/2025 3:00:00 PM", "LFID": 16746117, "depDate": "2025-07-28T00:00:00", "flightGroupId": "16746117", "org": "SZG", "dest": "DXB", "depTime": "2025-07-28T15:00:00", "depTimeGMT": "2025-07-28T13:00:00", "arrTime": "2025-07-28T22:55:00", "operCarrier": "FZ", "operFlightNum": "1450", "mrktCarrier": "FZ ", "mrktFlightNum": "1450", "persons": [{"recNum": 7, "status": 1}, {"recNum": 8, "status": 1}, {"recNum": 5, "status": 1}, {"recNum": 6, "status": 1}], "legDetails": [{"PFID": 185620, "depDate": "2025-07-28T15:00:00", "legKey": "16746117:185620:7/28/2025 3:00:00 PM", "customerKey": "132D479B1E8ACA0E1B6AD266C2675E121CD66E2AA96E1749B32E495AC3BB7892"}], "active": true}], "persons": [{"paxID": 270861420, "fName": "MOHAMED", "lName": "ALZEYOUDI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [2, 7]}, {"paxID": 270861421, "fName": "MARYAM", "lName": "ALHANTOOBI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [3, 8]}, {"paxID": 270861422, "fName": "DANAH", "lName": "ALZEYOUDI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [4, 5]}, {"paxID": 270861423, "fName": "WARWAH", "lName": "ALZEYOUDI", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 6]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683c3b36000777000001fecc#4#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683c3b36000777000001fecc#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683c3b36000777000001fecc#2#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683c3b36000777000001fecc#3#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683c3b36000777000001fecc#3#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683c3b36000777000001fecc#4#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683c3b36000777000001fecc#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "M", "insuPurchasedate": "6/1/2025 11:51:52 AM", "provider": "<PERSON>", "status": 1, "fareClass": "M", "operFareClass": "M", "FBC": "MRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "ETLAB-B9S6X-INS/c5ae4dbc-a947-4768-a139-875cc59409dc", "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683c3b36000777000001fecc#2#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-06-01T11:51:51"}]}], "payments": [{"paymentID": *********, "paxID": 270861466, "method": "MSCD", "status": "1", "paidDate": "2025-06-01T11:52:19", "cardNum": "************9167", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 12401.82, "baseCurr": "AED", "baseAmt": 12401.82, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "mohamed <PERSON>", "authCode": "429511", "reference": "23384249", "externalReference": "23384249", "tranId": "21764096", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21764096}], "OAFlights": null, "physicalFlights": [{"key": "16743841:185614:2025-07-21T09:20:00 AM", "LFID": 16743841, "PFID": 185614, "org": "DXB", "dest": "SZG", "depDate": "2025-07-21T09:20:00", "depTime": "2025-07-21T09:20:00", "arrTime": "2025-07-21T14:00:00", "carrier": "FZ", "flightNum": "1449", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1449", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "SZG", "operatingCarrier": "FZ", "flightDuration": 24000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "SALZBURG", "isActive": true}, {"key": "16746117:185620:2025-07-28T03:00:00 PM", "LFID": 16746117, "PFID": 185620, "org": "SZG", "dest": "DXB", "depDate": "2025-07-28T15:00:00", "depTime": "2025-07-28T15:00:00", "arrTime": "2025-07-28T22:55:00", "carrier": "FZ", "flightNum": "1450", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1450", "flightStatus": "OPEN", "originMetroGroup": "SZG", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 21300, "reaccomChangeAlert": false, "originName": "SALZBURG", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": 1371487185, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487185:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487118, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1371487112, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487118:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487115, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487112, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487115:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487117, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371487112, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487117:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1371487116, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1371487112, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487116:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1371487113, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487112, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487113:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487112, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487112:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371489885, "codeType": "PMNT", "amt": 361.22, "curr": "AED", "originalAmt": 361.22, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:52:22", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371489885:*********", "paymentID": *********, "amt": 361.22, "approveCode": 0}]}, {"chargeID": 1371487119, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487112, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487114, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487112, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487200, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185614"}]}, {"recNum": 7, "charges": [{"chargeID": 1371487187, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487187:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487122, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487121, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487122:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487124, "codeType": "TAX", "taxID": 12693, "taxCode": "QD", "taxChargeID": 1371487121, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Air Transport Levy - International", "comment": "Air Transport Levy - International", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487124:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1371487125, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487121, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487125:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487126, "codeType": "TAX", "taxID": 12694, "taxCode": "AT", "taxChargeID": 1371487121, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Security Charge (International)", "comment": "Passenger Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487126:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1371487127, "codeType": "TAX", "taxID": 12695, "taxCode": "ZY", "taxChargeID": 1371487121, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487127:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1371487121, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487121:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487128, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487121, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487123, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487121, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487201, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185620"}]}, {"recNum": 3, "charges": [{"chargeID": 1371487189, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487189:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487135, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371487130, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487135:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1371487134, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1371487130, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487134:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1371487136, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1371487130, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487136:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487131, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487130, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487131:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487133, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487130, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487133:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487130, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487130:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487137, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487130, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487132, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487130, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487202, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185614"}]}, {"recNum": 8, "charges": [{"chargeID": 1371487191, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487191:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487140, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487139, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487140:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487143, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487139, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487143:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487145, "codeType": "TAX", "taxID": 12695, "taxCode": "ZY", "taxChargeID": 1371487139, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487145:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1371487144, "codeType": "TAX", "taxID": 12694, "taxCode": "AT", "taxChargeID": 1371487139, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Security Charge (International)", "comment": "Passenger Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487144:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1371487142, "codeType": "TAX", "taxID": 12693, "taxCode": "QD", "taxChargeID": 1371487139, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Air Transport Levy - International", "comment": "Air Transport Levy - International", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487142:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1371487139, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487139:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487146, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487139, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487141, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487139, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487203, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185620"}]}, {"recNum": 4, "charges": [{"chargeID": 1371487193, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487193:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487149, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487148, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487149:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487154, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1371487148, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487154:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487152, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1371487148, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487152:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1371487151, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487148, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487151:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487153, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371487148, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487153:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1371487148, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487148:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487155, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487148, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487150, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487148, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487204, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185614"}]}, {"recNum": 5, "charges": [{"chargeID": 1371487195, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487195:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487160, "codeType": "TAX", "taxID": 12693, "taxCode": "QD", "taxChargeID": 1371487157, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Air Transport Levy - International", "comment": "Air Transport Levy - International", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487160:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1371487162, "codeType": "TAX", "taxID": 12694, "taxCode": "AT", "taxChargeID": 1371487157, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Security Charge (International)", "comment": "Passenger Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487162:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1371487163, "codeType": "TAX", "taxID": 12695, "taxCode": "ZY", "taxChargeID": 1371487157, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487163:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1371487161, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487157, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487161:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487158, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487157, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487158:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487157, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487157:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487164, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487157, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487159, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487157, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487205, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185620"}]}, {"recNum": 1, "charges": [{"chargeID": 1371487197, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487197:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487167, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487166, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487167:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487169, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487166, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487169:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487170, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1371487166, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487170:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1371487172, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1371487166, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487172:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487171, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1371487166, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487171:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1371487166, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487166:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487173, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487166, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487168, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487166, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487206, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185614"}]}, {"recNum": 6, "charges": [{"chargeID": 1371487199, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487199:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"49.18\",\r\n  \"Tax\": \"2.34\",\r\n  \"SegPaxCount\": \"8\"\r\n}", "ChargeBookDate": "2025-06-01T11:51:51"}, {"chargeID": 1371487176, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1371487175, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487176:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1371487180, "codeType": "TAX", "taxID": 12694, "taxCode": "AT", "taxChargeID": 1371487175, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Security Charge (International)", "comment": "Passenger Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487180:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1371487179, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1371487175, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487179:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1371487181, "codeType": "TAX", "taxID": 12695, "taxCode": "ZY", "taxChargeID": 1371487175, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Passenger Service Charge (International)", "comment": "Passenger Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487181:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1371487178, "codeType": "TAX", "taxID": 12693, "taxCode": "QD", "taxChargeID": 1371487175, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Air Transport Levy - International", "comment": "Air Transport Levy - International", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487178:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1371487175, "codeType": "AIR", "amt": 1025, "curr": "AED", "originalAmt": 1025, "originalCurr": "AED", "status": 1, "billDate": "2025-06-01T11:51:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1371487175:*********", "paymentID": *********, "amt": 1025, "approveCode": 0}]}, {"chargeID": 1371487182, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1371487175, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487177, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1371487175, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1371487207, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-01T11:51:51", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185620"}]}], "parentPNRs": [], "childPNRs": []}