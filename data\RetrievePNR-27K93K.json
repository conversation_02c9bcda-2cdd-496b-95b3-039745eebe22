{"seriesNum": "299", "PNR": "27K93K", "bookAgent": "apimmtuae", "IATA": "96008168", "resCurrency": "AED", "PNRPin": "83238190", "bookDate": "2025-05-29T11:41:16", "modifyDate": "2025-05-29T20:47:42", "resType": "TPAPI", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "6890c24an5ofh38fu2m4d8677ak949eb8ee62e026e41", "securityGUID": "6890c24an5ofh38fu2m4d8677ak949eb8ee62e026e41", "lastLoadGUID": "397a3cfc-e7c1-4134-ab3f-23b1c54813aa", "isAsyncPNR": false, "MasterPNR": "27K93K", "segments": [{"segKey": "16087922:16087922:6/6/2025 9:40:00 PM", "LFID": 16087922, "depDate": "2025-06-06T00:00:00", "flightGroupId": "16087922", "org": "DXB", "dest": "EVN", "depTime": "2025-06-06T21:40:00", "depTimeGMT": "2025-06-06T17:40:00", "arrTime": "2025-06-07T00:50:00", "operCarrier": "FZ", "operFlightNum": "715", "mrktCarrier": "FZ ", "mrktFlightNum": "715", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181656, "depDate": "2025-06-06T21:40:00", "legKey": "16087922:181656:6/6/2025 9:40:00 PM", "customerKey": "D9694D063915011ECB0F176FF6501F5946DBB4E48439AC796583F3F17D068E72"}], "active": true, "changeType": "TK"}, {"segKey": "16087488:16087488:6/10/2025 12:25:00 PM", "LFID": 16087488, "depDate": "2025-06-10T00:00:00", "flightGroupId": "16087488", "org": "EVN", "dest": "DXB", "depTime": "2025-06-10T12:25:00", "depTimeGMT": "2025-06-10T08:25:00", "arrTime": "2025-06-10T15:30:00", "operCarrier": "FZ", "operFlightNum": "718", "mrktCarrier": "FZ ", "mrktFlightNum": "718", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181219, "depDate": "2025-06-10T12:25:00", "legKey": "16087488:181219:6/10/2025 12:25:00 PM", "customerKey": "3EB5458DE2DD2A045D1181F181D282D0D527CEDF7C682AA159231D64FDA70D2D"}], "active": true, "changeType": "TK"}, {"segKey": "16087922:16087922:6/5/2025 9:40:00 PM", "LFID": 16087922, "depDate": "2025-06-05T00:00:00", "flightGroupId": "16087922", "org": "DXB", "dest": "EVN", "depTime": "2025-06-05T21:40:00", "depTimeGMT": "2025-06-05T17:40:00", "arrTime": "2025-06-06T00:50:00", "operCarrier": "FZ", "operFlightNum": "715", "mrktCarrier": "FZ", "mrktFlightNum": "715", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181656, "depDate": "2025-06-05T21:40:00", "legKey": "16087922:181656:6/5/2025 9:40:00 PM", "customerKey": "FFE7A8D362DF342D393F5048824F58DA182B0B1A00B8C2D2E10FBC21AE4E2505"}], "active": true, "changeType": "TK"}, {"segKey": "16087900:16087900:6/9/2025 1:50:00 AM", "LFID": 16087900, "depDate": "2025-06-09T00:00:00", "flightGroupId": "16087900", "org": "EVN", "dest": "DXB", "depTime": "2025-06-09T01:50:00", "depTimeGMT": "2025-06-08T21:50:00", "arrTime": "2025-06-09T05:00:00", "operCarrier": "FZ", "operFlightNum": "716", "mrktCarrier": "FZ", "mrktFlightNum": "716", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181670, "depDate": "2025-06-09T01:50:00", "legKey": "16087900:181670:6/9/2025 1:50:00 AM", "customerKey": "30CEF874BD0B39A6DCF3710AD53B3FF49F06A8EA56CC58932A31938A1CB735BB"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270561154, "fName": "SHEMS", "lName": "MANSOOR", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1987-02-27T00:00:00", "recNum": [1, 2, 3, 4]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "apimmtuae", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "H", "insuPurchasedate": "5/29/2025 11:49:27 AM", "provider": "<PERSON>", "status": 0, "fareClass": "H", "operFareClass": "H", "FBC": "HRL7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "NSBFZ-HTBUF-INS", "insuTransID": "NSBFZ-HTBUF-INS/91989677-26d6-4c16-9e96-5e2793a00062", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683846b30007780000010095#1#1#TPAPI#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 5, "cancelReasonID": 1, "bookDate": "2025-05-29T11:41:16"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "apimmtuae", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "M", "insuPurchasedate": "5/29/2025 11:49:27 AM", "provider": "<PERSON>", "status": 0, "fareClass": "M", "operFareClass": "M", "FBC": "MRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "NSBFZ-HTBUF-INS", "insuTransID": "NSBFZ-HTBUF-INS/91989677-26d6-4c16-9e96-5e2793a00062", "toRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683846b30007780000010095#1#2#TPAPI#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 5, "cancelReasonID": 1, "bookDate": "2025-05-29T11:41:16"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "<PERSON><PERSON><PERSON>a<PERSON><PERSON>", "statusReasonID": 0, "markFareClass": "W", "insuPurchasedate": "5/29/2025 11:49:27 AM", "provider": "<PERSON>", "status": 1, "fareClass": "W", "operFareClass": "W", "FBC": "WRL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "NSBFZ-HTBUF-INS", "insuTransID": "NSBFZ-HTBUF-INS/91989677-26d6-4c16-9e96-5e2793a00062", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6838c5f10007780000015bb6#270561154#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-29T20:41:54"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "<PERSON><PERSON><PERSON>a<PERSON><PERSON>", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/29/2025 11:49:27 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "NSBFZ-HTBUF-INS", "insuTransID": "NSBFZ-HTBUF-INS/91989677-26d6-4c16-9e96-5e2793a00062", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6838c5f10007780000015bb6#270561154#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-29T20:41:54"}]}], "payments": [{"paymentID": *********, "paxID": 270617233, "method": "VISA", "status": "1", "paidDate": "2025-05-29T20:47:37", "cardNum": "************1916", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1672.72, "baseCurr": "AED", "baseAmt": 1672.72, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "145152", "reference": "23330227", "externalReference": "23330227", "tranId": "21715443", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21715443}, {"paymentID": 210751469, "paxID": 270617228, "method": "IPAY", "status": "2", "paidDate": "2025-05-29T20:45:06", "cardNum": "************7942", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1672.72, "baseCurr": "AED", "baseAmt": 1672.72, "userID": "paybylink", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON>", "reference": "23330219", "externalReference": "23330219", "tranId": "21715443", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21715443}, {"paymentID": *********, "paxID": 149578395, "method": "INVC", "status": "1", "paidDate": "2025-05-29T11:41:18", "IATANum": "96008168", "paidCurr": "AED", "paidAmt": 1795, "baseCurr": "AED", "baseAmt": 1795, "userID": "apimmtuae", "channelID": 5, "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1", "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 270562350, "method": "VISA", "status": "1", "paidDate": "2025-05-29T11:50:17", "cardNum": "************2730", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 358.13, "baseCurr": "AED", "baseAmt": 358.13, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "429520", "reference": "23321931", "externalReference": "23321931", "tranId": "21705234", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21705234}], "OAFlights": null, "physicalFlights": [{"key": "16087922:181656:2025-06-05T09:40:00 PM", "LFID": 16087922, "PFID": 181656, "org": "DXB", "dest": "EVN", "depDate": "2025-06-05T21:40:00", "depTime": "2025-06-05T21:40:00", "arrTime": "2025-06-06T00:50:00", "carrier": "FZ", "flightNum": "715", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "715", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "EVN", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Yerevan", "isActive": true, "changeType": "TK", "flightChangeTime": "1/24/2025 12:13:09 PM"}, {"key": "16087922:181656:2025-06-06T09:40:00 PM", "LFID": 16087922, "PFID": 181656, "org": "DXB", "dest": "EVN", "depDate": "2025-06-06T21:40:00", "depTime": "2025-06-06T21:40:00", "arrTime": "2025-06-07T00:50:00", "carrier": "FZ", "flightNum": "715", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "715", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "EVN", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Yerevan", "isActive": true, "changeType": "TK", "flightChangeTime": "1/24/2025 12:13:09 PM"}, {"key": "16087900:181670:2025-06-09T01:50:00 AM", "LFID": 16087900, "PFID": 181670, "org": "EVN", "dest": "DXB", "depDate": "2025-06-09T01:50:00", "depTime": "2025-06-09T01:50:00", "arrTime": "2025-06-09T05:00:00", "carrier": "FZ", "flightNum": "716", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "716", "flightStatus": "OPEN", "originMetroGroup": "EVN", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Yerevan", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "5/16/2025 12:54:24 PM"}, {"key": "16087488:181219:2025-06-10T12:25:00 PM", "LFID": 16087488, "PFID": 181219, "org": "EVN", "dest": "DXB", "depDate": "2025-06-10T12:25:00", "depTime": "2025-06-10T12:25:00", "arrTime": "2025-06-10T15:30:00", "carrier": "FZ", "flightNum": "718", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "718", "flightStatus": "OPEN", "originMetroGroup": "EVN", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11100, "reaccomChangeAlert": false, "originName": "Yerevan", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 7:46:25 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1368161791, "codeType": "INSU", "taxChargeID": 1368161789, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T20:41:54", "desc": "INSU", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367425223, "paymentMap": [{"key": "1368161791:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T20:41:54"}, {"chargeID": 1367425223, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T11:49:27", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367425223:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1368161792, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1368161789, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:54", "desc": "Passenger Facilities Charge.", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414816, "paymentMap": [{"key": "1368161792:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1368161793, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1368161789, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Advanced passenger information fee", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414815, "paymentMap": [{"key": "1368161793:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1368161795, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1368161789, "amt": -120, "curr": "AED", "originalAmt": -120, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "YQ - DUMMY", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414811, "paymentMap": [{"key": "1368161795:*********", "paymentID": *********, "amt": -120, "approveCode": 0}]}, {"chargeID": 1368161796, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1368161789, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Passenger Service Charge (Intl)", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414810, "paymentMap": [{"key": "1368161796:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1368161798, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1368161789, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Passengers Security & Safety Service Fees", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414813, "paymentMap": [{"key": "1368161798:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1367414813, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1367414809, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414813:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367414810, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1367414809, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414810:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1367414811, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367414809, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414811:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1367414815, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367414809, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414815:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367414816, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1367414809, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414816:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1368161789, "codeType": "AIR", "amt": -305, "curr": "AED", "originalAmt": -305, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T20:41:54", "desc": "WEB:AIR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414809, "paymentMap": [{"key": "1368161789:*********", "paymentID": *********, "amt": -305, "approveCode": 0}]}, {"chargeID": 1367414809, "codeType": "AIR", "amt": 305, "curr": "AED", "originalAmt": 305, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T11:41:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414809:*********", "paymentID": *********, "amt": 305, "approveCode": 0}]}, {"chargeID": 1367432773, "codeType": "PMNT", "amt": 10.43, "curr": "AED", "originalAmt": 10.43, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T11:50:19", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367432773:*********", "paymentID": *********, "amt": 10.43, "approveCode": 0}]}, {"chargeID": 1367414812, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1367414809, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414812:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1368161790, "codeType": "XLGR", "taxChargeID": 1368161789, "amt": -156, "curr": "AED", "originalAmt": -156, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T20:41:54", "desc": "XLGR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367425196, "paymentMap": [{"key": "1368161790:*********", "paymentID": *********, "amt": -156, "approveCode": 0}], "PFID": "181656"}, {"chargeID": 1367425196, "codeType": "XLGR", "amt": 156, "curr": "AED", "originalAmt": 156, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T11:49:27", "desc": "XLGR", "comment": "FLXID:XLGR_EMER_ZONE3_MID::181656", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367425196:*********", "paymentID": *********, "amt": 156, "approveCode": 0}], "PFID": "181656"}, {"chargeID": 1*********, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "CancelNoRefund FZ 715 DXB - EVN 06.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1*********:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1368161794, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1368161789, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "30kg BAG INCLUDED IN FARE", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414814, "paymentMap": []}, {"chargeID": 1367414814, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1367414809, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1368161797, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1368161789, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Standard meal", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414827, "paymentMap": [], "PFID": "181656"}, {"chargeID": 1367414827, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181656"}]}, {"recNum": 2, "charges": [{"chargeID": 1368161806, "codeType": "INSU", "taxChargeID": 1368161800, "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T20:41:55", "desc": "INSU", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367425224, "paymentMap": [{"key": "1368161806:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T20:41:55"}, {"chargeID": 1367425224, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T11:49:27", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367425224:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"2\"}"}, {"chargeID": 1368161801, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1368161800, "amt": -120, "curr": "AED", "originalAmt": -120, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "YQ - DUMMY", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414821, "paymentMap": [{"key": "1368161801:*********", "paymentID": *********, "amt": -120, "approveCode": 0}]}, {"chargeID": 1368161802, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1368161800, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Advanced passenger information fee", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414825, "paymentMap": [{"key": "1368161802:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1368161804, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1368161800, "amt": -120, "curr": "AED", "originalAmt": -120, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Passenger Airport And Security Charge", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414823, "paymentMap": [{"key": "1368161804:*********", "paymentID": *********, "amt": -120, "approveCode": 0}]}, {"chargeID": 1368161808, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1368161800, "amt": -100, "curr": "AED", "originalAmt": -100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "State Tax", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414820, "paymentMap": [{"key": "1368161808:*********", "paymentID": *********, "amt": -100, "approveCode": 0}]}, {"chargeID": 1367414823, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1367414819, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Passenger Airport And Security Charge", "comment": "Passenger Airport And Security Charge", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414823:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1367414820, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1367414819, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "State Tax", "comment": "State Tax", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414820:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1367414825, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367414819, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414825:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367414821, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367414819, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414821:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1368161800, "codeType": "AIR", "amt": -875, "curr": "AED", "originalAmt": -875, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T20:41:55", "desc": "WEB:AIR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414819, "paymentMap": [{"key": "1368161800:*********", "paymentID": *********, "amt": -875, "approveCode": 0}]}, {"chargeID": 1367414819, "codeType": "AIR", "amt": 875, "curr": "AED", "originalAmt": 875, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T11:41:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414819:*********", "paymentID": *********, "amt": 875, "approveCode": 0}]}, {"chargeID": 1367414822, "codeType": "TFEE", "taxID": 4247, "taxCode": "TFEE", "taxChargeID": 1367414819, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "TFEE DUMMY", "comment": "TFEE DUMMY", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367414822:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1368161805, "codeType": "XLGR", "taxChargeID": 1368161800, "amt": -156, "curr": "AED", "originalAmt": -156, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T20:41:55", "desc": "XLGR", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367425197, "paymentMap": [{"key": "1368161805:*********", "paymentID": *********, "amt": -156, "approveCode": 0}], "PFID": "181219"}, {"chargeID": 1367425197, "codeType": "XLGR", "amt": 156, "curr": "AED", "originalAmt": 156, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-29T11:49:27", "desc": "XLGR", "comment": "FLXID:XLGR_EMER_ZONE3_MID::181219", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367425197:*********", "paymentID": *********, "amt": 156, "approveCode": 0}], "PFID": "181219"}, {"chargeID": 1368161809, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "CancelNoRefund FZ 718 EVN - DXB 10.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161809:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1368161803, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1368161800, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "30kg BAG INCLUDED IN FARE", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414824, "paymentMap": []}, {"chargeID": 1367414824, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1367414819, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1368161807, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1368161800, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T20:41:55", "desc": "Standard meal", "comment": "M No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367414828, "paymentMap": [], "PFID": "181219"}, {"chargeID": 1367414828, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T11:41:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181219"}]}, {"recNum": 3, "charges": [{"chargeID": 1368161823, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:39:53", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161823:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T20:41:55"}, {"chargeID": 1368161811, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1368161810, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161811:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1368161812, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1368161810, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161812:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1368161813, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1368161810, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161813:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1368161814, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1368161810, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161814:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1368161815, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1368161810, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161815:*********", "paymentID": *********, "amt": 2, "approveCode": 0}, {"key": "1368161815:*********", "paymentID": *********, "amt": 118, "approveCode": 0}]}, {"chargeID": 1368161810, "codeType": "AIR", "amt": 1343, "curr": "AED", "originalAmt": 1343, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "FZ 715 DXB-EVN 05Jun2025 Thu 21:40 00:50\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161810:*********", "paymentID": *********, "amt": 1343, "approveCode": 0}]}, {"chargeID": 1368165807, "codeType": "PMNT", "amt": 48.72, "curr": "AED", "originalAmt": 48.72, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T20:47:42", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368165807:*********", "paymentID": *********, "amt": 48.72, "approveCode": 0}]}, {"chargeID": 1368161818, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161818:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1368161820, "codeType": "XLGR", "amt": 156, "curr": "AED", "originalAmt": 156, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:39:21", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request:XLGR-15B", "comment": "FLXID:XLGR_EMER_ZONE3_MID:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161820:*********", "paymentID": *********, "amt": 156, "approveCode": 0}]}, {"chargeID": 1368161822, "codeType": "XLGR", "amt": 156, "curr": "AED", "originalAmt": 156, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:39:40", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request:XLGR-15E", "comment": "FLXID:XLGR_EMER_ZONE3_MID:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161822:*********", "paymentID": *********, "amt": 82.3, "approveCode": 0}, {"key": "1368161822:*********", "paymentID": *********, "amt": 73.7, "approveCode": 0}], "PFID": "181656"}, {"chargeID": 1368161816, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1368161810, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1368161817, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1368161810, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181656"}]}, {"recNum": 4, "charges": [{"chargeID": 1368161833, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:39:53", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161833:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-29T20:41:55"}, {"chargeID": 1368161825, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1368161824, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161825:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1368161826, "codeType": "TAX", "taxID": 5265, "taxCode": "KC", "taxChargeID": 1368161824, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "KC: Passenger Airport And Security Charge", "comment": "KC: Passenger Airport And Security Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161826:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1368161827, "codeType": "TAX", "taxID": 2086, "taxCode": "AM", "taxChargeID": 1368161824, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "AM: State Tax", "comment": "AM: State Tax", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161827:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1368161828, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1368161824, "amt": 120, "curr": "AED", "originalAmt": 120, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161828:*********", "paymentID": *********, "amt": 120, "approveCode": 0}]}, {"chargeID": 1368161824, "codeType": "AIR", "amt": 885, "curr": "AED", "originalAmt": 885, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "FZ 716 EVN-DXB 09Jun2025 Mon 01:50 05:00\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161824:*********", "paymentID": *********, "amt": 885, "approveCode": 0}]}, {"chargeID": 1368161831, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161831:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1368161832, "codeType": "XLGR", "amt": 156, "curr": "AED", "originalAmt": 156, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:39:47", "billDate": "2025-05-29T20:41:55", "desc": "Special Service Request:XLGR-15B", "comment": "FLXID:XLGR_EMER_ZONE3_MID:\r\nXLGR - Extra legroom seat fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1368161832:*********", "paymentID": *********, "amt": 156, "approveCode": 0}], "PFID": "181670"}, {"chargeID": 1368161829, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1368161824, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "BAGL: 30kg BAG INCLUDED IN FARE", "comment": "BAGL: 30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1368161830, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1368161824, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T20:41:55", "billDate": "2025-05-29T20:41:55", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181670"}]}], "parentPNRs": [], "childPNRs": []}