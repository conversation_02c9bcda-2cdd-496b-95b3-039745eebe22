{"seriesNum": "299", "PNR": "FHBGJY", "bookAgent": "ANDROID_APP", "resCurrency": "QAR", "PNRPin": "83103097", "bookDate": "2025-05-24T17:48:47", "modifyDate": "2025-05-25T13:12:31", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "ed55b40du14bb2lf6cj7l4j37b5580f754d57769f55b", "securityGUID": "ed55b40du14bb2lf6cj7l4j37b5580f754d57769f55b", "lastLoadGUID": "4b931d24-fe51-4e10-b47d-8a93229dd785", "isAsyncPNR": false, "MasterPNR": "FHBGJY", "segments": [{"segKey": "16087271:16087271:5/25/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-05-25T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-05-25T17:05:00", "depTimeGMT": "2025-05-25T14:05:00", "arrTime": "2025-05-25T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ ", "mrktFlightNum": "018", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-25T17:05:00", "legKey": "16087271:181022:5/25/2025 5:05:00 PM", "customerKey": "C54AD54F0FEF44DABF053E61AA9A8222F9C092765103E0A934D44C6F536FA300"}], "active": true, "changeType": "TK"}, {"segKey": "16066159:16066159:6/6/2025 7:40:00 PM", "LFID": 16066159, "depDate": "2025-06-06T00:00:00", "flightGroupId": "16066159", "org": "DXB", "dest": "DOH", "depTime": "2025-06-06T19:40:00", "depTimeGMT": "2025-06-06T15:40:00", "arrTime": "2025-06-06T19:50:00", "operCarrier": "FZ", "operFlightNum": "005", "mrktCarrier": "FZ ", "mrktFlightNum": "005", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181000, "depDate": "2025-06-06T19:40:00", "legKey": "16066159:181000:6/6/2025 7:40:00 PM", "customerKey": "015E650B394CC9ACB60E472E764696426C6D53B52C2CCD2132C0D997D81C533A"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270044303, "fName": "MINKYUNG", "lName": "KANG", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "R", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8QA2", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683205f50007780000017829#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "bookDate": "2025-05-24T17:48:47"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "ANDROID_APP", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/25/2025 9:43:09 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URL8QA2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "PCFT2-JN9U4-INS/4795b688-d556-4c04-ac3f-e8ca2dd0a695", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683205f50007780000017829#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-24T17:48:47"}]}], "payments": [{"paymentID": *********, "paxID": 270044303, "method": "DCSH", "status": "1", "paidDate": "2025-05-25T13:12:30", "IATANum": "95093364", "paidCurr": "QAR", "paidAmt": 1320, "baseCurr": "QAR", "baseAmt": 1320, "userID": "krizia.gonzaga", "channelID": 19, "correlationId": "5976e8ab8aa445nd58h1sa48f85ft64dm3ufef2efe4a", "POSAirport": "DOH", "workStationID": "DOH1CKE802", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "1", "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 270087822, "method": "IPAY", "status": "1", "paidDate": "2025-05-25T09:43:14", "cardNum": "************9839", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 277.52, "baseCurr": "QAR", "baseAmt": 277.52, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "Minkyung Kang", "authCode": "060676", "reference": "23239963", "externalReference": "23239963", "tranId": "21617289", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21617289}, {"paymentID": *********, "paxID": 270044307, "method": "GPAY", "status": "1", "paidDate": "2025-05-24T17:48:53", "cardNum": "************4727", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 2199.05, "baseCurr": "QAR", "baseAmt": 2199.05, "userID": "ANDROID_APP", "channelID": 12, "cardHolderName": "SHAHEEN GHANIM S", "authCode": "017306", "reference": "23228500", "externalReference": "23228500", "tranId": "21609027", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21609027}], "OAFlights": null, "physicalFlights": [{"key": "16087271:181022:2025-05-25T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-25T17:05:00", "depTime": "2025-05-25T17:05:00", "arrTime": "2025-05-25T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:39:50 AM"}, {"key": "16066159:181000:2025-06-06T07:40:00 PM", "LFID": 16066159, "PFID": 181000, "org": "DXB", "dest": "DOH", "depDate": "2025-06-06T19:40:00", "depTime": "2025-06-06T19:40:00", "arrTime": "2025-06-06T19:50:00", "carrier": "FZ", "flightNum": "005", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "005", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4200, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true, "changeType": "TK", "flightChangeTime": "11/29/2024 4:28:29 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1361220002, "codeType": "XBAG", "amt": 1200, "curr": "QAR", "originalAmt": 1200, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-25T13:12:00", "comment": "40.00", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "chargeSequence": "1412801313737", "paymentRefStatus": 1, "paymentMap": [{"key": "1361220002:*********", "paymentID": *********, "amt": 1200, "approveCode": 0}], "PFID": "181022", "POSAirport": "DOH", "workStationID": "DOH1CKE802", "isSSR": true, "parameter1Name": "BATCH_ID", "parameter1Value": "0003d21a033abgr544u6w7t369u8t34934423917ea08", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-25T17:05:00\",\"fltNum\":\"018\",\"depDate\":\"2025-05-25T00:00:00\",\"board\":\"DOH\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1151116928_DOHDXBDXB_XBAG", "ChargeBookDate": "2025-05-25T13:12:00"}, {"chargeID": 1360397837, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1360397831, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397837:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1360397832, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1360397831, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397832:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1360397838, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1360397831, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397838:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1360397835, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1360397831, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397835:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1360397833, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1360397831, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397833:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1360397836, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1360397831, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397836:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1360397831, "codeType": "AIR", "amt": 900, "curr": "QAR", "originalAmt": 900, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-24T17:48:48", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397831:*********", "paymentID": *********, "amt": 900, "approveCode": 0}]}, {"chargeID": 1360398747, "codeType": "PMNT", "amt": 64.05, "curr": "QAR", "originalAmt": 64.05, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-24T17:48:57", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360398747:*********", "paymentID": *********, "amt": 64.05, "approveCode": 0}]}, {"chargeID": 1360966384, "codeType": "PMNT", "amt": 8.08, "curr": "QAR", "originalAmt": 8.08, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-25T09:43:18", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360966384:*********", "paymentID": *********, "amt": 8.08, "approveCode": 0}]}, {"chargeID": 1361220003, "codeType": "GHA", "taxChargeID": 1361220002, "amt": 120, "curr": "QAR", "originalAmt": 120, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-25T13:12:00", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "chargeSequence": "1412801313737", "paymentRefStatus": 1, "paymentMap": [{"key": "1361220003:*********", "paymentID": *********, "amt": 120, "approveCode": 0}], "PFID": "181022", "POSAirport": "DOH", "workStationID": "DOH1CKE802", "parameter1Name": "BATCH_ID", "parameter1Value": "0003d21a033abgr544u6w7t369u8t34934423917ea08", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-25T17:05:00\",\"fltNum\":\"018\",\"depDate\":\"2025-05-25T00:00:00\",\"board\":\"DOH\",\"off\":\"DXB\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1151116928_DOHDXBDXB_XBAG"}, {"chargeID": 1360397851, "codeType": "FRST", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181022", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}, {"chargeID": 1360397839, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1360397831, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1360397834, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1360397831, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1360397854, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}, {"recNum": 2, "charges": [{"chargeID": 1360965774, "codeType": "INSU", "amt": 35.44, "curr": "QAR", "originalAmt": 35.44, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-25T09:43:09", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360965774:*********", "paymentID": *********, "amt": 35.44, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-25T09:43:09"}, {"chargeID": 1360397845, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1360397841, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397845:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1360397844, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1360397841, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397844:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1360397842, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1360397841, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397842:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1360397847, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1360397841, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397847:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1360397846, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1360397841, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397846:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1360397848, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1360397841, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397848:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1360397841, "codeType": "AIR", "amt": 655, "curr": "QAR", "originalAmt": 655, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-24T17:48:48", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397841:*********", "paymentID": *********, "amt": 655, "approveCode": 0}]}, {"chargeID": 1360397853, "codeType": "XLGR", "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181000", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360397853:*********", "paymentID": *********, "amt": 190, "approveCode": 0}], "PFID": "181000"}, {"chargeID": 1360397849, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1360397841, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1360397843, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1360397841, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1360965772, "codeType": "BUPZ", "amt": 234, "curr": "QAR", "originalAmt": 234, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-25T09:43:09", "desc": "BUPZ", "comment": "FLXID:BUPZ_GLOBAL_Hub_Z1:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360965772:*********", "paymentID": *********, "amt": 234, "approveCode": 0}]}, {"chargeID": 1360397855, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-24T17:48:48", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181000"}]}], "parentPNRs": [], "childPNRs": []}