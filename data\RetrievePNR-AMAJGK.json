{"seriesNum": "299", "PNR": "AMAJGK", "bookAgent": "WEB2_LIVE", "resCurrency": "BHD", "PNRPin": "82337695", "bookDate": "2025-04-27T06:27:29", "modifyDate": "2025-05-07T21:13:07", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "6f726f59r2tayfj1u534k369n4zbf9da26d94408e1b6", "securityGUID": "6f726f59r2tayfj1u534k369n4zbf9da26d94408e1b6", "lastLoadGUID": "3491E6E772856823E0631E206F0A2985", "isAsyncPNR": false, "MasterPNR": "AMAJGK", "segments": [{"segKey": "16087282:16087282:5/5/2025 6:30:00 PM", "LFID": 16087282, "depDate": "2025-05-05T00:00:00", "flightGroupId": "16087282", "org": "BAH", "dest": "DXB", "depTime": "2025-05-05T18:30:00", "depTimeGMT": "2025-05-05T15:30:00", "arrTime": "2025-05-05T20:45:00", "operCarrier": "FZ", "operFlightNum": "028", "mrktCarrier": "FZ ", "mrktFlightNum": "028", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181053, "depDate": "2025-05-05T18:30:00", "legKey": "16087282:181053:5/5/2025 6:30:00 PM", "customerKey": "232AE2AD51550F1115C326C1639E6FB04F0C52D421E0AD52CB94F2F8FFF4C1CF"}], "active": true, "changeType": "AC"}, {"segKey": "16087275:16087275:5/7/2025 12:25:00 AM", "LFID": 16087275, "depDate": "2025-05-07T00:00:00", "flightGroupId": "16087275", "org": "DXB", "dest": "BAH", "depTime": "2025-05-07T00:25:00", "depTimeGMT": "2025-05-06T20:25:00", "arrTime": "2025-05-07T00:40:00", "operCarrier": "FZ", "operFlightNum": "029", "mrktCarrier": "FZ ", "mrktFlightNum": "029", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 181046, "depDate": "2025-05-07T00:25:00", "legKey": "16087275:181046:5/7/2025 12:25:00 AM", "customerKey": "1B5FA8692129AF465A57EC3B08F2C56F479408878EC3DB76FA7EE750F14561F5"}], "active": true}], "persons": [{"paxID": 267093905, "fName": "GRAHAM", "lName": "BENNETT", "title": "MR", "PTCID": 1, "gender": "M", "FFNum": "109221582", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "L", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "emergencyContactID": 267811953, "discloseEmergencyContact": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680dcc2c000778000000a604#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-04-27T06:27:29"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "L", "insuPurchasedate": "5/5/2025 3:31:09 AM", "provider": "AIG", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "emergencyContactID": 267990399, "discloseEmergencyContact": 1, "insuTransID": "f746947a-5bf3-4afa-8787-2937bffc0ba4", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "680dcc2c000778000000a604#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-27T06:27:29"}]}], "payments": [{"paymentID": *********, "paxID": 267891014, "method": "MSCD", "status": "1", "paidDate": "2025-05-05T03:37:59", "cardNum": "************8054", "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 6.032, "baseCurr": "BHD", "baseAmt": 6.032, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "T06451", "reference": "22842571", "externalReference": "22842571", "tranId": "21209815", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEBHD", "exchangeRate": "1", "resExternalPaymentID": 21209815}, {"paymentID": *********, "paxID": 267811948, "method": "MSCD", "status": "1", "paidDate": "2025-05-04T05:40:25", "cardNum": "************8054", "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 2.678, "baseCurr": "BHD", "baseAmt": 2.678, "userID": "olci", "channelID": 20, "cardHolderName": "<PERSON>", "authCode": "T04604", "reference": "22824846", "externalReference": "22824846", "tranId": "21193789", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEBHD", "exchangeRate": "1", "resExternalPaymentID": 21193789}, {"paymentID": *********, "paxID": 267093933, "method": "MSCD", "status": "1", "paidDate": "2025-04-27T06:28:05", "cardNum": "************8054", "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 87.55, "baseCurr": "BHD", "baseAmt": 87.55, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON>", "authCode": "T05120", "reference": "22677208", "externalReference": "22677208", "tranId": "21050859", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEBHD", "exchangeRate": "1", "resExternalPaymentID": 21050859}], "OAFlights": null, "physicalFlights": [{"key": "16087282:181053:2025-05-05T06:30:00 PM", "LFID": 16087282, "PFID": 181053, "org": "BAH", "dest": "DXB", "depDate": "2025-05-05T18:30:00", "depTime": "2025-05-05T18:30:00", "arrTime": "2025-05-05T20:45:00", "carrier": "FZ", "flightNum": "028", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "028", "flightStatus": "CLOSED", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "4/11/2025 12:15:53 PM"}, {"key": "16087275:181046:2025-05-07T12:25:00 AM", "LFID": 16087275, "PFID": 181046, "org": "DXB", "dest": "BAH", "depDate": "2025-05-07T00:25:00", "depTime": "2025-05-07T00:25:00", "arrTime": "2025-05-07T00:40:00", "carrier": "FZ", "flightNum": "029", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "029", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BAH", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bahrain", "isActive": false}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 3.556, "curr": "BHD", "originalAmt": 3.556, "originalCurr": "BHD", "status": 1, "exchRate": 1, "billDate": "2025-05-05T03:37:52", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 3.556, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T03:37:52"}, {"chargeID": **********, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 7.7, "curr": "BHD", "originalAmt": 7.7, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 7.7, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1321920076, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920076:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1321920077, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920077:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1321920074, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 3.8, "curr": "BHD", "originalAmt": 3.8, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920074:*********", "paymentID": *********, "amt": 3.8, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 26.5, "curr": "BHD", "originalAmt": 26.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-04-27T06:27:29", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 26.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332023108, "codeType": "NSST", "amt": 2.3, "curr": "BHD", "originalAmt": 2.3, "originalCurr": "BHD", "status": 1, "exchRate": 1, "billDate": "2025-05-05T03:37:52", "desc": "NSST", "comment": "FLXID:NSST_ZONE1_WIN_AIS::181046", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1332023108:*********", "paymentID": *********, "amt": 2.3, "approveCode": 0}], "PFID": "181046", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321920078, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321920083, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181046", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 1, "charges": [{"chargeID": 1321920046, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1321920045, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920046:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-27T06:27:29"}, {"chargeID": 1321920048, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1321920045, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920048:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1321920069, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1321920045, "amt": 3.8, "curr": "BHD", "originalAmt": 3.8, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920069:*********", "paymentID": *********, "amt": 3.8, "approveCode": 0}]}, {"chargeID": 1321920047, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1321920045, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321920047:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1321920045, "codeType": "AIR", "amt": 26.5, "curr": "BHD", "originalAmt": 26.5, "originalCurr": "BHD", "status": 1, "billDate": "2025-04-27T06:27:29", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1321920045:*********", "paymentID": *********, "amt": 26.5, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1332024324, "codeType": "PMNT", "amt": 0.176, "curr": "BHD", "originalAmt": 0.176, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-05T03:38:03", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332024324:*********", "paymentID": *********, "amt": 0.176, "approveCode": 0}]}, {"chargeID": 1330994752, "codeType": "PMNT", "amt": 0.078, "curr": "BHD", "originalAmt": 0.078, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-04T05:40:29", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1330994752:*********", "paymentID": *********, "amt": 0.078, "approveCode": 0}]}, {"chargeID": 1321922023, "codeType": "PMNT", "amt": 2.55, "curr": "BHD", "originalAmt": 2.55, "originalCurr": "BHD", "status": 1, "billDate": "2025-04-27T06:28:08", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1321922023:*********", "paymentID": *********, "amt": 2.55, "approveCode": 0}]}, {"chargeID": 1330994046, "codeType": "NSST", "amt": 2.6, "curr": "BHD", "originalAmt": 2.6, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-04T05:39:09", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1330994046:*********", "paymentID": *********, "amt": 2.6, "approveCode": 0}], "PFID": "181053", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0, "ssrCommentId": "*********"}, {"chargeID": 1321920070, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1321920045, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1321920082, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 0, "billDate": "2025-04-27T06:27:29", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181053", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}], "parentPNRs": [], "childPNRs": []}