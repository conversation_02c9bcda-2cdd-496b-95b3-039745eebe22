{"seriesNum": "299", "PNR": "Q9ALGQ", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "83159330", "bookDate": "2025-05-27T01:32:49", "modifyDate": "2025-05-27T02:03:59", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "82fa90z5ke26j3u2k09a6402h54ca14da3104ca34355", "securityGUID": "82fa90z5ke26j3u2k09a6402h54ca14da3104ca34355", "lastLoadGUID": "e6e4544d-d224-44a6-813d-ff5728f50c4a", "isAsyncPNR": false, "MasterPNR": "Q9ALGQ", "segments": [{"segKey": "16087490:16087490:5/27/2025 9:15:00 AM", "LFID": 16087490, "depDate": "2025-05-27T00:00:00", "flightGroupId": "16087490", "org": "DXB", "dest": "SAW", "depTime": "2025-05-27T09:15:00", "depTimeGMT": "2025-05-27T05:15:00", "arrTime": "2025-05-27T13:05:00", "operCarrier": "FZ", "operFlightNum": "751", "mrktCarrier": "FZ", "mrktFlightNum": "751", "persons": [{"recNum": 2, "status": 5}, {"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181221, "depDate": "2025-05-27T09:15:00", "legKey": "16087490:181221:5/27/2025 9:15:00 AM", "customerKey": "21564266018777323ED1E1777E7D305FC3006FD4739FFD42D66EB4A18B41CA96"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 270261875, "fName": "THENJIWE", "lName": "MABOPE", "title": "MS", "PTCID": 1, "gender": "F", "FFNum": "623836953", "FFTier": "SILVER", "TierID": "4", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/27/2025 1:32:50 AM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "UO6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "KAFNG-DC833-INS", "insuTransID": "KAFNG-DC833-INS/cbd908b8-a42f-44fc-acfd-029c05ba82bd", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835156f000777000002457d#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-27T01:32:49"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "mohamad.merei", "statusReasonID": 0, "markFareClass": "Z", "insuPurchasedate": "5/27/2025 1:32:50 AM", "provider": "<PERSON>", "status": 5, "fareClass": "Z", "operFareClass": "Z", "FBC": "ZO9AE2", "fareBrand": "Business", "cabin": "BUSINESS", "emergencyContactID": 270262365, "discloseEmergencyContact": 1, "insuTransID": "KAFNG-DC833-INS/cbd908b8-a42f-44fc-acfd-029c05ba82bd", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68351b8a0007780000023362#270261875#1#ENT#SFQE#CHANGE", "fareTypeID": 25, "channelID": 1, "bookDate": "2025-05-27T01:56:21"}]}], "payments": [{"paymentID": *********, "paxID": 270262346, "method": "TABY", "status": "1", "paidDate": "2025-05-27T01:58:51", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 7425.6, "baseCurr": "AED", "baseAmt": 7425.6, "userID": "paybylink", "channelID": 2, "authCode": "A5438803", "reference": "A5438803", "externalReference": "A5438803", "tranId": "21649514", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FDAE", "exchangeRate": "1", "resExternalPaymentID": 21649514}, {"paymentID": *********, "paxID": 270261907, "method": "TABY", "status": "1", "paidDate": "2025-05-27T01:33:55", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 985.64, "baseCurr": "AED", "baseAmt": 985.64, "userID": "MOBILE_APP", "channelID": 12, "authCode": "A5438796", "reference": "A5438796", "externalReference": "A5438796", "tranId": "21649424", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FDAE", "exchangeRate": "1", "resExternalPaymentID": 21649424}], "OAFlights": null, "physicalFlights": [{"key": "16087490:181221:2025-05-27T09:15:00 AM", "LFID": 16087490, "PFID": 181221, "org": "DXB", "dest": "SAW", "depDate": "2025-05-27T09:15:00", "depTime": "2025-05-27T09:15:00", "arrTime": "2025-05-27T13:05:00", "carrier": "FZ", "flightNum": "751", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "751", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Sabiha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:57:08 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1363319831, "codeType": "INSU", "taxChargeID": 1363319725, "amt": -35.7, "curr": "AED", "originalAmt": -35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:56:22", "desc": "INSU", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315132, "paymentMap": [{"key": "1363319831:*********", "paymentID": *********, "amt": -35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}", "ChargeBookDate": "2025-05-27T01:56:22"}, {"chargeID": 1363315132, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315132:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"1\"\r\n}"}, {"chargeID": 1363319726, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363319725, "amt": -190, "curr": "AED", "originalAmt": -190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:56:21", "desc": "YQ - DUMMY", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315127, "paymentMap": [{"key": "1363319726:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1363319727, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363319725, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:56:22", "desc": "Passengers Security & Safety Service Fees", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315130, "paymentMap": [{"key": "1363319727:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1363319728, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363319725, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:56:22", "desc": "Passenger Service Charge (Intl)", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315128, "paymentMap": [{"key": "1363319728:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1363319829, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363319725, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:56:22", "desc": "Passenger Facilities Charge.", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315129, "paymentMap": [{"key": "1363319829:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1363319830, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363319725, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:56:22", "desc": "Advanced passenger information fee", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315126, "paymentMap": [{"key": "1363319830:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1363315127, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363315125, "amt": 190, "curr": "AED", "originalAmt": 190, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315127:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1363315130, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363315125, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315130:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363315129, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363315125, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315129:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363315126, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363315125, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315126:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363315128, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363315125, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315128:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363319725, "codeType": "AIR", "amt": -518, "curr": "AED", "originalAmt": -518, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T01:56:21", "desc": "WEB:AIR", "comment": "MD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1363315125, "paymentMap": [{"key": "1363319725:*********", "paymentID": *********, "amt": -518, "approveCode": 0}]}, {"chargeID": 1363315125, "codeType": "AIR", "amt": 518, "curr": "AED", "originalAmt": 518, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T01:32:49", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315125:*********", "paymentID": *********, "amt": 518, "approveCode": 0}]}, {"chargeID": 1363315345, "codeType": "PMNT", "amt": 46.94, "curr": "AED", "originalAmt": 46.94, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T01:33:59", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315345:*********", "paymentID": *********, "amt": 46.94, "approveCode": 0}]}, {"chargeID": 1363315131, "codeType": "BAGL", "amt": 65, "curr": "AED", "originalAmt": 65, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T01:32:49", "desc": "BAGL", "comment": "FLXID:GCC-AE DXB-TURKEYZONE/IRAQ/BEY:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363315131:*********", "paymentID": *********, "amt": 65, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1363319841, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:55:29", "billDate": "2025-05-27T01:56:22", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319841:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-27T01:56:22"}, {"chargeID": 1363319833, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1363319832, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319833:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1363319834, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1363319832, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319834:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363319835, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1363319832, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319835:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1363319836, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1363319832, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319836:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1363319837, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1363319832, "amt": 890, "curr": "AED", "originalAmt": 890, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319837:*********", "paymentID": *********, "amt": 890, "approveCode": 0}]}, {"chargeID": 1363319832, "codeType": "AIR", "amt": 6830, "curr": "AED", "originalAmt": 6830, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "FZ 751 DXB-SAW 27May2025 Tue 09:15 13:05\r\n", "reasonID": 2, "channelID": 1, "basePoints": 1870, "tierPoints": 1870, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 280, "PromoTier": 0, "paymentMap": [{"key": "1363319832:*********", "paymentID": *********, "amt": 5956.3, "approveCode": 0}, {"key": "1363319832:*********", "paymentID": *********, "amt": 873.7, "approveCode": 0}], "bonusMiles": 280, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363320058, "codeType": "PMNT", "amt": 353.6, "curr": "AED", "originalAmt": 353.6, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T01:58:56", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363320058:*********", "paymentID": *********, "amt": 353.6, "approveCode": 0}]}, {"chargeID": 1363319840, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1363319840:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1363319838, "codeType": "JBAG", "taxID": 6544, "taxCode": "JBAG", "taxChargeID": 1363319832, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "JBAG: 40kg Baggage allowance Business", "comment": "JBAG: 40kg Baggage allowance Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363319839, "codeType": "IFPJ", "taxID": 6545, "taxCode": "IFPJ", "taxChargeID": 1363319832, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T01:56:22", "billDate": "2025-05-27T01:56:22", "desc": "IFPJ: In flight entertainment Business", "comment": "IFPJ: In flight entertainment Business", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1363320953, "codeType": "MAAS", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T02:03:59", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181221", "ssrCommentId": "*********", "parameter1Name": "MAAS_REPORTING_DATE", "parameter1Value": "27-May-25", "parameter2Name": "MAAS_REPORTING_TIME", "parameter2Value": "07:00 - 07:30", "parameter3Name": "MAAS_REPORTING_OPTION", "parameter3Value": "OPTIN"}]}], "parentPNRs": [], "childPNRs": []}