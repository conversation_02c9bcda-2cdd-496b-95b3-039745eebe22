{"seriesNum": "299", "PNR": "WEC6DR", "bookAgent": "ellen.titto", "resCurrency": "AED", "PNRPin": "83231092", "bookDate": "2025-05-29T08:28:59", "modifyDate": "2025-05-29T08:36:13", "resType": "STANDARD", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "", "securityGUID": "", "lastLoadGUID": "36429570809915CCE0630A57380A107B", "isAsyncPNR": false, "MasterPNR": "WEC6DR", "segments": [{"segKey": "16776058:16776058:7/22/2025 8:55:00 PM", "LFID": 16776058, "depDate": "2025-07-22T00:00:00", "flightGroupId": "16776058", "org": "DXB", "dest": "OTP", "depTime": "2025-07-22T20:55:00", "depTimeGMT": "2025-07-22T16:55:00", "arrTime": "2025-07-23T01:20:00", "operCarrier": "FZ", "operFlightNum": "1795", "mrktCarrier": "FZ", "mrktFlightNum": "1795", "persons": [{"recNum": 1, "status": 1}], "legDetails": [{"PFID": 185758, "depDate": "2025-07-22T20:55:00", "legKey": "16776058:185758:7/22/2025 8:55:00 PM", "customerKey": "981EECFFE823737439D9866A3B65722B4EC8A6D9805A79D99E79BB1510D7F65C"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 270534097, "fName": "NICOLAE GEORGEL", "lName": "MARITA", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "ellen.titto", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/29/2025 8:29:00 AM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "FPBQT-X7TLS-INS", "insuTransID": "FPBQT-X7TLS-INS/bf579fd3-9682-45e0-86c9-e97b04cf9a32", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683819bc000778000000e25d#1#1#ENT#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-29T08:28:59"}]}], "payments": [{"paymentID": *********, "paxID": 270534097, "method": "VCHR", "status": "1", "paidDate": "2025-05-29T08:33:09", "voucherNum": 3266557, "paidCurr": "USD", "paidAmt": 150, "baseCurr": "AED", "baseAmt": 550.95, "userID": "ellen.titto", "channelID": 1, "voucherNumFull": "W1HNBL", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "exchangeRate": "0.27225701"}, {"paymentID": *********, "paxID": 270534983, "method": "TABY", "status": "1", "paidDate": "2025-05-29T08:34:47", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 881.74, "baseCurr": "AED", "baseAmt": 881.74, "userID": "paybylink", "channelID": 2, "authCode": "A5453289", "reference": "A5453289", "externalReference": "A5453289", "tranId": "21700213", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FDAE", "exchangeRate": "1", "resExternalPaymentID": 21700213}], "OAFlights": null, "physicalFlights": [{"key": "16776058:185758:2025-07-22T08:55:00 PM", "LFID": 16776058, "PFID": 185758, "org": "DXB", "dest": "OTP", "depDate": "2025-07-22T20:55:00", "depTime": "2025-07-22T20:55:00", "arrTime": "2025-07-23T01:20:00", "carrier": "FZ", "flightNum": "1795", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1795", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "OTP", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bucharest Henri <PERSON>", "isActive": true, "changeType": "AC", "flightChangeTime": "3/26/2025 11:02:43 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1367044394, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:26:11", "billDate": "2025-05-29T08:29:00", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044394:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"Currency\":\"USD\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-29T08:29:00"}, {"chargeID": 1367044387, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1367044385, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "AE: Passenger Service Charge (Intl)", "comment": "AE: Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044387:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1367044388, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1367044385, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044388:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1367044389, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1367044385, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "TP: Passengers Security & Safety Service Fees", "comment": "TP: Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044389:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367044390, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367044385, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044390:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367044391, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367044385, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044391:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1367044385, "codeType": "AIR", "amt": 885, "curr": "AED", "originalAmt": 885, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "FZ 1795 DXB-OTP 22Jul2025 Tue 20:55 01:20\r\n", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044385:*********", "paymentID": *********, "amt": 550.95, "approveCode": 0}, {"key": "1367044385:*********", "paymentID": *********, "amt": 334.05, "approveCode": 0}]}, {"chargeID": 1367055231, "codeType": "PMNT", "amt": 41.99, "curr": "AED", "originalAmt": 41.99, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T08:34:51", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367055231:*********", "paymentID": *********, "amt": 41.99, "approveCode": 0}]}, {"chargeID": 1367044386, "codeType": "CFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "CALL CENTRE ACCESS FEES", "comment": "CALL CENTRE ACCESS FEES", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367044386:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1367044392, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1367044385, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1367044393, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1367044385, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-29T08:28:59", "billDate": "2025-05-29T08:29:00", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 0, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "185758"}]}], "parentPNRs": [], "childPNRs": []}