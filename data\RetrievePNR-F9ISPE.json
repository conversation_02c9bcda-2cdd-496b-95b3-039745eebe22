{"seriesNum": "299", "PNR": "F9ISPE", "bookAgent": "WEB_MOBILE", "resCurrency": "AED", "PNRPin": "83105956", "bookDate": "2025-05-24T21:38:15", "modifyDate": "2025-05-27T13:20:50", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "34deu3412e566djfq6o5e71d4b2bb617c1619e9fbdb7", "securityGUID": "34deu3412e566djfq6o5e71d4b2bb617c1619e9fbdb7", "lastLoadGUID": "e1f18ee7-f372-4822-9766-b7f9a068375e", "isAsyncPNR": false, "MasterPNR": "F9ISPE", "segments": [{"segKey": "16087815:16087815:7/18/2025 5:50:00 AM", "LFID": 16087815, "depDate": "2025-07-18T00:00:00", "flightGroupId": "16087815", "org": "FRU", "dest": "DXB", "depTime": "2025-07-18T05:50:00", "depTimeGMT": "2025-07-17T23:50:00", "arrTime": "2025-07-18T08:10:00", "operCarrier": "FZ", "operFlightNum": "1690", "mrktCarrier": "FZ", "mrktFlightNum": "1690", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181591, "depDate": "2025-07-18T05:50:00", "legKey": "16087815:181591:7/18/2025 5:50:00 AM", "customerKey": "67A5D128F0A823120E8291B6F95D7429B23E9F3B3DDAB848B063E92ADE35A389"}], "active": true}, {"segKey": "16087793:16087793:7/7/2025 10:45:00 PM", "LFID": 16087793, "depDate": "2025-07-07T00:00:00", "flightGroupId": "16087793", "org": "DXB", "dest": "FRU", "depTime": "2025-07-07T22:45:00", "depTimeGMT": "2025-07-07T18:45:00", "arrTime": "2025-07-08T04:40:00", "operCarrier": "FZ", "operFlightNum": "1689", "mrktCarrier": "FZ ", "mrktFlightNum": "1689", "persons": [{"recNum": 1, "status": 1}], "legDetails": [{"PFID": 181530, "depDate": "2025-07-07T22:45:00", "legKey": "16087793:181530:7/7/2025 10:45:00 PM", "customerKey": "081564BD1671FCF503E53CE9D5919149F41CC17360BC0149DD715798B6FB3C77"}], "active": true}, {"segKey": "16087815:16087815:7/19/2025 5:50:00 AM", "LFID": 16087815, "depDate": "2025-07-19T00:00:00", "flightGroupId": "16087815", "org": "FRU", "dest": "DXB", "depTime": "2025-07-19T05:50:00", "depTimeGMT": "2025-07-18T23:50:00", "arrTime": "2025-07-19T08:10:00", "operCarrier": "FZ", "operFlightNum": "1690", "mrktCarrier": "FZ ", "mrktFlightNum": "1690", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181591, "depDate": "2025-07-19T05:50:00", "legKey": "16087815:181591:7/19/2025 5:50:00 AM", "customerKey": "5BFB77734A5C2BA93589C8D34E29755212C5F17176536E2DF8C28B3DCBD9A272"}], "active": true}], "persons": [{"paxID": 270056281, "fName": "OMAR", "lName": "ALZAROONI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/24/2025 9:38:15 PM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "Q4XDK-SAW9G-INS", "insuTransID": "Q4XDK-SAW9G-INS/51a02247-08f3-4382-a900-e532541d4a8a", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835ba4d0007780000003910#270056281#1#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-24T21:38:15"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "insuPurchasedate": "5/24/2025 9:38:15 PM", "provider": "<PERSON>", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "Q4XDK-SAW9G-INS", "insuTransID": "Q4XDK-SAW9G-INS/51a02247-08f3-4382-a900-e532541d4a8a", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6832348b000778000001aa42#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 1, "bookDate": "2025-05-24T21:38:15"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "fatemeh.hajebi", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/24/2025 9:38:15 PM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "URB7AE5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "Q4XDK-SAW9G-INS/51a02247-08f3-4382-a900-e532541d4a8a", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6835ba4d0007780000003910#270056281#2#ENT#SFQE#CHANGE", "fareTypeID": 22, "channelID": 1, "bookDate": "2025-05-27T13:17:08"}]}], "payments": [{"paymentID": *********, "paxID": 270056630, "method": "VISA", "status": "1", "paidDate": "2025-05-24T21:47:29", "cardNum": "************6342", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1679.05, "baseCurr": "AED", "baseAmt": 1679.05, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "authCode": "540556", "reference": "23230436", "externalReference": "23230436", "tranId": "21611366", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21611366}, {"paymentID": *********, "paxID": 270330535, "method": "VISA", "status": "1", "paidDate": "2025-05-27T13:20:45", "cardNum": "************6342", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 226.6, "baseCurr": "AED", "baseAmt": 226.6, "userID": "paybylink", "channelID": 2, "cardHolderName": "OMAR ZAROONI", "authCode": "185941", "reference": "23282183", "externalReference": "23282183", "tranId": "21662209", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21662209}, {"paymentID": 210173255, "paxID": 270056615, "method": "VISA", "status": "2", "paidDate": "2025-05-24T21:38:18", "cardNum": "************6342", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1679.05, "baseCurr": "AED", "baseAmt": 1679.05, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON>", "reference": "23230398", "externalReference": "23230398", "tranId": "21611366", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21611366}], "OAFlights": null, "physicalFlights": [{"key": "16087793:181530:2025-07-07T10:45:00 PM", "LFID": 16087793, "PFID": 181530, "org": "DXB", "dest": "FRU", "depDate": "2025-07-07T22:45:00", "depTime": "2025-07-07T22:45:00", "arrTime": "2025-07-08T04:40:00", "carrier": "FZ", "flightNum": "1689", "depTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1689", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "FRU", "operatingCarrier": "FZ", "flightDuration": 14100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bishkek", "isActive": true}, {"key": "16087815:181591:2025-07-18T05:50:00 AM", "LFID": 16087815, "PFID": 181591, "org": "FRU", "dest": "DXB", "depDate": "2025-07-18T05:50:00", "depTime": "2025-07-18T05:50:00", "arrTime": "2025-07-18T08:10:00", "carrier": "FZ", "flightNum": "1690", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1690", "flightStatus": "OPEN", "originMetroGroup": "FRU", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 15600, "reaccomChangeAlert": false, "originName": "Bishkek", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "16087815:181591:2025-07-19T05:50:00 AM", "LFID": 16087815, "PFID": 181591, "org": "FRU", "dest": "DXB", "depDate": "2025-07-19T05:50:00", "depTime": "2025-07-19T05:50:00", "arrTime": "2025-07-19T08:10:00", "carrier": "FZ", "flightNum": "1690", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1690", "flightStatus": "OPEN", "originMetroGroup": "FRU", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 15600, "reaccomChangeAlert": false, "originName": "Bishkek", "destinationName": "Dubai International Airport", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1364236002, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:12:54", "billDate": "2025-05-27T13:17:09", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236002:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-27T13:17:09"}, {"chargeID": 1364236003, "codeType": "INSU", "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562049, "paymentMap": [{"key": "1364236003:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1360562049, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562049:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1360562034, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1360562032, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562034:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1360562035, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1360562032, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562035:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1360562036, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1360562032, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562036:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1360562037, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1360562032, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562037:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1360562038, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1360562032, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562038:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1360562032, "codeType": "AIR", "amt": 365, "curr": "AED", "originalAmt": 365, "originalCurr": "AED", "status": 1, "billDate": "2025-05-24T21:38:15", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562032:*********", "paymentID": *********, "amt": 365, "approveCode": 0}]}, {"chargeID": 1364242652, "codeType": "PMNT", "amt": 6.6, "curr": "AED", "originalAmt": 6.6, "originalCurr": "AED", "status": 1, "billDate": "2025-05-27T13:20:49", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364242652:*********", "paymentID": *********, "amt": 6.6, "approveCode": 0}]}, {"chargeID": 1360567161, "codeType": "PMNT", "amt": 48.9, "curr": "AED", "originalAmt": 48.9, "originalCurr": "AED", "status": 1, "billDate": "2025-05-24T21:47:37", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360567161:*********", "paymentID": *********, "amt": 48.9, "approveCode": 0}]}, {"chargeID": 1360562033, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1360562032, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1360562048, "codeType": "GFML", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "GFML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181530"}]}, {"recNum": 2, "charges": [{"chargeID": 1364236008, "codeType": "INSU", "taxChargeID": 1364236004, "amt": -22.58, "curr": "AED", "originalAmt": -22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562051, "paymentMap": [{"key": "1364236008:*********", "paymentID": *********, "amt": -22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-27T13:17:09"}, {"chargeID": 1360562051, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562051:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1364236005, "codeType": "TAX", "taxID": 12491, "taxCode": "Q3", "taxChargeID": 1364236004, "amt": -50, "curr": "AED", "originalAmt": -50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "Passenger Terminal Use Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562045, "paymentMap": [{"key": "1364236005:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1364236006, "codeType": "TAX", "taxID": 8685, "taxCode": "HL", "taxChargeID": 1364236004, "amt": -60, "curr": "AED", "originalAmt": -60, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "Airport Development Tax", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562046, "paymentMap": [{"key": "1364236006:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1364236007, "codeType": "TAX", "taxID": 12489, "taxCode": "KG", "taxChargeID": 1364236004, "amt": -50, "curr": "AED", "originalAmt": -50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "Passenger Service Charge", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562044, "paymentMap": [{"key": "1364236007:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1364236010, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364236004, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562042, "paymentMap": [{"key": "1364236010:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1364236011, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364236004, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562043, "paymentMap": [{"key": "1364236011:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1360562043, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1360562040, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562043:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1360562042, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1360562040, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562042:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1360562044, "codeType": "TAX", "taxID": 12489, "taxCode": "KG", "taxChargeID": 1360562040, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562044:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1360562046, "codeType": "TAX", "taxID": 8685, "taxCode": "HL", "taxChargeID": 1360562040, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Airport Development Tax", "comment": "Airport Development Tax", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562046:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1360562045, "codeType": "TAX", "taxID": 12491, "taxCode": "Q3", "taxChargeID": 1360562040, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "Passenger Terminal Use Charge", "comment": "Passenger Terminal Use Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562045:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364236004, "codeType": "AIR", "amt": -365, "curr": "AED", "originalAmt": -365, "originalCurr": "AED", "status": 0, "billDate": "2025-05-27T13:17:09", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562040, "paymentMap": [{"key": "1364236004:*********", "paymentID": *********, "amt": -365, "approveCode": 0}]}, {"chargeID": 1360562040, "codeType": "AIR", "amt": 365, "curr": "AED", "originalAmt": 365, "originalCurr": "AED", "status": 0, "billDate": "2025-05-24T21:38:15", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1360562040:*********", "paymentID": *********, "amt": 365, "approveCode": 0}]}, {"chargeID": 1364236014, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "CancelNoRefund FZ 1690 FRU - DXB 19.07.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236014:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1364236013, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364236004, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562041, "paymentMap": []}, {"chargeID": 1360562041, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1360562040, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364236009, "codeType": "GFML", "taxChargeID": 1364236004, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-27T13:17:09", "desc": "GFML", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1360562050, "paymentMap": [], "PFID": "181591"}, {"chargeID": 1360562050, "codeType": "GFML", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-24T21:38:15", "desc": "GFML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181591"}]}, {"recNum": 3, "charges": [{"chargeID": 1364236024, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:12:55", "billDate": "2025-05-27T13:17:09", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236024:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"0.27\",\"Premium\":\"12.29\",\"Tax\":\"0.59\",\"Currency\":\"USD\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-27T13:17:09"}, {"chargeID": 1364236017, "codeType": "TAX", "taxID": 12491, "taxCode": "Q3", "taxChargeID": 1364236016, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "Q3: Passenger Terminal Use Charge", "comment": "Q3: Passenger Terminal Use Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236017:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364236018, "codeType": "TAX", "taxID": 8685, "taxCode": "HL", "taxChargeID": 1364236016, "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "HL: Airport Development Tax", "comment": "HL: Airport Development Tax", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236018:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1364236019, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1364236016, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236019:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1364236020, "codeType": "TAX", "taxID": 12489, "taxCode": "KG", "taxChargeID": 1364236016, "amt": 50, "curr": "AED", "originalAmt": 50, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "KG: Passenger Service Charge", "comment": "KG: Passenger Service Charge", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236020:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1364236021, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1364236016, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236021:*********", "paymentID": *********, "amt": 114.85, "approveCode": 0}, {"key": "1364236021:*********", "paymentID": *********, "amt": 165.15, "approveCode": 0}]}, {"chargeID": 1364236016, "codeType": "AIR", "amt": 375, "curr": "AED", "originalAmt": 375, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "FZ 1690 FRU-DXB 18Jul2025 Fri 05:50 08:10\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236016:*********", "paymentID": *********, "amt": 375, "approveCode": 0}]}, {"chargeID": 1364236023, "codeType": "MFEE", "amt": 60, "curr": "AED", "originalAmt": 60, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1364236023:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1364236022, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1364236016, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:17:09", "billDate": "2025-05-27T13:17:09", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1364236026, "codeType": "GFML", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-27T13:12:55", "billDate": "2025-05-27T13:17:09", "desc": "GFML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181591"}]}], "parentPNRs": [], "childPNRs": []}