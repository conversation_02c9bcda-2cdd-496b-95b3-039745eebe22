{"seriesNum": "299", "PNR": "7SBVHX", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82856993", "bookDate": "2025-05-15T13:31:51", "modifyDate": "2025-05-15T13:32:56", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "981b11c749ea40131a69vb5376u8i4ka622dsd4d995f", "securityGUID": "981b11c749ea40131a69vb5376u8i4ka622dsd4d995f", "lastLoadGUID": "d0bdd4b8-**************-ed561b74d9eb", "isAsyncPNR": false, "MasterPNR": "7SBVHX", "segments": [{"segKey": "16066157:16066157:5/28/2025 10:00:00 AM", "LFID": 16066157, "depDate": "2025-05-28T00:00:00", "flightGroupId": "16066157", "org": "DOH", "dest": "DXB", "depTime": "2025-05-28T10:00:00", "depTimeGMT": "2025-05-28T07:00:00", "arrTime": "2025-05-28T12:15:00", "operCarrier": "FZ", "operFlightNum": "002", "mrktCarrier": "FZ ", "mrktFlightNum": "002", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 180998, "depDate": "2025-05-28T10:00:00", "legKey": "16066157:180998:5/28/2025 10:00:00 AM", "customerKey": "1231692FAACCD8501C41968F862E8B823FA90E722334B11807EB7489A527218E"}], "active": true, "changeType": "AC"}, {"segKey": "16066156:16066156:5/29/2025 9:35:00 AM", "LFID": 16066156, "depDate": "2025-05-29T00:00:00", "flightGroupId": "16066156", "org": "DXB", "dest": "DOH", "depTime": "2025-05-29T09:35:00", "depTimeGMT": "2025-05-29T05:35:00", "arrTime": "2025-05-29T09:55:00", "operCarrier": "FZ", "operFlightNum": "003", "mrktCarrier": "FZ ", "mrktFlightNum": "003", "persons": [{"recNum": 2, "status": 4}], "legDetails": [{"PFID": 180997, "depDate": "2025-05-29T09:35:00", "legKey": "16066156:180997:5/29/2025 9:35:00 AM", "customerKey": "1534927286CB1546129633A53FDA0F9F44AAAB877FB1DD221C93D86D5CE94A34"}], "active": true}], "persons": [{"paxID": 269089860, "fName": "ESTHER", "lName": "RUITER", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/15/2025 1:31:51 PM", "provider": "<PERSON>", "status": 5, "fareClass": "Q", "operFareClass": "Q", "FBC": "QR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "emergencyContactID": 270281881, "discloseEmergencyContact": 1, "insuTransID": "LQJCE-VP4SK-INS/0253df8a-4a08-4bef-9296-a712d04d2030", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6825eb2c00077800000065b3#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 2, "bookDate": "2025-05-15T13:31:51"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "Q", "insuPurchasedate": "5/15/2025 1:31:51 PM", "provider": "<PERSON>", "status": 4, "fareClass": "Q", "operFareClass": "Q", "FBC": "QR6QA2", "fareBrand": "Lite", "cabin": "ECONOMY", "emergencyContactID": 270281882, "discloseEmergencyContact": 1, "insuTransID": "LQJCE-VP4SK-INS/0253df8a-4a08-4bef-9296-a712d04d2030", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6825eb2c00077800000065b3#1#2#WEB#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 2, "bookDate": "2025-05-15T13:31:51"}]}], "payments": [{"paymentID": *********, "paxID": 269090079, "method": "MSCD", "status": "1", "paidDate": "2025-05-15T13:32:51", "cardNum": "************4113", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 298.73, "baseCurr": "QAR", "baseAmt": 1066.51, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "631740", "reference": "23048993", "externalReference": "23048993", "tranId": "21428333", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgmccecomusdallniall001", "exchangeRate": "0.28010435", "resExternalPaymentID": 21428333}], "OAFlights": null, "physicalFlights": [{"key": "16066157:180998:2025-05-28T10:00:00 AM", "LFID": 16066157, "PFID": 180998, "org": "DOH", "dest": "DXB", "depDate": "2025-05-28T10:00:00", "depTime": "2025-05-28T10:00:00", "arrTime": "2025-05-28T12:15:00", "carrier": "FZ", "flightNum": "002", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "002", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "5/13/2025 9:06:05 AM"}, {"key": "16066156:180997:2025-05-29T09:35:00 AM", "LFID": 16066156, "PFID": 180997, "org": "DXB", "dest": "DOH", "depDate": "2025-05-29T09:35:00", "depTime": "2025-05-29T09:35:00", "arrTime": "2025-05-29T09:55:00", "carrier": "FZ", "flightNum": "003", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "003", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1347724221, "codeType": "INSU", "amt": 17.73, "curr": "QAR", "originalAmt": 17.73, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724221:*********", "paymentID": *********, "amt": 17.73, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-15T13:31:50"}, {"chargeID": 1347724208, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347724207, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724208:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347724209, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1347724207, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724209:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1347724210, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1347724207, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724210:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1347724213, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1347724207, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724213:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347724212, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1347724207, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724212:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347724211, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347724207, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724211:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1347724207, "codeType": "AIR", "amt": 305, "curr": "QAR", "originalAmt": 305, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-15T13:31:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724207:*********", "paymentID": *********, "amt": 305, "approveCode": 0}]}, {"chargeID": 1347734135, "codeType": "PMNT", "amt": 31.06, "curr": "QAR", "originalAmt": 31.06, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-15T13:32:55", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347734135:*********", "paymentID": *********, "amt": 31.06, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1347724222, "codeType": "INSU", "amt": 17.72, "curr": "QAR", "originalAmt": 17.72, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724222:*********", "paymentID": *********, "amt": 17.72, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-15T13:31:50"}, {"chargeID": 1347724216, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1347724214, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724216:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1347724220, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1347724214, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724220:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347724217, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1347724214, "amt": 80, "curr": "QAR", "originalAmt": 80, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724217:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1347724219, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1347724214, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724219:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1347724218, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1347724214, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724218:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347724215, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1347724214, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-15T13:31:51", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724215:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1347724214, "codeType": "AIR", "amt": 305, "curr": "QAR", "originalAmt": 305, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-15T13:31:51", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1347724214:*********", "paymentID": *********, "amt": 305, "approveCode": 0}]}]}], "parentPNRs": [], "childPNRs": []}