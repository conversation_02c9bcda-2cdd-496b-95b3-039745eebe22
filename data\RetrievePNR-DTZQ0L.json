{"seriesNum": "299", "PNR": "DTZQ0L", "bookAgent": "WEB2_LIVE", "resCurrency": "BHD", "PNRPin": "83222128", "bookDate": "2025-05-28T23:33:57", "modifyDate": "2025-05-31T18:39:49", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 2, "activeSegCount": 2, "webBookingID": "75cf4d455148435c95d9ba5r09fdu776b662d7ja4f81", "securityGUID": "75cf4d455148435c95d9ba5r09fdu776b662d7ja4f81", "lastLoadGUID": "a32cbb3e-afe7-45fa-ade1-c7c95a19fb16", "isAsyncPNR": false, "MasterPNR": "DTZQ0L", "segments": [{"segKey": "16094073:16094073:6/16/2025 1:40:00 AM", "LFID": 16094073, "depDate": "2025-06-16T00:00:00", "flightGroupId": "16094073", "org": "BAH", "dest": "TBS", "depTime": "2025-06-16T01:40:00", "depTimeGMT": "2025-06-15T22:40:00", "arrTime": "2025-06-16T08:40:00", "operCarrier": "FZ", "operFlightNum": "030/8625", "mrktCarrier": "FZ", "mrktFlightNum": "030/8625", "persons": [{"recNum": 5, "status": 1}, {"recNum": 7, "status": 1}], "legDetails": [{"PFID": 181050, "depDate": "2025-06-16T01:40:00", "legKey": "16094073:181050:6/16/2025 1:40:00 AM", "customerKey": "337C1C7230C347553CA8F909D2A3DEA7C327CB3D82C4297ED92F1D90BEF2505B"}, {"PFID": 181625, "depDate": "2025-06-16T05:15:00", "legKey": "16094073:181625:6/16/2025 5:15:00 AM", "customerKey": "066704569746E273C6F5132C24FEEB63AFB0950BF08133CCF4E0B2F3B5FE4927"}], "active": true, "changeType": "TK"}, {"segKey": "16155479:16155479:6/19/2025 4:35:00 PM", "LFID": 16155479, "depDate": "2025-06-19T00:00:00", "flightGroupId": "16155479", "org": "TBS", "dest": "BAH", "depTime": "2025-06-19T16:35:00", "depTimeGMT": "2025-06-19T12:35:00", "arrTime": "2025-06-19T21:00:00", "operCarrier": "FZ", "operFlightNum": "712/025", "mrktCarrier": "FZ", "mrktFlightNum": "712/025", "persons": [{"recNum": 6, "status": 1}, {"recNum": 8, "status": 1}], "legDetails": [{"PFID": 181237, "depDate": "2025-06-19T16:35:00", "legKey": "16155479:181237:6/19/2025 4:35:00 PM", "customerKey": "B5227A416F012472875240F9DA16DAC2E9D01F89E9815D369C7338DE708B4B45"}, {"PFID": 181044, "depDate": "2025-06-19T20:45:00", "legKey": "16155479:181044:6/19/2025 8:45:00 PM", "customerKey": "0F601E48873B9A82D7707C18F18999EE900B0A9EB3511549FFE0D62BE06A9A92"}], "active": true, "changeType": "AC"}, {"segKey": "17115094:17115094:6/2/2025 1:40:00 AM", "LFID": 17115094, "depDate": "2025-06-02T00:00:00", "flightGroupId": "17115094", "org": "BAH", "dest": "TBS", "depTime": "2025-06-02T01:40:00", "depTimeGMT": "2025-06-01T22:40:00", "arrTime": "2025-06-02T09:55:00", "operCarrier": "FZ", "operFlightNum": "030/8625", "mrktCarrier": "FZ ", "mrktFlightNum": "030/8625", "persons": [{"recNum": 1, "status": 0}, {"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181050, "depDate": "2025-06-02T01:40:00", "legKey": "17115094:181050:6/2/2025 1:40:00 AM", "customerKey": "1E89F3A17768CB0759AD651AC14ECE7F148F56BB66269CE084D3D6960C64B021"}, {"PFID": 187899, "depDate": "2025-06-02T06:30:00", "legKey": "17115094:187899:6/2/2025 6:30:00 AM", "customerKey": "1B2D89703789081B876A11C1231F6A1DDF82ED67F5D8811E7D4288FF054BC9D0"}], "active": true}, {"segKey": "16155479:16155479:6/5/2025 4:35:00 PM", "LFID": 16155479, "depDate": "2025-06-05T00:00:00", "flightGroupId": "16155479", "org": "TBS", "dest": "BAH", "depTime": "2025-06-05T16:35:00", "depTimeGMT": "2025-06-05T12:35:00", "arrTime": "2025-06-05T21:00:00", "operCarrier": "FZ", "operFlightNum": "712/025", "mrktCarrier": "FZ ", "mrktFlightNum": "712/025", "persons": [{"recNum": 4, "status": 0}, {"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181237, "depDate": "2025-06-05T16:35:00", "legKey": "16155479:181237:6/5/2025 4:35:00 PM", "customerKey": "871A30F6EAE6AEF5EDB441BC86ECBFAF405D86061ADE65CB5C4B1F9AF005E73A"}, {"PFID": 181044, "depDate": "2025-06-05T20:45:00", "legKey": "16155479:181044:6/5/2025 8:45:00 PM", "customerKey": "F9476895FF565276E6DD3F5E266AF8D70543EC9BC12B93B3BD91C58BA325553E"}], "active": true, "changeType": "XL"}], "persons": [{"paxID": 270499119, "fName": "RAVICHANDRAN", "lName": "VENUGOPAL", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 4, 5, 6]}, {"paxID": 270499120, "fName": "LOGESH", "lName": "RAVICHANDRAN", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [2, 3, 7, 8]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "NRD6D-3PCZ6-INS", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683795de00077700000093b8#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-28T23:33:57"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "NRD6D-3PCZ6-INS", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "toRecNum": 7, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683795de00077700000093b8#2#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-28T23:33:57"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "NRD6D-3PCZ6-INS", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "toRecNum": 8, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683795de00077700000093b8#2#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-28T23:33:57"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "NRD6D-3PCZ6-INS", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683795de00077700000093b8#1#2#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 1, "bookDate": "2025-05-28T23:33:57"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "mai.gabir", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b4ae000077800000163c8#270499119#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-31T18:33:47"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "mai.gabir", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683b4ae000077800000163c8#270499119#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-31T18:33:47"}]}, {"recNum": 7, "recordDetails": [{"bookAgent": "mai.gabir", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683b4ae000077800000163c8#270499120#1#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-31T18:33:47"}]}, {"recNum": 8, "recordDetails": [{"bookAgent": "mai.gabir", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/28/2025 11:33:57 PM", "provider": "<PERSON>", "status": 1, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7BH5", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "NRD6D-3PCZ6-INS/33316cf5-4866-43df-a5da-f326b0554063", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683b4ae000077800000163c8#270499120#2#ENT#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 1, "bookDate": "2025-05-31T18:33:47"}]}], "payments": [{"paymentID": *********, "paxID": 270499131, "method": "VISA", "status": "1", "paidDate": "2025-05-28T23:35:13", "cardNum": "************2069", "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 328.076, "baseCurr": "BHD", "baseAmt": 328.076, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "RAVICHANDRAN V", "authCode": "969788", "reference": "23311102", "externalReference": "23311102", "tranId": "21693756", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEBHD", "exchangeRate": "1", "resExternalPaymentID": 21693756}, {"paymentID": *********, "paxID": 270807706, "method": "VISA", "status": "1", "paidDate": "2025-05-31T18:39:46", "cardNum": "************2069", "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 93.347, "baseCurr": "BHD", "baseAmt": 93.347, "userID": "paybylink", "channelID": 2, "cardHolderName": "RAVICHANDRAN V", "authCode": "041705", "reference": "23369263", "externalReference": "23369263", "tranId": "21752743", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEBHD", "exchangeRate": "1", "resExternalPaymentID": 21752743}], "OAFlights": null, "physicalFlights": [{"key": "17115094:181050:2025-06-02T01:40:00 AM", "LFID": 17115094, "PFID": 181050, "org": "BAH", "dest": "DXB", "depDate": "2025-06-02T01:40:00", "depTime": "2025-06-02T01:40:00", "arrTime": "2025-06-02T03:55:00", "carrier": "FZ", "flightNum": "030", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "030", "flightStatus": "CLOSED", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "17115094:187899:2025-06-02T06:30:00 AM", "LFID": 17115094, "PFID": 187899, "org": "DXB", "dest": "TBS", "depDate": "2025-06-02T06:30:00", "depTime": "2025-06-02T06:30:00", "arrTime": "2025-06-02T09:55:00", "carrier": "FZ", "flightNum": "8625", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "8625", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": false}, {"key": "16155479:181237:2025-06-05T04:35:00 PM", "LFID": 16155479, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-06-05T16:35:00", "depTime": "2025-06-05T16:35:00", "arrTime": "2025-06-05T19:50:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "OPEN", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11700, "reaccomChangeAlert": true, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "XL", "flightChangeTime": "6/2/2025 6:01:52 AM"}, {"key": "16155479:181044:2025-06-05T08:45:00 PM", "LFID": 16155479, "PFID": 181044, "org": "DXB", "dest": "BAH", "depDate": "2025-06-05T20:45:00", "depTime": "2025-06-05T20:45:00", "arrTime": "2025-06-05T21:00:00", "carrier": "FZ", "flightNum": "025", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "025", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "BAH", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": true, "originName": "Dubai International Airport", "destinationName": "Bahrain", "isActive": true, "changeType": "XL", "flightChangeTime": "6/2/2025 6:01:52 AM"}, {"key": "16094073:181050:2025-06-16T01:40:00 AM", "LFID": 16094073, "PFID": 181050, "org": "BAH", "dest": "DXB", "depDate": "2025-06-16T01:40:00", "depTime": "2025-06-16T01:40:00", "arrTime": "2025-06-16T03:55:00", "carrier": "FZ", "flightNum": "030", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "030", "flightStatus": "OPEN", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": true, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "5/8/2025 12:38:00 PM"}, {"key": "16094073:181625:2025-06-16T05:15:00 AM", "LFID": 16094073, "PFID": 181625, "org": "DXB", "dest": "TBS", "depDate": "2025-06-16T05:15:00", "depTime": "2025-06-16T05:15:00", "arrTime": "2025-06-16T08:40:00", "carrier": "FZ", "flightNum": "8625", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "8625", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "TBS", "operatingCarrier": "FZ", "flightDuration": 12300, "reaccomChangeAlert": true, "originName": "Dubai International Airport", "destinationName": "Tbilisi", "isActive": true, "changeType": "TK", "flightChangeTime": "5/8/2025 12:38:00 PM"}, {"key": "16155479:181237:2025-06-19T04:35:00 PM", "LFID": 16155479, "PFID": 181237, "org": "TBS", "dest": "DXB", "depDate": "2025-06-19T16:35:00", "depTime": "2025-06-19T16:35:00", "arrTime": "2025-06-19T19:45:00", "carrier": "FZ", "flightNum": "712", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "712", "flightStatus": "OPEN", "originMetroGroup": "TBS", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 11400, "reaccomChangeAlert": false, "originName": "Tbilisi", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "5/6/2025 8:44:34 AM"}, {"key": "16155479:181044:2025-06-19T08:45:00 PM", "LFID": 16155479, "PFID": 181044, "org": "DXB", "dest": "BAH", "depDate": "2025-06-19T20:45:00", "depTime": "2025-06-19T20:45:00", "arrTime": "2025-06-19T21:00:00", "carrier": "FZ", "flightNum": "025", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "025", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "BAH", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Bahrain", "isActive": true, "changeType": "AC", "flightChangeTime": "5/6/2025 8:44:34 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1370745082, "codeType": "INSU", "taxChargeID": 1370745075, "amt": -1.83, "curr": "BHD", "originalAmt": -1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592885, "paymentMap": [{"key": "1370745082:*********", "paymentID": *********, "amt": -1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-31T18:33:47"}, {"chargeID": 1366592885, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592885:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1370745077, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745075, "amt": -0.6, "curr": "BHD", "originalAmt": -0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592855, "paymentMap": [{"key": "1370745077:*********", "paymentID": *********, "amt": -0.6, "approveCode": 0}]}, {"chargeID": 1370745078, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745075, "amt": -4.6, "curr": "BHD", "originalAmt": -4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592854, "paymentMap": [{"key": "1370745078:*********", "paymentID": *********, "amt": -4.6, "approveCode": 0}]}, {"chargeID": 1370745079, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745075, "amt": -18.9, "curr": "BHD", "originalAmt": -18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592856, "paymentMap": [{"key": "1370745079:*********", "paymentID": *********, "amt": -18.9, "approveCode": 0}]}, {"chargeID": 1370745081, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1370745075, "amt": -0.3, "curr": "BHD", "originalAmt": -0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "Passenger Service Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592852, "paymentMap": [{"key": "1370745081:*********", "paymentID": *********, "amt": -0.3, "approveCode": 0}]}, {"chargeID": 1370745091, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1370745075, "amt": -10, "curr": "BHD", "originalAmt": -10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "Passenger Service Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592853, "paymentMap": [{"key": "1370745091:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1366592855, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366592851, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592855:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1366592854, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366592851, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592854:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1366592856, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366592851, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592856:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1366592853, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1366592851, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592853:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366592852, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1366592851, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592852:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}]}, {"chargeID": 1370745075, "codeType": "AIR", "amt": -42.5, "curr": "BHD", "originalAmt": -42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-31T18:33:47", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592851, "paymentMap": [{"key": "1370745075:*********", "paymentID": *********, "amt": -42.5, "approveCode": 0}]}, {"chargeID": 1366592851, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-28T23:33:57", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592851:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}]}, {"chargeID": 1366599080, "codeType": "PMNT", "amt": 9.556, "curr": "BHD", "originalAmt": 9.556, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-28T23:35:15", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366599080:*********", "paymentID": *********, "amt": 9.556, "approveCode": 0}]}, {"chargeID": 1370745092, "codeType": "PNLT", "amt": 16, "curr": "BHD", "originalAmt": 16, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:47", "billDate": "2025-05-31T18:33:47", "desc": "CancelNoRefund FZ 030/8625 BAH - TBS 02.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745092:*********", "paymentID": *********, "amt": 16, "approveCode": 0}]}, {"chargeID": 1370745076, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745075, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592857, "paymentMap": []}, {"chargeID": 1366592857, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366592851, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745080, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745075, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592896, "paymentMap": [], "PFID": "181050"}, {"chargeID": 1370745083, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745075, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:47", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592895, "paymentMap": [], "PFID": "187899"}, {"chargeID": 1366592896, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181050"}, {"chargeID": 1366592895, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187899"}]}, {"recNum": 4, "charges": [{"chargeID": 1370745094, "codeType": "INSU", "taxChargeID": 1370745093, "amt": -1.83, "curr": "BHD", "originalAmt": -1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592888, "paymentMap": [{"key": "1370745094:*********", "paymentID": *********, "amt": -1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-31T18:33:48"}, {"chargeID": 1366592888, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592888:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1370745095, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745093, "amt": -18.9, "curr": "BHD", "originalAmt": -18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592862, "paymentMap": [{"key": "1370745095:*********", "paymentID": *********, "amt": -18.9, "approveCode": 0}]}, {"chargeID": 1370745096, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745093, "amt": -0.6, "curr": "BHD", "originalAmt": -0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592861, "paymentMap": [{"key": "1370745096:*********", "paymentID": *********, "amt": -0.6, "approveCode": 0}]}, {"chargeID": 1370745098, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745093, "amt": -4.6, "curr": "BHD", "originalAmt": -4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592860, "paymentMap": [{"key": "1370745098:*********", "paymentID": *********, "amt": -4.6, "approveCode": 0}]}, {"chargeID": 1370745101, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1370745093, "amt": -2.7, "curr": "BHD", "originalAmt": -2.7, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592863, "paymentMap": [{"key": "1370745101:*********", "paymentID": *********, "amt": -2.7, "approveCode": 0}]}, {"chargeID": 1370745102, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1370745093, "amt": -9.4, "curr": "BHD", "originalAmt": -9.4, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592864, "paymentMap": [{"key": "1370745102:*********", "paymentID": *********, "amt": -9.4, "approveCode": 0}]}, {"chargeID": 1366592864, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1366592859, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592864:*********", "paymentID": *********, "amt": 9.4, "approveCode": 0}]}, {"chargeID": 1366592863, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1366592859, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592863:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0}]}, {"chargeID": 1366592860, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366592859, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592860:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1366592861, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366592859, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592861:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1366592862, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366592859, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592862:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1370745093, "codeType": "AIR", "amt": -42.5, "curr": "BHD", "originalAmt": -42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-31T18:33:47", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592859, "paymentMap": [{"key": "1370745093:*********", "paymentID": *********, "amt": -42.5, "approveCode": 0}]}, {"chargeID": 1366592859, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-28T23:33:57", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592859:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}]}, {"chargeID": 1370745103, "codeType": "PNLT", "amt": 16, "curr": "BHD", "originalAmt": 16, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "CancelNoRefund FZ 712/025 TBS - BAH 05.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745103:*********", "paymentID": *********, "amt": 16, "approveCode": 0}]}, {"chargeID": 1370745097, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745093, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592865, "paymentMap": []}, {"chargeID": 1366592865, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366592859, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745099, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745093, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592897, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1370745100, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745093, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592898, "paymentMap": [], "PFID": "181044"}, {"chargeID": 1366592898, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181044"}, {"chargeID": 1366592897, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}]}, {"recNum": 5, "charges": [{"chargeID": 1370745114, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:31:00", "billDate": "2025-05-31T18:33:48", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745114:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"2.65\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-31T18:33:48"}, {"chargeID": 1370745105, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1370745104, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "HM: Passenger Service Fee", "comment": "HM: Passenger Service Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745105:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}]}, {"chargeID": 1370745106, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1370745104, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "BH: Passenger Service Fee", "comment": "BH: Passenger Service Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745106:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1370745107, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745104, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745107:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1370745108, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745104, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745108:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1370745109, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745104, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745109:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1370745104, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "FZ 030 BAH-DXB 16Jun2025 Mon 01:40 03:55\r\nFZ 8625 DXB-TBS 16Jun2025 Mon 05:15 08:40\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745104:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}]}, {"chargeID": 1370750599, "codeType": "PMNT", "amt": 2.719, "curr": "BHD", "originalAmt": 2.719, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-31T18:39:49", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370750599:*********", "paymentID": *********, "amt": 2.719, "approveCode": 0}]}, {"chargeID": 1370745113, "codeType": "MFEE", "amt": 6.157, "curr": "BHD", "originalAmt": 6.157, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745113:*********", "paymentID": *********, "amt": 6.157, "approveCode": 0}]}, {"chargeID": 1370745110, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745104, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745111, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745104, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181050"}, {"chargeID": 1370745112, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745104, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181625"}]}, {"recNum": 6, "charges": [{"chargeID": 1370745126, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:31:00", "billDate": "2025-05-31T18:33:48", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745126:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"2.65\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-31T18:33:48"}, {"chargeID": 1370745116, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1370745115, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745116:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0}]}, {"chargeID": 1370745117, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1370745115, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745117:*********", "paymentID": *********, "amt": 7.07, "approveCode": 0}, {"key": "1370745117:*********", "paymentID": *********, "amt": 2.33, "approveCode": 0}]}, {"chargeID": 1370745118, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745115, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745118:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1370745119, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745115, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745119:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1370745120, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745115, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745120:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1370745115, "codeType": "AIR", "amt": 43.5, "curr": "BHD", "originalAmt": 43.5, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "FZ 712 TBS-DXB 19Jun2025 Thu 16:35 19:45\r\nFZ 025 DXB-BAH 19Jun2025 Thu 20:45 21:00\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745115:*********", "paymentID": *********, "amt": 43.5, "approveCode": 0}]}, {"chargeID": 1370745125, "codeType": "MFEE", "amt": 6.157, "curr": "BHD", "originalAmt": 6.157, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745125:*********", "paymentID": *********, "amt": 6.157, "approveCode": 0}]}, {"chargeID": 1370745121, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745115, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745122, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745115, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1370745123, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745115, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181044"}]}, {"recNum": 2, "charges": [{"chargeID": 1370745129, "codeType": "INSU", "taxChargeID": 1370745127, "amt": -1.83, "curr": "BHD", "originalAmt": -1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592891, "paymentMap": [{"key": "1370745129:*********", "paymentID": *********, "amt": -1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-31T18:33:48"}, {"chargeID": 1366592891, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592891:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1370745128, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745127, "amt": -0.6, "curr": "BHD", "originalAmt": -0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592871, "paymentMap": [{"key": "1370745128:*********", "paymentID": *********, "amt": -0.6, "approveCode": 0}]}, {"chargeID": 1370745132, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745127, "amt": -4.6, "curr": "BHD", "originalAmt": -4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592870, "paymentMap": [{"key": "1370745132:*********", "paymentID": *********, "amt": -4.6, "approveCode": 0}]}, {"chargeID": 1370745133, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1370745127, "amt": -0.3, "curr": "BHD", "originalAmt": -0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Passenger Service Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592868, "paymentMap": [{"key": "1370745133:*********", "paymentID": *********, "amt": -0.3, "approveCode": 0}]}, {"chargeID": 1370745135, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1370745127, "amt": -10, "curr": "BHD", "originalAmt": -10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Passenger Service Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592869, "paymentMap": [{"key": "1370745135:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1370745136, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745127, "amt": -18.9, "curr": "BHD", "originalAmt": -18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592872, "paymentMap": [{"key": "1370745136:*********", "paymentID": *********, "amt": -18.9, "approveCode": 0}]}, {"chargeID": 1366592870, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366592867, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592870:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1366592871, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366592867, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592871:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1366592868, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1366592867, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592868:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}]}, {"chargeID": 1366592869, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1366592867, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592869:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1366592872, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366592867, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592872:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1370745127, "codeType": "AIR", "amt": -42.5, "curr": "BHD", "originalAmt": -42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-31T18:33:48", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592867, "paymentMap": [{"key": "1370745127:*********", "paymentID": *********, "amt": -42.5, "approveCode": 0}]}, {"chargeID": 1366592867, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-28T23:33:57", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592867:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}]}, {"chargeID": 1370745137, "codeType": "PNLT", "amt": 16, "curr": "BHD", "originalAmt": 16, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:48", "billDate": "2025-05-31T18:33:48", "desc": "CancelNoRefund FZ 030/8625 BAH - TBS 02.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745137:*********", "paymentID": *********, "amt": 16, "approveCode": 0}]}, {"chargeID": 1370745131, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745127, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592873, "paymentMap": []}, {"chargeID": 1366592873, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366592867, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745130, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745127, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592899, "paymentMap": [], "PFID": "187899"}, {"chargeID": 1370745134, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745127, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:48", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592900, "paymentMap": [], "PFID": "181050"}, {"chargeID": 1366592899, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "187899"}, {"chargeID": 1366592900, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181050"}]}, {"recNum": 3, "charges": [{"chargeID": 1370745145, "codeType": "INSU", "taxChargeID": 1370745138, "amt": -1.83, "curr": "BHD", "originalAmt": -1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "INSU", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592894, "paymentMap": [{"key": "1370745145:*********", "paymentID": *********, "amt": -1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}", "ChargeBookDate": "2025-05-31T18:33:49"}, {"chargeID": 1366592894, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592894:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"2.65\",\r\n  \"Premium\": \"19.44\",\r\n  \"Tax\": \"0.93\",\r\n  \"SegPaxCount\": \"4\"\r\n}"}, {"chargeID": 1370745141, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745138, "amt": -0.6, "curr": "BHD", "originalAmt": -0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "Advanced passenger information fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592877, "paymentMap": [{"key": "1370745141:*********", "paymentID": *********, "amt": -0.6, "approveCode": 0}]}, {"chargeID": 1370745143, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1370745138, "amt": -2.7, "curr": "BHD", "originalAmt": -2.7, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "Airport Passenger Security Fee (International)", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592879, "paymentMap": [{"key": "1370745143:*********", "paymentID": *********, "amt": -2.7, "approveCode": 0}]}, {"chargeID": 1370745144, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745138, "amt": -18.9, "curr": "BHD", "originalAmt": -18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "YQ - DUMMY", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592878, "paymentMap": [{"key": "1370745144:*********", "paymentID": *********, "amt": -18.9, "approveCode": 0}]}, {"chargeID": 1370745147, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1370745138, "amt": -9.4, "curr": "BHD", "originalAmt": -9.4, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "Passenger Fee", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592880, "paymentMap": [{"key": "1370745147:*********", "paymentID": *********, "amt": -9.4, "approveCode": 0}]}, {"chargeID": 1370745148, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745138, "amt": -4.6, "curr": "BHD", "originalAmt": -4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "Passenger Facilities Charge.", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592876, "paymentMap": [{"key": "1370745148:*********", "paymentID": *********, "amt": -4.6, "approveCode": 0}]}, {"chargeID": 1366592880, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1366592875, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Fee", "comment": "Passenger Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592880:*********", "paymentID": *********, "amt": 9.4, "approveCode": 0}]}, {"chargeID": 1366592876, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1366592875, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592876:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1366592878, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1366592875, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592878:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1366592879, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1366592875, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Airport Passenger Security Fee (International)", "comment": "Airport Passenger Security Fee (International)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592879:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0}]}, {"chargeID": 1366592877, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1366592875, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592877:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1370745138, "codeType": "AIR", "amt": -42.5, "curr": "BHD", "originalAmt": -42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-31T18:33:48", "desc": "WEB:AIR", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592875, "paymentMap": [{"key": "1370745138:*********", "paymentID": *********, "amt": -42.5, "approveCode": 0}]}, {"chargeID": 1366592875, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-28T23:33:57", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1366592875:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}]}, {"chargeID": 1370745149, "codeType": "PNLT", "amt": 16, "curr": "BHD", "originalAmt": 16, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "CancelNoRefund FZ 712/025 TBS - BAH 05.06.2025", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745149:*********", "paymentID": *********, "amt": 16, "approveCode": 0}]}, {"chargeID": 1370745146, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745138, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "20kg BAG INCLUDED IN FARE", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592881, "paymentMap": []}, {"chargeID": 1366592881, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1366592875, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745140, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745138, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592902, "paymentMap": [], "PFID": "181044"}, {"chargeID": 1370745142, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745138, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-31T18:33:49", "desc": "Standard meal", "comment": "MOD No Refund", "reasonID": 1, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1366592901, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1366592901, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1366592902, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-28T23:33:57", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181044"}]}, {"recNum": 7, "charges": [{"chargeID": 1370745160, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:31:00", "billDate": "2025-05-31T18:33:49", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745160:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"2.65\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-31T18:33:49"}, {"chargeID": 1370745151, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1370745150, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "HM: Passenger Service Fee", "comment": "HM: Passenger Service Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745151:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}]}, {"chargeID": 1370745152, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1370745150, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "BH: Passenger Service Fee", "comment": "BH: Passenger Service Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745152:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1370745153, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745150, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745153:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1370745154, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745150, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745154:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1370745155, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745150, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745155:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1370745150, "codeType": "AIR", "amt": 42.5, "curr": "BHD", "originalAmt": 42.5, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "FZ 030 BAH-DXB 16Jun2025 Mon 01:40 03:55\r\nFZ 8625 DXB-TBS 16Jun2025 Mon 05:15 08:40\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745150:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}]}, {"chargeID": 1370745159, "codeType": "MFEE", "amt": 6.157, "curr": "BHD", "originalAmt": 6.157, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745159:*********", "paymentID": *********, "amt": 6.157, "approveCode": 0}]}, {"chargeID": 1370745156, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745150, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745157, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745150, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181050"}, {"chargeID": 1370745158, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745150, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181625"}]}, {"recNum": 8, "charges": [{"chargeID": 1370745171, "codeType": "INSU", "amt": 1.83, "curr": "BHD", "originalAmt": 1.83, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:31:00", "billDate": "2025-05-31T18:33:49", "desc": "Special Service Request", "comment": "Insurance SSR", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745171:*********", "paymentID": *********, "amt": 1.83, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Fx\":\"2.65\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"Currency\":\"USD\",\"SegPaxCount\":\"4\"}", "ChargeBookDate": "2025-05-31T18:33:49"}, {"chargeID": 1370745162, "codeType": "TAX", "taxID": 11913, "taxCode": "JA", "taxChargeID": 1370745161, "amt": 2.7, "curr": "BHD", "originalAmt": 2.7, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "JA: Airport Passenger Security Fee (International)", "comment": "JA: Airport Passenger Security Fee (International)", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745162:*********", "paymentID": *********, "amt": 2.7, "approveCode": 0}]}, {"chargeID": 1370745163, "codeType": "TAX", "taxID": 10586, "taxCode": "GE", "taxChargeID": 1370745161, "amt": 9.4, "curr": "BHD", "originalAmt": 9.4, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "GE: Passenger Fee", "comment": "GE: Passenger Fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745163:*********", "paymentID": *********, "amt": 7.07, "approveCode": 0}, {"key": "1370745163:*********", "paymentID": *********, "amt": 2.33, "approveCode": 0}]}, {"chargeID": 1370745164, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1370745161, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "F6: Passenger Facilities Charge.", "comment": "F6: Passenger Facilities Charge.", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745164:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1370745165, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1370745161, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "ZR: Advanced passenger information fee", "comment": "ZR: Advanced passenger information fee", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745165:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1370745166, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1370745161, "amt": 18.9, "curr": "BHD", "originalAmt": 18.9, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "YQ: YQ - DUMMY", "comment": "YQ: YQ - DUMMY", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745166:*********", "paymentID": *********, "amt": 18.9, "approveCode": 0}]}, {"chargeID": 1370745161, "codeType": "AIR", "amt": 43.5, "curr": "BHD", "originalAmt": 43.5, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "FZ 712 TBS-DXB 19Jun2025 Thu 16:35 19:45\r\nFZ 025 DXB-BAH 19Jun2025 Thu 20:45 21:00\r\n", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745161:*********", "paymentID": *********, "amt": 43.5, "approveCode": 0}]}, {"chargeID": 1370745170, "codeType": "MFEE", "amt": 6.157, "curr": "BHD", "originalAmt": 6.157, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "Special Service Request", "comment": "CALL CENTRE MODIFICATION FEES – AUTO", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1370745170:*********", "paymentID": *********, "amt": 6.157, "approveCode": 0}]}, {"chargeID": 1370745167, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1370745161, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "BAGB: 20kg BAG INCLUDED IN FARE", "comment": "BAGB: 20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1370745168, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745161, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181237"}, {"chargeID": 1370745169, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "taxChargeID": 1370745161, "amt": 0, "curr": "BHD", "originalAmt": 0, "originalCurr": "BHD", "status": 1, "exchRate": 1, "exchRateDate": "2025-05-31T18:33:49", "billDate": "2025-05-31T18:33:49", "desc": "MLIN: Standard meal", "comment": "MLIN: Standard meal", "reasonID": 2, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181044"}]}], "parentPNRs": [], "childPNRs": []}